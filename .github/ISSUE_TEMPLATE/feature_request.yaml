name: Feature request
description: Suggest an idea for this project
labels: ["feature request"]
body:
  - type: dropdown
    id: component
    attributes:
      label: Component(s)
      description: Which component(s) does your feature request concern?
      multiple: true
      options:
      # NOTE: The list below is autogenerated using `make generate-gh-issue-templates`
      # Do not manually edit it.
      # Start components list
      - cmd/builder
      - cmd/mdatagen
      - cmd/mdatagen/internal/sampleprocessor
      - cmd/mdatagen/internal/samplereceiver
      - cmd/mdatagen/internal/samplescraper
      - confmap
      - confmap/provider/envprovider
      - confmap/provider/fileprovider
      - confmap/provider/httpprovider
      - confmap/provider/httpsprovider
      - confmap/provider/yamlprovider
      - connector/forward
      - connector/x
      - consumer/xconsumer
      - docs/rfcs
      - exporter/debug
      - exporter/exporterhelper
      - exporter/exporterhelper/xexporterhelper
      - exporter/exporterqueue
      - exporter/nop
      - exporter/otlp
      - exporter/otlphttp
      - exporter/x
      - extension/memorylimiter
      - extension/x
      - extension/x/storage
      - extension/zpages
      - pdata
      - pdata/pprofile
      - processor/batch
      - processor/memorylimiter
      - processor/processorhelper
      - processor/x
      - receiver/nop
      - receiver/otlp
      - receiver/receiverhelper
      - receiver/x
      - scraper
      - scraper/scraperhelper
      - service
      - service/internal/graph
      # End components list
  - type: textarea
    attributes:
      label: Is your feature request related to a problem? Please describe.
      description: A clear and concise description of what the problem is. Ex. I'm always frustrated when [...]. We are currently preparing for the upcoming 1.0 GA release. Feature requests that are not aligned with the current roadmap and are not aimed at stabilizing and preparing the Collector for the release will
  not be prioritized.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Describe the solution you'd like
      description: A clear and concise description of what you want to happen.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Describe alternatives you've considered
      description: A clear and concise description of any alternative solutions or features you've considered.
  - type: textarea
    attributes:
      label: Additional context
      description: Add any other context or screenshots about the feature request here.
