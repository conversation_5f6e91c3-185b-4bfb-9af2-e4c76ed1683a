name: Bug report
description: Create a report to help us improve
labels: ["bug"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report! Please make sure to fill out the entire form below, providing as much context as you can in order to help us triage and track down your bug as quickly as possible.

        Before filing a bug, please be sure you have searched through [existing bugs](https://github.com/open-telemetry/opentelemetry-collector/issues?q=is%3Aissue+is%3Aopen+sort%3Aupdated-desc+label%3Abug) to see if an existing issue covers your bug.
  - type: dropdown
    id: component
    attributes:
      label: Component(s)
      description: Which component(s) does your bug report concern?
      multiple: true
      options:
      # NOTE: The list below is autogenerated using `make generate-gh-issue-templates`
      # Do not manually edit it.
      # Start components list
      - cmd/builder
      - cmd/mdatagen
      - cmd/mdatagen/internal/sampleprocessor
      - cmd/mdatagen/internal/samplereceiver
      - cmd/mdatagen/internal/samplescraper
      - confmap
      - confmap/provider/envprovider
      - confmap/provider/fileprovider
      - confmap/provider/httpprovider
      - confmap/provider/httpsprovider
      - confmap/provider/yamlprovider
      - connector/forward
      - connector/x
      - consumer/xconsumer
      - docs/rfcs
      - exporter/debug
      - exporter/exporterhelper
      - exporter/exporterhelper/xexporterhelper
      - exporter/exporterqueue
      - exporter/nop
      - exporter/otlp
      - exporter/otlphttp
      - exporter/x
      - extension/memorylimiter
      - extension/x
      - extension/x/storage
      - extension/zpages
      - pdata
      - pdata/pprofile
      - processor/batch
      - processor/memorylimiter
      - processor/processorhelper
      - processor/x
      - receiver/nop
      - receiver/otlp
      - receiver/receiverhelper
      - receiver/x
      - scraper
      - scraper/scraperhelper
      - service
      - service/internal/graph
      # End components list
  - type: textarea
    attributes:
      label: What happened?
      description: Please provide as much detail as you reasonably can.
      value: |
        **Describe the bug**
        <!-- A clear and concise description of what the bug is. -->
        
        **Steps to reproduce**
        <!-- If possible, provide a recipe for reproducing the error. -->
        
        **What did you expect to see?**
        <!-- A clear and concise description of what you expected to see. -->
        
        **What did you see instead?**
        <!-- A clear and concise description of what you saw instead. -->

    validations:
      required: true
  - type: input
    attributes:
      label: Collector version
      description: What version did you use? (e.g., `v0.4.0`, `1eb551b`, etc)
    validations:
      required: true
  - type: textarea
    attributes:
      label: Environment information
      description: Please provide any additional information about your installation.
      value: |
        ## Environment
        OS: (e.g., "Ubuntu 20.04")
        Compiler(if manually compiled): (e.g., "go 14.2")

  - type: textarea
    attributes:
      label: OpenTelemetry Collector configuration
      description: Please provide the configuration you are using (e.g. the YAML config file).
      placeholder: | # Empty Collector config
        receivers:
        exporters:
        processors:
        extensions:
        service:
          pipelines:
            traces:
              receivers: []
              exporters: []
              processors: []
            metrics:
              receivers: []
              exporters: []
              processors: []
            logs:
              receivers: []
              exporters: []
              processors: []
      render: yaml
  - type: textarea
    attributes:
      label: Log output
      description: |
        Please copy and paste any relevant log output.
      render: shell
  - type: textarea
    attributes:
      label: Additional context
      description: Any additional information you think may be relevant to this issue.

