name: Other issue
description: Create a new issue to help us improve the collector
body:
  - type: dropdown
    id: component
    attributes:
      label: Component(s)
      description: Which component(s) does your issue concern?
      multiple: true
      options:
      # NOTE: The list below is autogenerated using `make generate-gh-issue-templates`
      # Do not manually edit it.
      # Start components list
      - cmd/builder
      - cmd/mdatagen
      - cmd/mdatagen/internal/sampleprocessor
      - cmd/mdatagen/internal/samplereceiver
      - cmd/mdatagen/internal/samplescraper
      - confmap
      - confmap/provider/envprovider
      - confmap/provider/fileprovider
      - confmap/provider/httpprovider
      - confmap/provider/httpsprovider
      - confmap/provider/yamlprovider
      - connector/forward
      - connector/x
      - consumer/xconsumer
      - docs/rfcs
      - exporter/debug
      - exporter/exporterhelper
      - exporter/exporterhelper/xexporterhelper
      - exporter/exporterqueue
      - exporter/nop
      - exporter/otlp
      - exporter/otlphttp
      - exporter/x
      - extension/memorylimiter
      - extension/x
      - extension/x/storage
      - extension/zpages
      - pdata
      - pdata/pprofile
      - processor/batch
      - processor/memorylimiter
      - processor/processorhelper
      - processor/x
      - receiver/nop
      - receiver/otlp
      - receiver/receiverhelper
      - receiver/x
      - scraper
      - scraper/scraperhelper
      - service
      - service/internal/graph
      # End components list
  - type: textarea
    attributes:
      label: Describe the issue you're reporting
      description: A clear and concise description of what the problem is. Ex. I'm always frustrated when [...]
    validations:
      required: true
