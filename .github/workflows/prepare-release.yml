name: Automation - Prepare Release

on:
  workflow_dispatch:
    # Determine the version number that will be assigned to the release. During the beta phase, we increment
    # the minor version number and set the patch number to 0.
    inputs:
      candidate-stable:
        description: Release candidate version (stable, like 1.3.0). Don't include a leading `v`.

      current-stable:
        required: true
        description: Current version (stable, like 1.2.0). Don't include a leading `v`.

      candidate-beta:
        description: Release candidate version (beta, like 0.96.0). Don't include `v`.

      current-beta:
        required: true
        description: Current version (beta, like 0.95.1). Don't include `v`.

permissions: read-all

jobs:
  validate-versions-format:
    runs-on: ubuntu-latest

    steps:
      - name: Validate version format
        shell: bash
        run: |
          validate_beta_version() {
            local regex_pattern_beta='^[0-9]+\.[0-9]+\.[0-9]+$'
            if [[ ! "$1" =~ $regex_pattern_beta ]]; then
              echo "Invalid $2 version format. For beta, it can be 0.1.0 or higher"
              exit 1
            fi
          }

          validate_stable_version() {
            local regex_pattern_stable='^[1-9][0-9]*\.[0-9]+\.[0-9]+$'
            if [[ ! "$1" =~ $regex_pattern_stable ]]; then
              echo "Invalid stable version format for $2. Major version must be greater than 1."
              exit 1
            fi
          }

          if [[ ! -z "${{ inputs.candidate-beta }}" ]]; then
            validate_beta_version "${{ inputs.candidate-beta }}" "candidate-beta"
          fi
          validate_beta_version "${{ inputs.current-beta }}" "current-beta"

          if [[ ! -z "${{ inputs.candidate-stable }}" ]]; then
            validate_stable_version "${{ inputs.candidate-stable }}" "candidate-stable"
          fi
          validate_stable_version "${{ inputs.current-stable }}" "current-stable"

          if [[ -z "${{ inputs.candidate-beta }}" && -z "${{ inputs.candidate-stable }}" ]]; then
            echo "Candidate version is not set for beta or stable. Please set a version to proceed."
            exit 1
          fi

  check-blockers:
    needs:
      - validate-versions-format
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
      # Make sure that there are no open issues with release:blocker label in Core. The release has to be delayed until they are resolved.
      - name: Check blockers in core
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          REPO: open-telemetry/opentelemetry-collector
        run: ./.github/workflows/scripts/release-check-blockers.sh
      # Make sure that there are no open issues with release:blocker label in Contrib. The release has to be delayed until they are resolved.
      - name: Check blockers in contrib
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          REPO: open-telemetry/opentelemetry-collector-contrib
        run: ./.github/workflows/scripts/release-check-blockers.sh
      # Make sure the current main branch build successfully passes (Core).
      - name: Check build status in core
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          REPO: open-telemetry/opentelemetry-collector
        run: ./.github/workflows/scripts/release-check-build-status.sh
      # Make sure the current main branch build successfully passes (Contrib).
      - name: Check build status in contrib
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          REPO: open-telemetry/opentelemetry-collector-contrib
        run: ./.github/workflows/scripts/release-check-build-status.sh

  create-release-issue:
    needs:
      - check-blockers
    runs-on: ubuntu-latest
    permissions:
      issues: write
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
      # To keep track of the progress, it might be helpful to create a tracking issue similar to #6067. You are responsible
      # for all of the steps under the "Performed by collector release manager" heading. Once the issue is created, you can
      # create the individual ones by hovering them and clicking the "Convert to issue" button on the right hand side.
      - name: Create issue for tracking release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          CANDIDATE_BETA: ${{ inputs.candidate-beta }}
          CANDIDATE_STABLE: ${{ inputs.candidate-stable }}
          CURRENT_BETA: ${{ inputs.current-beta }}
          CURRENT_STABLE: ${{ inputs.current-stable }}
          REPO: open-telemetry/opentelemetry-collector
        run: ./.github/workflows/scripts/release-create-tracking-issue.sh

  # Releasing opentelemetry-collector
  prepare-release:
    needs:
      - check-blockers
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
      - name: Setup Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: 1.23.10
      # Prepare Core for release.
      #   - Update CHANGELOG.md file, this is done via chloggen
      #   - Run make prepare-release PREVIOUS_VERSION=1.0.0 RELEASE_CANDIDATE=1.1.0 MODSET=stable
      #   - Run make prepare-release PREVIOUS_VERSION=0.52.0 RELEASE_CANDIDATE=0.53.0 MODSET=beta
      - name: Prepare release for core
        env:
          GITHUB_TOKEN: ${{ secrets.OPENTELEMETRYBOT_GITHUB_TOKEN }}
          REPO: open-telemetry/opentelemetry-collector
          CANDIDATE_BETA: ${{ inputs.candidate-beta }}
          CANDIDATE_STABLE: ${{ inputs.candidate-stable }}
          CURRENT_BETA: ${{ inputs.current-beta }}
          CURRENT_STABLE: ${{ inputs.current-stable }}
        run: ./.github/workflows/scripts/release-prepare-release.sh
