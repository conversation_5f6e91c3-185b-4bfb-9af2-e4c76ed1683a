name: contrib-tests
on:
  push:
    branches: [main]
    tags:
      - v[0-9]+.[0-9]+.[0-9]+.*
  pull_request:
    types: [opened, ready_for_review, synchronize, reopened, labeled, unlabeled]
    branches: [main]
  merge_group:
    types: [checks_requested]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref_name }}
  cancel-in-progress: true

permissions: read-all

jobs:
  contrib-tests-prepare:
    runs-on: ubuntu-latest
    if: ${{ !contains(github.event.pull_request.labels.*.name, 'Skip Contrib Tests') }}
    steps:
      - name: Checkout Repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Prepare Contrib Tests
        run: |
          contrib_path=/tmp/opentelemetry-collector-contrib
          git clone --depth=1 https://github.com/open-telemetry/opentelemetry-collector-contrib.git $contrib_path
          make CONTRIB_PATH=$contrib_path prepare-contrib
      - uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: contrib
          path: /tmp/opentelemetry-collector-contrib/
          include-hidden-files: true

  contrib-tests-matrix:
    runs-on: ubuntu-latest
    needs: [contrib-tests-prepare]
    if: ${{ !contains(github.event.pull_request.labels.*.name, 'Skip Contrib Tests') }}
    strategy:
      fail-fast: false
      matrix:
        group:
          - receiver-0
          - receiver-1
          - receiver-2
          - receiver-3
          - processor
          - exporter-0
          - exporter-1
          - extension
          - connector
          - internal
          - pkg
          - cmd-0
          - other
    steps:
      - name: Checkout Repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Download contrib
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          name: contrib
          path: /tmp/contrib
      - name: Setup Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: 1.23.10
          cache: false
      - name: Run tests
        run: |
          chmod +x /tmp/contrib/.tools/*
          make CONTRIB_PATH=/tmp/contrib SKIP_RESTORE_CONTRIB=true GROUP=${{ matrix.group }} check-contrib

  contrib_tests:
    runs-on: ubuntu-latest
    if: ${{ !contains(github.event.pull_request.labels.*.name, 'Skip Contrib Tests') }}
    needs: [contrib-tests-matrix]
    steps:
      - name: Print result
        run: echo ${{ needs.contrib-tests-matrix.result }}
      - name: Interpret result
        run: |
          if [[ success == ${{ needs.contrib-tests-matrix.result }} ]]
          then
            echo "All matrix jobs passed!"
          else
            echo "One or more matrix jobs failed."
            false
          fi
