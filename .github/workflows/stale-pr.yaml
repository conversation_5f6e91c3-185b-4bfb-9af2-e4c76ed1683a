name: "Close stale pull requests"
on:
  schedule:
    - cron: "12 3 * * *" # arbitrary time not to DDOS GitHub

permissions: read-all

jobs:
  stale:
    permissions:
      issues: write # for actions/stale to close stale issues
      pull-requests: write # for actions/stale to close stale PRs
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@5bef64f19d7facfb25b37b414482c7164d639639 # v9.1.0
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          stale-pr-message: "This PR was marked stale due to lack of activity. It will be closed in 14 days."
          close-pr-message: "Closed as inactive. Feel free to reopen if this PR is still being worked on."
          days-before-pr-stale: 14
          days-before-issue-stale: 730
          days-before-pr-close: 14
          days-before-issue-close: 30
