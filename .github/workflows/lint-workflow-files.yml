name: Lint G<PERSON>Hub Workflow YAML Files

on:
  pull_request:
    paths:
      - '.github/workflows/*.yml'
      - '.github/workflows/*.yaml'

jobs:
  lint:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5
        with:
          go-version: '1.24'

      - name: Install Actionlint
        run: |
          mkdir -p .tools
          cd internal/tools
          go install github.com/rhysd/actionlint/cmd/actionlint@v1.7.7
          mv $(go env GOPATH)/bin/actionlint ../../.tools/actionlint

      - name: Run Actionlint
        run: |
          make actionlint

      - name: Reminder to Address Linting Errors
        if: failure()
        run: echo "⚠️ Please address all linting errors before merging this pull request."

      - name: All linting checks passed
        if: success()
        run: echo "✅ All linting checks passed."
