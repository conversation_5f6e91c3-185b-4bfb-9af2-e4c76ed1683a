name: Automation - Performance

on:
  push:
    branches: [main]

permissions: read-all

jobs:
  runperf:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Setup Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: 1.23.10

      - name: Run benchmark
        run: make gobenchmark

      # Disabling until fine-grained permissions token enabled for the
      # repository
      #- name: Store benchmark result
      #  uses: benchmark-action/github-action-benchmark@v1
      #  with:
      #    tool: 'go'
      #    output-file-path: benchmarks.txt
      #    gh-pages-branch: gh-pages
      #    auto-push: true
      #    github-token: ${{ secrets.GITHUB_TOKEN }}
      #    benchmark-data-dir-path: "docs/dev/bench"
