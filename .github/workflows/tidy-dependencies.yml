name: "Project: Tidy"
on:
  pull_request_target:
    types: [opened, ready_for_review, synchronize, reopened, labeled, unlabeled]
    branches:
      - main

permissions: read-all

jobs:
  setup-environment:
    permissions:
      contents: write # for Git to git push
    timeout-minutes: 30
    runs-on: ubuntu-latest
    if: ${{ !contains(github.event.pull_request.labels.*.name, 'dependency-major-update') && (github.actor == 'renovate[bot]' || contains(github.event.pull_request.labels.*.name, 'renovatebot')) }}
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: ${{ github.head_ref }}
      - uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: 1.23.10
          cache: false
      - name: Cache Go
        id: go-cache
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: |
            ~/go/bin
            ~/go/pkg/mod
          key: go-cache-${{ runner.os }}-${{ hashFiles('**/go.sum') }}
      - name: Install dependencies
        if: steps.go-cache.outputs.cache-hit != 'true'
        run: make -j2 gomoddownload
      - name: Install Tools
        if: steps.go-cache.outputs.cache-hit != 'true'
        run: make install-tools
      - name: go mod tidy
        run: |
          make gotidy
          git config user.name opentelemetrybot
          git config user.email <EMAIL>
          echo "git diff --exit-code || (git add . && git commit -m \"go mod tidy\" && git push)"
          git diff --exit-code || (git add . && git commit -m "go mod tidy" && git push)
      - uses: actions-ecosystem/action-remove-labels@2ce5d41b4b6aa8503e285553f75ed56e0a40bae0 # v1.3.0
        with:
          labels: renovatebot
