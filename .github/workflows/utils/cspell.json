{"version": "0.2", "language": "en", "words": ["Alolita", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Appy", "atombender", "Backpressure", "Baeyens", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "CHACHA", "CODEOWNERS", "<PERSON>", "Confmap", "DOLLARSIGN", "Distro", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Dynatrace", "Excalidraw", "Expvar", "Fanout", "Funcs", "GHSA", "GOARCH", "GOBIN", "GOCMD", "GOMEMLIMIT", "GOPATH", "GOPROXY", "Guiton", "<PERSON><PERSON>", "Hostfeature", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Keepalive", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MODSET", "Map<PERSON>rov<PERSON>", "Marshalers", "Marshall<PERSON>", "<PERSON><PERSON>", "Mirabella", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "OTEP", "Olly", "<PERSON><PERSON><PERSON>", "Paixão", "Pdata", "Prometheusremotewrite", "Punya", "RCPC", "<PERSON><PERSON>", "SASL", "Samplingdecision", "<PERSON>", "Statefulness", "<PERSON><PERSON><PERSON>", "Tailsampling", "Tigran", "<PERSON><PERSON><PERSON>", "Triagers", "Unconfigured", "Unmarshable", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>mar<PERSON>llers", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "alibabacloudlogserviceexporter", "alives", "<PERSON><PERSON><PERSON>", "atoulme", "attributeprocessor", "attributesprocessor", "authextension", "authtest", "backoffs", "backpressure", "ballastextension", "batchprocessor", "bearertokenauthextension", "behaviour", "bog<PERSON><PERSON><PERSON><PERSON>", "bucketize", "buildinfo", "bwalk", "capabilityconsumer", "certfile", "cgroupv", "cheung", "chlog", "chloggen", "cmux", "codeboten", "codeowners", "componenterror", "componenthelper", "componentprofiles", "componentstatus", "componenttest", "configauth", "configcheck", "configcompression", "configcompressions", "configerror", "configgrpc", "confighttp", "configloader", "configma<PERSON><PERSON><PERSON>", "configmiddleware", "configmodels", "confignet", "configopaque", "configoptional", "configparser", "configretry", "configrpc", "configsource", "configtelemetry", "configtest", "configtls", "confi<PERSON><PERSON><PERSON><PERSON>", "confmap", "confmaptest", "connectorprofiles", "connectortest", "consumerdata", "consumererror", "consumererrorprofiles", "consumerfanout", "consumerhelper", "consumerprofiles", "consumertest", "consumetest", "conv", "cookiejar", "coreinternal", "cpus", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crosslink", "cumulativetodeltaprocessor", "customname", "dataloss", "datapoints", "debugexporter", "defaultcomponents", "distro", "distros", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "dokey", "<PERSON>v<PERSON><PERSON><PERSON><PERSON>", "envp<PERSON><PERSON>", "exampleexporter", "examplereceiver", "expandconverter", "expandmapconverter", "expandvar", "exporterbatch", "exporterbatcher", "exporterhelper", "exporterhelperprofiles", "exporterprofiles", "exporterqueue", "exportertest", "exporthelper", "expvar", "extensionauth", "extensionauthtest", "extensioncapabilities", "extensionmiddleware", "extensionmiddlewaretest", "extensionhelper", "extensiontest", "extensionz", "fanout", "fanoutconsumer", "featureflags", "featuregate", "featuregates", "featurez", "fieldalignment", "fileexporter", "filemapprovider", "fileprovider", "filterprocessor", "filterset", "fluentbit", "fluentforward", "forwardconnector", "fsnotify", "funcs", "gcflags", "genpdata", "genproto", "godoc", "gofmt", "<PERSON><PERSON><PERSON>", "goldendataset", "goleak", "golint", "gomod", "goproxy", "goreleaser", "goroutines", "gotidy", "groupbyattrprocessor", "groupbytrace", "groupbytraceprocessor", "grpclb", "guiton", "healthcheck", "healthcheckextension", "healthcheckv", "hostcapabilities", "hostmetrics", "hostmetricsreceiver", "httpclientconfig", "httpprovider", "httpsprovider", "httptest", "illumos", "incorrectclass", "incorrectcomponent", "incorrectstability", "instrgen", "internaldata", "ints", "invalidaggregation", "invalidtype", "iruntime", "jaegerexporter", "jaegerreceiver", "jmacd", "jpkrohling", "jsoniter", "kafkaexporter", "kafkaexporter's", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keepalive", "k<PERSON><PERSON>", "ldflags", "limitermiddleware", "localhostgate", "loggingexporter", "logstest", "mapconverter", "mapstructure", "marshalers", "mdatagen", "mdatagen's", "memorylimiter", "memorylimiterextension", "memorylimiterprocessor", "metadatatest", "metricfamily", "metricreceiver", "metricsexporter", "metricsgenerationprocessor", "metricstransformprocessor", "middleware", "<PERSON><PERSON><PERSON>", "muehle", "multiclient", "multimod", "mycert", "myconnector", "myexporter", "myextension", "myorg", "myprocessor", "myreceiver", "my<PERSON><PERSON>", "mysite", "nonclobbering", "nopexporter", "nopreceiver", "nosuchprocessor", "notls", "obsreceiver", "obsreport", "obsreporttest", "oidcauthextension", "oltp", "okey", "omitempty", "omnition", "opencensus", "opencensusexporter", "opencensusreceiver", "otelcol", "otelcoltest", "otelcorecol", "otelsvc", "oteltest", "otelzap", "otlpexporter", "otlpgrpc", "otlphttp", "otlphttpexporter", "otlphttpexporter's", "otlphttpreceiver", "o<PERSON><PERSON><PERSON><PERSON>", "otlpmetrics", "otlpreceiver", "otlptext", "overwritepropertiesconverter", "overwritepropertiesmapconverter", "parserp<PERSON><PERSON>", "pcommon", "pdata", "pdatagen", "pdatagrpc", "perfcounters", "perflib", "pipelineprofiles", "pipelinez", "<PERSON><PERSON><PERSON><PERSON>", "plog", "plogotlp", "plogs", "pmetric", "pmetricotlp", "policyevaluation", "pprof", "pprofextension", "pprofile", "pprofileotlp", "preconfigured", "priya", "probabilisticsamplerprocessor", "processorhelper", "processorhelperprofiles", "processorprofiles", "processortest", "processscraper", "proctelemetry", "prometheusexporter", "prometheusreceiver", "prometheusremotewrite", "prometheusremotewriteexporter", "protogen", "protos", "ptraceotlp", "queuebatch", "receiverhelper", "receiverprofiles", "receivertest", "resourcedetection", "resourcedetectionprocessor", "resourceprocessor", "resourcetolabel", "retryable", "rpcz", "<PERSON><PERSON><PERSON><PERSON>", "safelist", "samplereceiver", "samplingdecision", "samplingprocessor", "sarama", "sattributes", "sattributesprocessor", "scrapererror", "scraperhelper", "scrapertest", "semconv", "servicetelemetry", "servicetest", "servicez", "sfixed", "sharedcomponent", "sigstore", "someclientid", "someclientsecret", "somevalue", "songy", "spanmetricsconnector", "spanmetricsprocessor", "spanprocessor", "sprocessor", "statusdata", "statusreporting", "statuswatcher", "stdlib", "stencel", "stretchr", "subcomponent", "subcomponents", "subpackages", "swiatekm", "tailsampling", "tchannel", "telemetrygen", "testcomponents", "testconverter", "testdata", "testfunc", "testonly", "testprovider", "testutil", "tlsconfig", "tocstop", "tpmrm", "tracecontext", "traceid", "tracesonmemory", "tracetranslator", "tracez", "triager", "triagers", "triaging", "uints", "unixgram", "unixpacket", "unkeyed", "unmarshal", "unmarshalling", "Unmarshalable", "unmarshalls", "unshallow", "unstarted", "userfriendly", "validatable", "van<PERSON><PERSON><PERSON>", "vmmetrics", "<PERSON><PERSON><PERSON>", "xconfighttp", "xconfmap", "xconnector", "xconsumer", "xconsumererror", "xexporter", "xexporterhelper", "xextension", "xpdata", "xpipeline", "xprocessor", "xprocessorhelper", "xreceiver", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yam<PERSON><PERSON><PERSON><PERSON>", "yamls", "zapcore", "zipkin", "zipkinexporter", "zipkinreceiver", "zipkinv", "zpages", "zpagesextension", "zstd"]}