name: Spell Check

on: [push, pull_request]

jobs:
  spell-check:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Run cSpell
        uses: streetsidesoftware/cspell-action@69543c3f9f14d4fcc6004c7bee03c4d366f11d64 # v7.0.1
        with:
          files: |
            **/*.{md,yaml,yml}
          config: '.github/workflows/utils/cspell.json'
