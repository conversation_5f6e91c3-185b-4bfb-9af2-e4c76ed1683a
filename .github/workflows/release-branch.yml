name: Automation - Release Branch

on:
  push:
    tags:
      # Trigger on beta version tags (0.x.x series) to create release branch
      # This pattern matches: v0.{minor}.{patch} for new releases and bugfix releases
      - 'v0.[0-9]+.[0-9]+'
      - 'v0.[0-9]+.[0-9]+-*' # Also support release candidates if needed

jobs:
  release-branch:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0

      - name: Setup Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: 1.23.10

      - name: Setup Git config
        run: |
          git config --global user.name "opentelemetrybot"
          git config --global user.email "<EMAIL>"

      - name: Run release-branch.sh
        run: |
          ./.github/workflows/scripts/release-branch.sh
        env:
          UPSTREAM_REMOTE_NAME: "origin"
          MAIN_BRANCH_NAME: "main"
          GITHUB_REF: ${{ github.ref }}
