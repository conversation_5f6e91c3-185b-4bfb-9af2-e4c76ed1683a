name: build-and-test-arm
on:
  push:
    branches: [main]
    tags:
      - "v[0-9]+.[0-9]+.[0-9]+*"
  merge_group:
    types: [checks_requested]
  pull_request:
env:
  TEST_RESULTS: testbed/tests/results/junit/results.xml
  # Make sure to exit early if cache segment download times out after 2 minutes.
  # We limit cache download as a whole to 5 minutes.
  SEGMENT_DOWNLOAD_TIMEOUT_MINS: 2

permissions: read-all

# Do not cancel this workflow on main. See https://github.com/open-telemetry/opentelemetry-collector-contrib/pull/16616
concurrency:
  group: ${{ github.workflow }}-${{ github.ref_name }}
  cancel-in-progress: true

jobs:
  arm-unittest-matrix:
    strategy:
      matrix:
        os: [ubuntu-22.04-arm, macos-14]
    if: ${{ github.actor != 'dependabot[bot]' && (contains(github.event.pull_request.labels.*.name, 'Run ARM') || github.event_name == 'push' || github.event_name == 'merge_group') }}
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: 1.23.10
          cache: false
      - name: Cache Go
        id: go-cache
        timeout-minutes: 5
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: |
            ~/go/bin
            ~/go/pkg/mod
          key: go-build-cache-${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
      - name: Install dependencies
        if: steps.go-cache.outputs.cache-hit != 'true'
        run: make gomoddownload
      - name: Install Tools
        if: steps.go-cache.outputs.cache-hit != 'true'
        run: make install-tools
      - name: Run Unit Tests
        run: make -j4 gotest
  arm-unittest:
    if: ${{ github.actor != 'dependabot[bot]' && (contains(github.event.pull_request.labels.*.name, 'Run ARM') || github.event_name == 'push' || github.event_name == 'merge_group') }}
    runs-on: ubuntu-latest
    needs: [arm-unittest-matrix]
    steps:
      - name: Print result
        run: echo ${{ needs.arm-unittest-matrix.result }}
      - name: Interpret result
        run: |
          if [[ success == ${{ needs.arm-unittest-matrix.result }} ]]
          then
            echo "All matrix jobs passed!"
          else
            echo "One or more matrix jobs failed."
            false
          fi
