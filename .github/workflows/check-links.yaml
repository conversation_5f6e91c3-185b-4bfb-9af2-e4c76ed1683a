name: check-links
on:
  push:
    branches: [main]
  pull_request:
  merge_group:
    types: [checks_requested]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref_name }}
  cancel-in-progress: true

permissions: read-all

jobs:
  changedfiles:
    name: changed files
    runs-on: ubuntu-latest
    env:
      PR_HEAD: ${{ github.event.pull_request.head.sha }}
    outputs:
      files: ${{ steps.changes.outputs.files }}
    steps:
      - name: Checkout Repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
      - name: Get changed files
        id: changes
        run: |
          files=$(git diff --name-only --diff-filter=ACMRTUXB $(git merge-base origin/main $PR_HEAD) $PR_HEAD | grep .md$ | xargs)

          if [ -z "$files" ] && git diff --name-only $(git merge-base origin/main $PR_HEAD) $PR_HEAD | grep -q "package.json"; then
            files="**/*.md"
          fi

          echo "files=$files" >> $GITHUB_OUTPUT
  check-links:
    runs-on: ubuntu-latest
    needs: changedfiles
    if: ${{needs.changedfiles.outputs.files}}
    steps:
      - name: Checkout Repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0

      - name: Link Checker
        id: lychee
        uses: lycheeverse/lychee-action@82202e5e9c2f4ef1a55a3d02563e1cb6041e5332
        with:
          args: "--verbose --no-progress ${{needs.changedfiles.outputs.files}} --config .github/lychee.toml"
          failIfEmpty: false
