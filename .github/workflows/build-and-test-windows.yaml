name: build-and-test-windows
on:
  push:
    branches: [main]
    tags:
      - "v[0-9]+.[0-9]+.[0-9]+*"
  merge_group:
    types: [checks_requested]
  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref_name }}
  cancel-in-progress: true

permissions: read-all

jobs:
  windows-unittest:
    strategy:
      fail-fast: false
      matrix:
        os: [windows-2022, windows-2025]
    runs-on: ${{ matrix.os }}
    steps:
      - name: Checkout Rep<PERSON>
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: 1.23.10
          cache: false
      - name: Cache Go
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        env:
          cache-name: cache-go-modules
        with:
          path: |
            ~\go\pkg\mod
            ~\AppData\Local\go-build
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
      - name: Ensure required ports in the dynamic range are available
        run: |
          & ${{ github.workspace }}\.github\workflows\scripts\win-required-ports.ps1
      - name: Run Unit Tests
        run: make gotest

  windows-service-test:
    strategy:
      fail-fast: false
      matrix:
        os: [windows-2022, windows-2025]
    runs-on: ${{ matrix.os }}
    steps:
      - name: Checkout Repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: 1.23.10
          cache: false
      - name: Cache Go
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        env:
          cache-name: cache-go-modules
        with:
          path: |
            ~\go\pkg\mod
            ~\AppData\Local\go-build
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
      - name: Ensure required ports in the dynamic range are available
        run: |
          & ${{ github.workspace }}\.github\workflows\scripts\win-required-ports.ps1

      - name: Make otelcorecol
        run: make otelcorecol

      - name: Install otelcorecol as a service
        run: |
          New-Service -Name "otelcorecol" -StartupType "Manual" -BinaryPathName "${PWD}\bin\otelcorecol_windows_amd64 --config ${PWD}\examples\local\otel-config.yaml"
          eventcreate.exe /t information /id 1 /l application /d "Creating event provider for 'otelcorecol'" /so otelcorecol

      - name: Test otelcorecol service
        working-directory: ${{ github.workspace }}/otelcol
        run: |
          go test -timeout 90s -run ^TestCollectorAsService$  -v -tags=win32service

      - name: Remove otelcorecol service
        if: always()
        run: |
          Remove-Service otelcorecol
          Remove-Item HKLM:\SYSTEM\CurrentControlSet\Services\EventLog\Application\otelcorecol
