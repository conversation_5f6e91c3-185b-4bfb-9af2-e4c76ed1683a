# This GitHub action is used to compare API state snapshots of Main
# to Head of the PR in order to validate releases are not breaking
# backwards compatibility.
#
# This GitHub action will fail if there are incompatible changes.
#
name: "Inform Incompatible PRs"
on:
  pull_request:
    branches:
      - main

permissions: read-all

jobs:
  Check-Compatibility:
    runs-on: ubuntu-latest
    env:
      BASE_REF: ${{ github.base_ref }}
      HEAD_REF: ${{ github.head_ref }}
    steps:
      - name: Checkout-Main
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: ${{ github.base_ref }}
          path: ${{ github.base_ref }}

      - name: Checkout-HEAD
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          path: ${{ github.head_ref }}

      - name: Setup Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: 1.23.10

      # Generate apidiff states of Main
      - name: Generate-States
        run: |
          cd $BASE_REF
          make apidiff-build

      # Compare apidiff states of Main with PR
      - name: Compare-States
        env:
          CI: true
          COMPARE_OPTS: -d "../${{ github.base_ref }}/internal/data/apidiff"
        run: |
          cd $HEAD_REF
          make apidiff-compare

      # Fail GitHub Action if there are incompatible changes
      - name: Check-States
        env:
          CI: true
          COMPARE_OPTS: -d "../${{ github.base_ref }}/internal/data/apidiff" -c
        run: |
          cd $HEAD_REF
          make apidiff-compare
