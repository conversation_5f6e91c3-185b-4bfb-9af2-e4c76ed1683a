name: Builder - Integration tests

on:
  # on changes to the main branch touching the builder
  push:
    branches: [main]

  # on PRs touching the builder
  pull_request:
    branches: [main]

  # once a day at 6:17 AM UTC
  schedule:
    - cron: "17 6 * * *"

  # manual execution
  workflow_dispatch:

  merge_group:
    types: [checks_requested]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref_name }}
  cancel-in-progress: true

permissions: read-all

jobs:
  integration-test:
    name: Integration test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout <PERSON><PERSON>
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: 1.23.10
      - name: Test
        run: make builder-integration-test
