name: 'Ping code owners on PRs'
on:
  pull_request_target:
    types: [labeled]

permissions: read-all

jobs:
  ping-owners:
    permissions:
      pull-requests: write
    runs-on: ubuntu-24.04
    if: ${{ github.actor != 'dependabot[bot]' && github.actor != 'renovate[bot]' && github.repository_owner == 'open-telemetry' }}
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
    
      - name: Run ping-codeowners-prs.sh
        run: ./.github/workflows/scripts/ping-codeowners-prs.sh
        env:
          REPO: ${{ github.repository }}
          AUTHOR: ${{ github.event.pull_request.user.login }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          PR: ${{ github.event.number }}
          COMPONENT: ${{ github.event.label.name }}
