# One of 'breaking', 'deprecation', 'new_component', 'enhancement', 'bug_fix'
change_type: enhancement

# The name of the component, or a single word describing the area of concern, (e.g. otlpreceiver)
component: exporterhelper

# A brief description of the change.  Surround your text with quotes ("") if it needs to start with a backtick (`).
note: Preserve request span context and client information in the persistent queue.

# One or more tracking issues or pull requests related to the change
issues: [11740, 13220, 13232]

# (Optional) One or more lines of additional information to render under the primary note.
# These lines will be padded with 2 spaces and then inserted directly into the document.
# Use pipe (|) for multiline entries.
subtext: |
  It allows internal collector spans and client information to propagate through the persistent queue used by 
  the exporters. The same way as it's done for the in-memory queue.
  Currently, it is behind the exporter.PersistRequestContext feature gate, which can be enabled by adding 
  `--feature-gates=exporter.PersistRequestContext` to the collector command line. An exporter buffer stored by
  a previous version of the collector (or by a collector with the feature gate disabled) can be read by a newer
  collector with the feature enabled. However, the reverse is not supported: a buffer stored by a newer collector with
  the feature enabled cannot be read by an older collector (or by a collector with the feature gate disabled).

# Optional: The change log or logs in which this entry should be included.
# e.g. '[user]' or '[user, api]'
# Include 'user' if the change is relevant to end users.
# Include 'api' if there is a change to a library API.
# Default: '[user]'
change_logs: [user]
