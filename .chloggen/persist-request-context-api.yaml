# One of 'breaking', 'deprecation', 'new_component', 'enhancement', 'bug_fix'
change_type: breaking

# The name of the component, or a single word describing the area of concern, (e.g. otlpreceiver)
component: exporterhelper

# A brief description of the change.  Surround your text with quotes ("") if it needs to start with a backtick (`).
note: QueueBatchEncoding interface is changed to support marshaling and unmarshaling of request context.

# One or more tracking issues or pull requests related to the change
issues: [13188]

# Optional: The change log or logs in which this entry should be included.
# e.g. '[user]' or '[user, api]'
# Include 'user' if the change is relevant to end users.
# Include 'api' if there is a change to a library API.
# Default: '[user]'
change_logs: [api]
