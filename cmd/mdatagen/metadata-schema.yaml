# Required: The type of the component - Usually the name. The type and class combined uniquely identify the component (eg. receiver/otlp) or subcomponent (eg. receiver/hostmetricsreceiver/cpu)
type:

# Required for subcomponents: The type of the parent component.
parent: string

# Optional: Scope name for the telemetry generated by the component. If not set, name of the go package will be used.
scope_name: string

# Optional: The name of the package that mdatagen generates. If not set, the name "metadata" will be used.
generated_package_name: string

# Required for components (Optional for subcomponents): A high-level view of the development status and use of this component
status:
  # Required: The class of the component (For example receiver)
  class: <receiver|processor|exporter|connector|extension|cmd|pkg|scraper|converter|provider>
  # Required: The stability of the component - See https://github.com/open-telemetry/opentelemetry-collector/blob/main/docs/component-stability.md#stability-levels
  stability:
    development: [<metrics|traces|logs|traces_to_metrics|metrics_to_metrics|logs_to_metrics|extension,converter,provider>]
    alpha: [<metrics|traces|logs|traces_to_metrics|metrics_to_metrics|logs_to_metrics|extension,converter,provider>]
    beta: [<metrics|traces|logs|traces_to_metrics|metrics_to_metrics|logs_to_metrics|extension,converter,provider>]
    stable: [<metrics|traces|logs|traces_to_metrics|metrics_to_metrics|logs_to_metrics|extension,converter,provider>]
    deprecated: [<metrics|traces|logs|traces_to_metrics|metrics_to_metrics|logs_to_metrics|extension,converter,provider>]
    unmaintained: [<metrics|traces|logs|traces_to_metrics|metrics_to_metrics|logs_to_metrics|extension,converter,provider>]
  # Required for deprecated components: The deprecation information for the deprecated components - See https://github.com/open-telemetry/opentelemetry-collector/blob/main/docs/component-stability.md#deprecation-information
  deprecation:
    <component>:
      date: string
      migration: string
  # Optional: The distributions that this component is bundled with (For example core or contrib). See statusdata.go for a list of common distros.
  distributions: [string]
  # Optional: A list of warnings that should be brought to the attention of users looking to use this component
  warnings: [string]
  # Optional: Metadata related to codeowners of the component
  codeowners:
    active: [string]
    emeritus: [string]
  unsupported_platforms: [<linux|windows>]

# Optional: OTel Semantic Conventions version that will be associated with the scraped metrics.
# This attribute should be set for metrics compliant with OTel Semantic Conventions.
sem_conv_version: 1.9.0

# Optional: map of resource attribute definitions with the key being the attribute name.
resource_attributes:
  <attribute.name>:
    # Required: whether the resource attribute is added the emitted metrics by default.
    enabled: bool
    # Required: description of the attribute.
    description:
    # Optional: array of attribute values if they are static values (currently, only string type is supported).
    enum: [string]
    # Required: attribute value type.
    type: <string|int|double|bool|bytes|slice|map>
    # Optional: warnings that will be shown to user under specified conditions.
    warnings:
      # A warning that will be displayed if the resource_attribute is enabled in user config.
      # Should be used for deprecated default resource_attributes that will be removed soon.
      if_enabled:
      # A warning that will be displayed if `enabled` field is not set explicitly in user config.
      # Should be used for resource_attributes that will be turned from default to optional or vice versa.
      if_enabled_not_set:
      # A warning that will be displayed if the resource_attribute is configured by user in any way.
      # Should be used for deprecated optional resource_attributes that will be removed soon.
      if_configured:


# Optional: map of attribute definitions with the key being the attribute name and value
# being described below.
attributes:
  <attribute.name>:
    # Optional: this field can be used to override the actual attribute name defined by the key.
    # It should be used if multiple metrics have different attributes with the same name.
    name_override:
    # Required: description of the attribute.
    description:
    # Optional: array of attribute values if they are static values (currently, only string type is supported).
    enum: [string]
    # Required: attribute value type.
    type: <string|int|double|bool|bytes|slice|map>

# Optional: map of metric names with the key being the metric name and value
# being described below.
metrics:
  <metric.name>:
    # Required: whether the metric is collected by default.
    enabled: bool
    # Required: metric description.
    description:
    # Optional: extended documentation of the metric.
    extended_documentation:
    # Optional: warnings that will be shown to user under specified conditions.
    warnings:
      # A warning that will be displayed if the metric is enabled in user config.
      # Should be used for deprecated default metrics that will be removed soon.
      if_enabled:
      # A warning that will be displayed if `enabled` field is not set explicitly in user config.
      # Should be used for metrics that will be turned from default to optional or vice versa.
      if_enabled_not_set:
      # A warning that will be displayed if the metrics is configured by user in any way.
      # Should be used for deprecated optional metrics that will be removed soon.
      if_configured:
    # Required: metric unit as defined by https://ucum.org/ucum.html.
    unit:
    # Required: metric type with its settings.
    <sum|gauge>:
      # Required for sum and gauge metrics: type of number data point values.
      value_type: <int|double>
      # Required for sum metric: whether the metric is monotonic (no negative delta values).
      monotonic: bool
      # Required for sum metric: whether reported values incorporate previous measurements
      # (cumulative) or not (delta).
      aggregation_temporality: <delta|cumulative>
       # Optional: Indicates the type the metric needs to be parsed from. If set, the generated
       # functions will parse the value from string to value_type.
      input_type: string
    # Optional: array of attributes that were defined in the attributes section that are emitted by this metric.
    attributes: [string]

# Optional: map of event names with the key being the event name and value
# being described below.
events:
  <event.name>:
    # Required: whether the event is collected by default.
    enabled: bool
    # Required: event description.
    description:
    # Optional: extended documentation of the event.
    extended_documentation:
    # Optional: warnings that will be shown to user under specified conditions.
    warnings:
      # A warning that will be displayed if the event is enabled in user config.
      # Should be used for deprecated default events that will be removed soon.
      if_enabled:
      # A warning that will be displayed if `enabled` field is not set explicitly in user config.
      if_enabled_not_set:
      # A warning that will be displayed if the event is configured by user in any way.
      if_configured:
    # Optional: array of attributes that were defined in the attributes section that are emitted by this event.
    attributes: [string]

# Lifecycle tests generated for this component.
tests:
  config: # {} by default, specific testing configuration for lifecycle tests.
  # Skip lifecycle tests for this component. Not recommended for components that are not in development.
  skip_lifecycle: false # false by default
  # Skip shutdown tests for this component. Not recommended for components that are not in development.
  skip_shutdown: false # false by default
  # Whether it's expected that the Consume[Logs|Metrics|Traces] method will return an error with the given configuration.
  expect_consumer_error: true # false by default
  goleak: # {} by default generates a package_test to enable check for leaks
    skip: false # set to true if goleak tests should be skipped
    setup: string # Optional: supports configuring a setup function that runs before goleak checks
    teardown: string # Optional: supports configuring a teardown function that runs before goleak checks
    ignore:
      top: [string] # Optional: array of strings representing functions that should be ignore via IgnoreTopFunction
      any: [string] # Optional: array of strings representing functions that should be ignore via IgnoreAnyFunction


# Optional: map of metric names with the key being the metric name and value
# being described below.
telemetry:
  metrics:
    <metric.name>:
      # Required: whether the metric is collected by default.
      enabled: bool
      # Required: metric description.
      description:
      # Optional: the stability level of the metric. Set to alpha by default.
      stability: [alpha|stable|deprecated]
      # Optional: extended documentation of the metric.
      extended_documentation:
      # Optional: whether or not this metric is optional. Optional metrics may only be initialized
      # if certain features are enabled or configured.
      optional: bool
      # Optional: warnings that will be shown to user under specified conditions.
      warnings:
        # A warning that will be displayed if the metric is enabled in user config.
        # Should be used for deprecated default metrics that will be removed soon.
        if_enabled:
        # A warning that will be displayed if `enabled` field is not set explicitly in user config.
        # Should be used for metrics that will be turned from default to optional or vice versa.
        if_enabled_not_set:
        # A warning that will be displayed if the metrics is configured by user in any way.
        # Should be used for deprecated optional metrics that will be removed soon.
        if_configured:
      # Required: metric unit as defined by https://ucum.org/ucum.html.
      unit:
      # Required: metric type with its settings.
      <sum|gauge|histogram>:
        # Optional: Whether this metric is asynchronous. If async, a mechanism is required to be able to
        # pass in options to the callbacks that are called when the metric is observed.
        async: bool
        # Required: type of number data point values.
        value_type: <int|double>
        # Required for sum metric: whether the metric is monotonic (no negative delta values).
        monotonic: bool
        # Bucket boundaries are only available to set for histogram metrics.
        bucket_boundaries: [double]
      # Optional: array of attributes that were defined in the attributes section that are emitted by this metric.
      # Note: Only the following attribute types are supported: <string|int|double|bool>
      attributes: [string]
