// Code generated by mdatagen. DO NOT EDIT.

package {{ .Package }}

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

{{- $enabledAttrCount := 0 }}
{{- range $_, $attr := .ResourceAttributes }}
{{- if $attr.Enabled }}
{{- $enabledAttrCount = inc $enabledAttrCount }}
{{- end }}
{{- end }}

func TestResourceBuilder(t *testing.T) {
	for _, tt := range []string{"default", "all_set", "none_set"} {
		t.Run(tt, func(t *testing.T) {
			cfg := loadResourceAttributesConfig(t, tt)
			rb := NewResourceBuilder(cfg)
			{{- range $name, $attr := .ResourceAttributes }}
			{{- if $attr.Enum }}
			rb.Set{{ $name.Render }}{{ index $attr.Enum 0 | publicVar }}()
			{{- else }}
			rb.Set{{ $name.Render }}({{ $attr.TestValue }})
			{{- end }}
			{{- end }}

			res := rb.Emit()
			assert.Equal(t, 0, rb.Emit().Attributes().Len()) // Second call should return empty Resource

			switch tt {
			case "default":
				assert.Equal(t, {{ $enabledAttrCount }}, res.Attributes().Len())
			case "all_set":
				assert.Equal(t, {{ len .ResourceAttributes }}, res.Attributes().Len())
			case "none_set":
				assert.Equal(t, 0, res.Attributes().Len())
				return
			default:
				assert.Failf(t, "unexpected test case: %s", tt)
			}

			{{ $assignSign := ":=" }}
			{{- range $name, $attr := .ResourceAttributes }}
			val, ok {{ $assignSign }} res.Attributes().Get("{{ $name }}")
			{{- if $attr.Enabled }}
			assert.True(t, ok)
			{{- else }}
			assert.Equal(t, tt == "all_set", ok)
			{{- end }}
			if ok {
				{{- if eq $attr.Type.String "Int" -}}
				assert.EqualValues(t, {{ $attr.TestValue }}, val.{{ $attr.Type }}()
				{{- else -}}
				assert.Equal(t, {{ $attr.TestValue }}, val.{{ $attr.Type }}()
				{{- end -}}
				{{- if or (eq $attr.Type.String "Bytes") (eq $attr.Type.String "Slice") (eq $attr.Type.String "Map") -}}
				.AsRaw()
				{{- end -}}
				)
			}
			{{- $assignSign = "=" }}
			{{- end }}
		})
	}
}
