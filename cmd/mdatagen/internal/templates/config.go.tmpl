// Code generated by mdatagen. DO NOT EDIT.

package {{ .Package }}

import (
    "go.opentelemetry.io/collector/confmap"
    {{ if and .Metrics .ResourceAttributes -}}
    "go.opentelemetry.io/collector/filter"
    {{- end }}
)
{{ if .Metrics -}}

// MetricConfig provides common config for a particular metric.
type MetricConfig struct {
	Enabled bool `mapstructure:"enabled"`

	enabledSetByUser bool
}

func (ms *MetricConfig) Unmarshal(parser *confmap.Conf) error {
	if parser == nil {
		return nil
	}
	err := parser.Unmarshal(ms)
	if err != nil {
		return err
	}
	ms.enabledSetByUser = parser.IsSet("enabled")
	return nil
}

// MetricsConfig provides config for {{ .Type }} metrics.
type MetricsConfig struct {
	{{- range $name, $metric := .Metrics }}
	{{ $name.Render }} MetricConfig `mapstructure:"{{ $name }}"`
	{{- end }}
}

func DefaultMetricsConfig() MetricsConfig {
	return MetricsConfig{
		{{- range $name, $metric := .Metrics }}
		{{ $name.Render }}: MetricConfig{
			Enabled: {{ $metric.Enabled }},
		},
		{{- end }}
	}
}
{{- end }}

{{ if .Events }}
// EventConfig provides common config for a particular event.
type EventConfig struct {
	Enabled bool `mapstructure:"enabled"`

	enabledSetByUser bool
}

func (ec *EventConfig) Unmarshal(parser *confmap.Conf) error {
	if parser == nil {
		return nil
	}
	err := parser.Unmarshal(ec)
	if err != nil {
		return err
	}
	ec.enabledSetByUser = parser.IsSet("enabled")
	return nil
}

// EventsConfig provides config for {{ .Type }} events.
type EventsConfig struct {
	{{- range $name, $event := .Events }}
	{{ $name.Render }} EventConfig `mapstructure:"{{ $name }}"`
	{{- end }}
}

func DefaultEventsConfig() EventsConfig {
	return EventsConfig{
		{{- range $name, $event := .Events }}
		{{ $name.Render }}: EventConfig{
			Enabled: {{ $event.Enabled }},
		},
		{{- end }}
	}
}
{{- end }}

{{ if .ResourceAttributes -}}
// ResourceAttributeConfig provides common config for a particular resource attribute.
type ResourceAttributeConfig struct {
	Enabled bool `mapstructure:"enabled"`
	{{- if .Metrics }}
	// Experimental: MetricsInclude defines a list of filters for attribute values.
	// If the list is not empty, only metrics with matching resource attribute values will be emitted.
	MetricsInclude []filter.Config `mapstructure:"metrics_include"`
	// Experimental: MetricsExclude defines a list of filters for attribute values.
	// If the list is not empty, metrics with matching resource attribute values will not be emitted.
	// MetricsInclude has higher priority than MetricsExclude.
	MetricsExclude []filter.Config `mapstructure:"metrics_exclude"`
	{{- end }}
	{{- if .Events }}
	// Experimental: EventsInclude defines a list of filters for attribute values.
	// If the list is not empty, only events with matching resource attribute values will be emitted.
	EventsInclude []filter.Config `mapstructure:"events_include"`
	// Experimental: EventsExclude defines a list of filters for attribute values.
	// If the list is not empty, events with matching resource attribute values will not be emitted.
	// EventsInclude has higher priority than EventsExclude.
	EventsExclude []filter.Config `mapstructure:"events_exclude"`
	{{- end }}

	enabledSetByUser bool
}

func (rac *ResourceAttributeConfig) Unmarshal(parser *confmap.Conf) error {
	if parser == nil {
		return nil
	}
	err := parser.Unmarshal(rac)
	if err != nil {
		return err
	}
	rac.enabledSetByUser = parser.IsSet("enabled")
	return nil
}

// ResourceAttributesConfig provides config for {{ .Type }} resource attributes.
type ResourceAttributesConfig struct {
	{{- range $name, $attr := .ResourceAttributes }}
	{{ $name.Render }} ResourceAttributeConfig `mapstructure:"{{ $name }}"`
	{{- end }}
}

func DefaultResourceAttributesConfig() ResourceAttributesConfig {
	return ResourceAttributesConfig{
		{{- range $name, $attr := .ResourceAttributes }}
		{{ $name.Render }}: ResourceAttributeConfig {
			Enabled: {{ $attr.Enabled }},
		},
		{{- end }}
	}
}
{{- end }}

{{ if .Metrics -}}
// MetricsBuilderConfig is a configuration for {{ .Type }} metrics builder.
type MetricsBuilderConfig struct {
	Metrics MetricsConfig `mapstructure:"metrics"`
	{{- if .ResourceAttributes }}
	ResourceAttributes ResourceAttributesConfig `mapstructure:"resource_attributes"`
	{{- end }}
}

func DefaultMetricsBuilderConfig() MetricsBuilderConfig {
	return MetricsBuilderConfig {
		Metrics: DefaultMetricsConfig(),
		{{- if .ResourceAttributes }}
		ResourceAttributes: DefaultResourceAttributesConfig(),
		{{- end }}
	}
}
{{- end }}

{{ if .Events -}}
// LogsBuilderConfig is a configuration for {{ .Type }} logs builder.
type LogsBuilderConfig struct {
	Events EventsConfig `mapstructure:"events"`
	{{- if .ResourceAttributes }}
	ResourceAttributes ResourceAttributesConfig `mapstructure:"resource_attributes"`
	{{- end }}
}

func DefaultLogsBuilderConfig() LogsBuilderConfig {
	return LogsBuilderConfig {
		Events: DefaultEventsConfig(),
		{{- if .ResourceAttributes }}
		ResourceAttributes: DefaultResourceAttributesConfig(),
		{{- end }}
	}
}
{{- end }}
