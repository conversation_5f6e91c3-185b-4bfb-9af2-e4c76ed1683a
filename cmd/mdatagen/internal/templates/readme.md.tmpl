<!-- status autogenerated section -->
{{- if len .Status.Stability }}
| Status        |           |
| ------------- |-----------|
{{- $class := .Status.Class }}
{{- $shortName := .ShortFolderName }}
{{- if ne $class "connector" }}
{{- $idx := 0 }}
{{- range $stability, $value := .Status.Stability }}
| {{ if not $idx }}Stability{{ else }}         {{ end }}     | [{{ toLowerCase $stability.String }}]{{ if and (ne $class "extension") (ne $class "converter") (ne $class "provider") }}: {{ stringsJoin $value ", " }} {{ end }}  |
{{- $idx = inc $idx }}
{{- end }}
{{- if .Status.Deprecation }}
{{- range $deprecation, $value := .Status.Deprecation }}
| Deprecation of {{ toLowerCase $deprecation }} | [Date]: {{ $value.Date }}{{ if and (ne $class "extension") (ne $class "converter") (ne $class "provider") }} {{ end }}  |
|                      | [Migration Note]: {{ $value.Migration }}{{ if ne $class "extension" }} {{ end }}  |
{{- end }}
{{- end }}
{{- end}}
{{- if .Status.UnsupportedPlatforms }}
| Unsupported Platforms | {{ stringsJoin .Status.UnsupportedPlatforms ", " }} |
{{- end }}
{{- if and (ne $class "cmd") (ne $class "pkg")  }}
| Distributions | [{{ stringsJoin .Status.SortedDistributions "], [" }}] |
{{- end }}
{{- if .Status.Warnings }}
| Warnings      | [{{ stringsJoin .Status.Warnings ", " }}](#warnings) |
{{- end }}
{{- if ne $class "" }}
| Issues        | [![Open issues](https://img.shields.io/github/issues-search/{{ .GithubProject }}?query=is%3Aissue%20is%3Aopen%20label%3A{{ $class }}%2F{{ $shortName }}%20&label=open&color=orange&logo=opentelemetry)](https://github.com/{{ .GithubProject }}/issues?q=is%3Aopen+is%3Aissue+label%3A{{ $class }}%2F{{ $shortName }}) [![Closed issues](https://img.shields.io/github/issues-search/{{ .GithubProject }}?query=is%3Aissue%20is%3Aclosed%20label%3A{{ $class }}%2F{{ $shortName }}%20&label=closed&color=blue&logo=opentelemetry)](https://github.com/{{ .GithubProject }}/issues?q=is%3Aclosed+is%3Aissue+label%3A{{ $class }}%2F{{ $shortName }}) |
{{- if not .Status.DisableCodeCov }}
| Code coverage | [![codecov](https://codecov.io/github/{{ .GithubProject }}/graph/main/badge.svg?component={{ .GetCodeCovComponentID }})](https://app.codecov.io/gh/{{ .GithubProject}}/tree/main/?components%5B0%5D={{ .GetCodeCovComponentID }}&displayType=list) |
{{- end }}
{{- end }}
{{- if .Status.Codeowners }}
{{- $codeowners := userLinks .Status.Codeowners.Active }}
{{- $emeritus := userLinks .Status.Codeowners.Emeritus }}
| [Code Owners](https://github.com/open-telemetry/opentelemetry-collector-contrib/blob/main/CONTRIBUTING.md#becoming-a-code-owner)    | {{ stringsJoin $codeowners ", " }} {{ if .Status.Codeowners.SeekingNew }}\| Seeking more code owners! {{ end }}|
{{- if $emeritus }}
| Emeritus      | {{ stringsJoin $emeritus ", " }} |
{{- end }}
{{- end }}
{{range $stability, $val := .Status.Stability}}
[{{ toLowerCase $stability.String }}]: https://github.com/open-telemetry/opentelemetry-collector/blob/main/docs/component-stability.md#{{ toLowerCase $stability.String }}
{{- end }}
{{- if .Status.Deprecation }}
[Date]: https://github.com/open-telemetry/opentelemetry-collector/blob/main/docs/component-stability.md#deprecation-information
[Migration Note]: https://github.com/open-telemetry/opentelemetry-collector/blob/main/docs/component-stability.md#deprecation-information
{{- end }}
{{- range .Status.SortedDistributions }}
[{{.}}]: {{ distroURL . }}
{{- end }}
{{- if eq $class "connector"}}

## Supported Pipeline Types

| [Exporter Pipeline Type] | [Receiver Pipeline Type] | [Stability Level] |
| ------------------------ | ------------------------ | ----------------- |
{{- range $stability, $pipelines := .Status.Stability }}
{{- range $pipeline := $pipelines }}
{{- $parts := stringsSplit $pipeline "_to_"  }}
| {{index $parts 0}} | {{index $parts 1}} | [{{ toLowerCase $stability.String }}] |
{{- end }}
{{- end }}

[Exporter Pipeline Type]: https://github.com/open-telemetry/opentelemetry-collector/blob/main/connector/README.md#exporter-pipeline-type
[Receiver Pipeline Type]: https://github.com/open-telemetry/opentelemetry-collector/blob/main/connector/README.md#receiver-pipeline-type
[Stability Level]: https://github.com/open-telemetry/opentelemetry-collector/blob/main/docs/component-stability.md#stability-levels
{{- end }}
{{- end }}
<!-- end autogenerated section -->