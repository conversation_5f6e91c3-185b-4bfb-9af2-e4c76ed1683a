// Code generated by mdatagen. DO NOT EDIT.

package {{ .Package }}

import (
	"go.opentelemetry.io/collector/pdata/pcommon"
)

// ResourceBuilder is a helper struct to build resources predefined in metadata.yaml.
// The ResourceBuilder is not thread-safe and must not to be used in multiple goroutines.
type ResourceBuilder struct {
	config ResourceAttributesConfig
	res	pcommon.Resource
}

// NewResourceBuilder creates a new ResourceBuilder. This method should be called on the start of the application.
func NewResourceBuilder(rac ResourceAttributesConfig) *ResourceBuilder {
	return &ResourceBuilder{
		config: rac,
		res: pcommon.NewResource(),
	}
}

{{- range $name, $attr := .ResourceAttributes }}
{{- range $attr.Enum }}
// Set{{ $name.Render }}{{ . | publicVar }} sets "{{ $name }}={{ . }}" attribute.
func (rb *ResourceBuilder) Set{{ $name.Render }}{{ . | publicVar }}() {
	if rb.config.{{ $name.Render }}.Enabled {
		rb.res.Attributes().PutStr("{{ $name }}", "{{ . }}")
	}
}
{{- else }}
// Set{{ $name.Render }} sets provided value as "{{ $name }}" attribute.
func (rb *ResourceBuilder) Set{{ $name.Render }}(val {{ $attr.Type.Primitive }}) {
	if rb.config.{{ $name.Render }}.Enabled {
		{{- if or (eq $attr.Type.String "Bytes") (eq $attr.Type.String "Slice") (eq $attr.Type.String "Map") }}
		rb.res.Attributes().PutEmpty{{ $attr.Type }}("{{ $name }}").FromRaw(val)
		{{- else }}
		rb.res.Attributes().Put{{ $attr.Type }}("{{ $name }}", val)
		{{- end }}
	}
}
{{- end }}
{{ end }}

// Emit returns the built resource and resets the internal builder state.
func (rb *ResourceBuilder) Emit() pcommon.Resource {
	r := rb.res
	rb.res = pcommon.NewResource()
	return r
}
