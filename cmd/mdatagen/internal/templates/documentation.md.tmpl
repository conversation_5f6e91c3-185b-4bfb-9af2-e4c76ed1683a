{{- define "metric-documentation" -}}
{{- $metricName := . }}
{{- $metric := $metricName | metricInfo -}}

### {{ $metricName }}

{{ $metric.Description }}

{{- if $metric.ExtendedDocumentation }}

{{ $metric.ExtendedDocumentation }}

{{- end }}

| Unit | Metric Type | Value Type |{{ if $metric.Data.HasAggregated }} Aggregation Temporality |{{ end }}{{ if $metric.Data.HasMonotonic }} Monotonic |{{ end }}
| ---- | ----------- | ---------- |{{ if $metric.Data.HasAggregated }} ----------------------- |{{ end }}{{ if $metric.Data.HasMonotonic }} --------- |{{ end }}
| {{ $metric.Unit }} | {{ $metric.Data.Type }} | {{ $metric.Data.MetricValueType }} |
{{- if $metric.Data.HasAggregated }} {{ $metric.Data.AggregationTemporality }} |{{ end }}
{{- if $metric.Data.HasMonotonic }} {{ $metric.Data.Monotonic }} |{{ end }}

{{- if $metric.Attributes }}

#### Attributes

| Name | Description | Values |
| ---- | ----------- | ------ |
{{- range $metric.Attributes }}
{{- $attribute := . | attributeInfo }}
| {{ $attribute.Name }} | {{ $attribute.Description }} |
{{- if $attribute.Enum }} {{ $attribute.Type }}: ``{{ stringsJoin $attribute.Enum "``, ``" }}``{{ else }} Any {{ $attribute.Type }}{{ end }} |
{{- end }}

{{- end }}

{{- end -}}

{{- define "event-documentation" -}}
{{- $eventName := . }}
{{- $event := $eventName | eventInfo -}}

### {{ $eventName }}

{{ $event.Description }}

{{- if $event.ExtendedDocumentation }}

{{ $event.ExtendedDocumentation }}

{{- end }}

{{- if $event.Attributes }}

#### Attributes

| Name | Description | Values |
| ---- | ----------- | ------ |
{{- range $event.Attributes }}
{{- $attribute := . | attributeInfo }}
| {{ $attribute.Name }} | {{ $attribute.Description }} |
{{- if $attribute.Enum }} {{ $attribute.Type }}: ``{{ stringsJoin $attribute.Enum "``, ``" }}``{{ else }} Any {{ $attribute.Type }}{{ end }} |
{{- end }}

{{- end }}

{{- end -}}

{{- define "telemetry-documentation" -}}
{{- $metricName := . }}
{{- $metric := $metricName | telemetryInfo -}}

### {{ if $metric.Prefix -}}{{ $metric.Prefix }}{{- else -}}otelcol_{{- end -}}{{ $metricName }}

{{ $metric.Description }}{{ $metric.Stability }}

{{- if $metric.ExtendedDocumentation }}

{{ $metric.ExtendedDocumentation }}

{{- end }}

| Unit | Metric Type | Value Type |{{ if $metric.Data.HasMonotonic }} Monotonic |{{ end }}
| ---- | ----------- | ---------- |{{ if $metric.Data.HasMonotonic }} --------- |{{ end }}
| {{ $metric.Unit }} | {{ $metric.Data.Type }} | {{ $metric.Data.MetricValueType }} |
{{- if $metric.Data.HasMonotonic }} {{ $metric.Data.Monotonic }} |{{ end }}

{{- if $metric.Attributes }}

#### Attributes

| Name | Description | Values |
| ---- | ----------- | ------ |
{{- range $metric.Attributes }}
{{- $attribute := . | attributeInfo }}
| {{ $attribute.Name }} | {{ $attribute.Description }} |
{{- if $attribute.Enum }} {{ $attribute.Type }}: ``{{ stringsJoin $attribute.Enum "``, ``" }}``{{ else }} Any {{ $attribute.Type }}{{ end }} |
{{- end }}

{{- end }}

{{- end -}}

[comment]: <> (Code generated by mdatagen. DO NOT EDIT.)

# {{ .Type }}

{{- if .Parent }}

**Parent Component:** {{ .Parent }}
{{- end }}

{{- if .Metrics }}

## Default Metrics

The following metrics are emitted by default. Each of them can be disabled by applying the following configuration:

```yaml
metrics:
  <metric_name>:
    enabled: false
```

{{- end }}

{{- range $metricName, $metric := .Metrics }}
{{- if $metric.Enabled }}

{{ template "metric-documentation" $metricName }}

{{- end }}
{{- end }}

{{- $optionalMetricSeen := false }}
{{- range $metricName, $metric := .Metrics }}
{{- if not $metric.Enabled }}
{{- if not $optionalMetricSeen }}

## Optional Metrics

The following metrics are not emitted by default. Each of them can be enabled by applying the following configuration:

```yaml
metrics:
  <metric_name>:
    enabled: true
```

{{- end }}
{{- $optionalMetricSeen = true }}

{{ template "metric-documentation" $metricName }}

{{- end }}
{{- end }}

{{- if .Events }}

## Default Events

The following events are emitted by default. Each of them can be disabled by applying the following configuration:

```yaml
events:
  <event_name>:
    enabled: false
```

{{- range $eventName, $event := .Events }}
{{- if $event.Enabled }}

{{ template "event-documentation" $eventName }}

{{- end }}
{{- end }}
{{- end }}

{{- $optionalEventSeen := false }}
{{- range $eventName, $event := .Events }}
{{- if not $event.Enabled }}
{{- if not $optionalEventSeen }}

## Optional Events

The following events are not emitted by default. Each of them can be enabled by applying the following configuration:

```yaml
events:
  <event_name>:
    enabled: true
```

{{- end }}
{{- $optionalEventSeen = true }}

{{ template "event-documentation" $eventName }}

{{- end }}
{{- end }}

{{- if .ResourceAttributes }}

## Resource Attributes

| Name | Description | Values | Enabled |
| ---- | ----------- | ------ | ------- |
{{- range $attributeName, $attribute := .ResourceAttributes }}
| {{ $attributeName }} | {{ $attribute.Description }} |
{{- if $attribute.Enum }} {{ $attribute.Type }}: ``{{ stringsJoin $attribute.Enum "``, ``" }}``{{ else }} Any {{ $attribute.Type }}{{ end }} | {{ $attribute.Enabled }} |
{{- end }}

{{- end }}

{{- if .Telemetry.Metrics }}

## Internal Telemetry

The following telemetry is emitted by this component.

{{- range $metricName, $metric := .Telemetry.Metrics }}
{{- if $metric.Enabled }}

{{ template "telemetry-documentation" $metricName }}

{{- end }}
{{- end }}

{{- end }}
