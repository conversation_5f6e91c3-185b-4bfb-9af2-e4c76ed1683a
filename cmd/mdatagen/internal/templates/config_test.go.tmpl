// Code generated by mdatagen. DO NOT EDIT.

package {{ .Package }}

import (
	"path/filepath"
	"testing"

	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	"github.com/stretchr/testify/require"

	"go.opentelemetry.io/collector/confmap"
	"go.opentelemetry.io/collector/confmap/confmaptest"
)

{{ if .Metrics }}
func TestMetricsBuilderConfig(t *testing.T) {
	tests := []struct {
		name string
		want MetricsBuilderConfig
	}{
		{
			name: "default",
			want: DefaultMetricsBuilderConfig(),
		},
		{
			name: "all_set",
			want: MetricsBuilderConfig{
				Metrics: MetricsConfig{
					{{- range $name, $_ := .Metrics }}
					{{ $name.Render }}: MetricConfig{Enabled: true},
					{{- end }}
				},
				{{- if .ResourceAttributes }}
				ResourceAttributes: ResourceAttributesConfig{
					{{- range $name, $_ := .ResourceAttributes }}
					{{ $name.Render }}: ResourceAttributeConfig{Enabled: true},
					{{- end }}
				},
				{{- end }}
			},
		},
		{
			name: "none_set",
			want: MetricsBuilderConfig{
				Metrics: MetricsConfig{
					{{- range $name, $_ := .Metrics }}
					{{ $name.Render }}: MetricConfig{Enabled: false},
					{{- end }}
				},
				{{- if .ResourceAttributes }}
				ResourceAttributes: ResourceAttributesConfig{
					{{- range $name, $_ := .ResourceAttributes }}
					{{ $name.Render }}: ResourceAttributeConfig{Enabled: false},
					{{- end }}
				},
				{{- end }}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cfg := loadMetricsBuilderConfig(t, tt.name)
			diff := cmp.Diff(tt.want, cfg, cmpopts.IgnoreUnexported(MetricConfig{}
			{{- if .ResourceAttributes }}, ResourceAttributeConfig{}{{ end }}))
			require.Emptyf(t, diff, "Config mismatch (-expected +actual):\n%s", diff)
		})
	}
}

func loadMetricsBuilderConfig(t *testing.T, name string) MetricsBuilderConfig {
	cm, err := confmaptest.LoadConf(filepath.Join("testdata", "config.yaml"))
	require.NoError(t, err)
	sub, err := cm.Sub(name)
	require.NoError(t, err)
	cfg := DefaultMetricsBuilderConfig()
	require.NoError(t, sub.Unmarshal(&cfg, confmap.WithIgnoreUnused()))
	return cfg
}
{{- end }}

{{ if .Events }}
func loadLogsBuilderConfig(t *testing.T, name string) LogsBuilderConfig {
	cm, err := confmaptest.LoadConf(filepath.Join("testdata", "config.yaml"))
	require.NoError(t, err)
	sub, err := cm.Sub(name)
	require.NoError(t, err)
	cfg := DefaultLogsBuilderConfig()
	require.NoError(t, sub.Unmarshal(&cfg, confmap.WithIgnoreUnused()))
	return cfg
}
{{- end }}

{{ if .ResourceAttributes -}}
func TestResourceAttributesConfig(t *testing.T) {
	tests := []struct {
		name string
		want ResourceAttributesConfig
	}{
		{
			name: "default",
			want: DefaultResourceAttributesConfig(),
		},
		{
			name: "all_set",
			want: ResourceAttributesConfig{
				{{- range $name, $_ := .ResourceAttributes }}
				{{ $name.Render }}: ResourceAttributeConfig{Enabled: true},
				{{- end }}
			},
		},
		{
			name: "none_set",
			want: ResourceAttributesConfig{
				{{- range $name, $_ := .ResourceAttributes }}
				{{ $name.Render }}: ResourceAttributeConfig{Enabled: false},
				{{- end }}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cfg := loadResourceAttributesConfig(t, tt.name)
			diff := cmp.Diff(tt.want, cfg, cmpopts.IgnoreUnexported(ResourceAttributeConfig{}))
			require.Emptyf(t, diff, "Config mismatch (-expected +actual):\n%s", diff)
		})
	}
}

func loadResourceAttributesConfig(t *testing.T, name string) ResourceAttributesConfig {
	cm, err := confmaptest.LoadConf(filepath.Join("testdata", "config.yaml"))
	require.NoError(t, err)
	sub, err := cm.Sub(name)
	require.NoError(t, err)
	sub, err = sub.Sub("resource_attributes")
	require.NoError(t, err)
	cfg := DefaultResourceAttributesConfig()
	require.NoError(t, sub.Unmarshal(&cfg))
	return cfg
}
{{- end }}
