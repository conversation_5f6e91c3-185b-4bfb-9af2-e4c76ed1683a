// Code generated by mdatagen. DO NOT EDIT.

package {{ .Package }}test

import (
    "context"
    "testing"

	"github.com/stretchr/testify/require"
    {{- if .Telemetry.Metrics }}
        {{- range $_, $metric := .Telemetry.Metrics }}
             {{- if $metric.Data.Async }}
    "go.opentelemetry.io/otel/metric"
                {{- break}}
            {{- end }}
        {{- end }}
    {{- end }}
    "go.opentelemetry.io/otel/sdk/metric/metricdata"
    "go.opentelemetry.io/otel/sdk/metric/metricdata/metricdatatest"

    "go.opentelemetry.io/collector/component/componenttest"
)

func TestSetupTelemetry(t *testing.T) {
	testTel := componenttest.NewTelemetry()
	tb, err := {{ .Package }}.NewTelemetryBuilder(testTel.NewTelemetrySettings())
    require.NoError(t, err)
	defer tb.Shutdown()

    {{- range $name, $metric := .Telemetry.Metrics }}
        {{- if $metric.Data.Async }}
    require.NoError(t, tb.Register{{ $name.Render }}Callback(func(_ context.Context, observer metric.{{ casesTitle $metric.Data.BasicType }}Observer) error {
        observer.Observe(1)
        return nil
    }))
        {{- end }}
    {{- end }}

    {{- range $name, $metric := .Telemetry.Metrics }}
        {{- if not $metric.Data.Async }}
            {{- if eq $metric.Data.Type "Sum" }}
            	tb.{{ $name.Render }}.Add(context.Background(), 1)
            {{- else }}
            	tb.{{ $name.Render }}.Record(context.Background(), 1)
            {{- end }}
        {{- end }}
    {{- end }}

    {{- range $name, $metric := .Telemetry.Metrics }}
    AssertEqual{{ $name.Render }}(t, testTel,
        {{ if eq $metric.Data.Type "Gauge" -}}
        []metricdata.DataPoint[{{ $metric.Gauge.MetricValueType.BasicType }}]{{"{{Value: 1}}"}},
        {{- else if eq $metric.Data.Type "Sum" -}}
        []metricdata.DataPoint[{{ $metric.Sum.MetricValueType.BasicType }}]{{"{{Value: 1}}"}},
        {{- else if eq $metric.Data.Type "Histogram" -}}
        []metricdata.HistogramDataPoint[{{ $metric.Histogram.MetricValueType.BasicType }}]{{"{{}}"}}, metricdatatest.IgnoreValue(),
        {{- end }}
        metricdatatest.IgnoreTimestamp())
    {{- end }}

	require.NoError(t, testTel.Shutdown(context.Background()))
}
