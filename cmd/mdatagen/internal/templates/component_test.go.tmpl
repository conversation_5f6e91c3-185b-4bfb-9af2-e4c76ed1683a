// Code generated by mdatagen. DO NOT EDIT.

{{- if len .Status.UnsupportedPlatforms }}
//go:build {{ range $i, $v := .Status.UnsupportedPlatforms }}{{ if $i }} && {{ end }}!{{ . }}{{ end }}
{{- end }}

package {{ .Package }}

import (
	{{- if not (and .Tests.SkipLifecycle .Tests.SkipShutdown) }}
	"context"
	{{- end }}
	"testing"
	{{- if and (not (and .Tests.SkipLifecycle .Tests.SkipShutdown)) (or isExporter isProcessor) }}
	"time"
	{{- end }}

	"github.com/stretchr/testify/require"
	"go.opentelemetry.io/collector/component"
	"go.opentelemetry.io/collector/component/componenttest"
	{{- if not (and .Tests.SkipLifecycle .Tests.SkipShutdown) }}
	"go.opentelemetry.io/collector/confmap/confmaptest"
	{{- if isExporter }}
	"go.opentelemetry.io/collector/exporter"
	"go.opentelemetry.io/collector/exporter/exportertest"
	{{- end }}
	{{- if isProcessor }}
	"go.opentelemetry.io/collector/consumer/consumertest"
	"go.opentelemetry.io/collector/processor"
	"go.opentelemetry.io/collector/processor/processortest"
	{{- end }}
	{{- if isReceiver }}
	"go.opentelemetry.io/collector/consumer/consumertest"
	"go.opentelemetry.io/collector/receiver"
	"go.opentelemetry.io/collector/receiver/receivertest"
	{{- end }}
	{{- if isScraper }}
	"go.opentelemetry.io/collector/scraper"
	"go.opentelemetry.io/collector/scraper/scrapertest"
	{{- end }}
	{{- if isExtension }}
	"go.opentelemetry.io/collector/extension/extensiontest"
	{{- end }}
	{{- if isConnector }}
	"go.opentelemetry.io/collector/connector"
	"go.opentelemetry.io/collector/connector/connectortest"
	"go.opentelemetry.io/collector/consumer"
	"go.opentelemetry.io/collector/consumer/consumertest"
	"go.opentelemetry.io/collector/pipeline"
	{{- end }}
	{{- if or isExporter isProcessor }}
	"go.opentelemetry.io/collector/pdata/pcommon"
	"go.opentelemetry.io/collector/pdata/plog"
	"go.opentelemetry.io/collector/pdata/pmetric"
	"go.opentelemetry.io/collector/pdata/ptrace"
	{{- end }}
	{{- end }}
)

var typ = component.MustNewType("{{ .Type }}")

func TestComponentFactoryType(t *testing.T) {
	require.Equal(t, typ, NewFactory().Type())
}

func TestComponentConfigStruct(t *testing.T) {
	require.NoError(t, componenttest.CheckConfigStruct(NewFactory().CreateDefaultConfig()))
}

{{ if not (and .Tests.SkipLifecycle .Tests.SkipShutdown) -}}
{{ if isExporter -}}
func TestComponentLifecycle(t *testing.T) {
	factory := NewFactory()

	tests := []struct{
		createFn func(ctx context.Context, set exporter.Settings, cfg component.Config) (component.Component, error)
		name string
	}{
{{ if supportsLogs }}
		{
			name: "logs",
			createFn: func(ctx context.Context, set exporter.Settings, cfg component.Config) (component.Component, error) {
				return factory.CreateLogs(ctx, set, cfg)
			},
		},
{{ end }}
{{ if supportsMetrics }}
		{
			name: "metrics",
			createFn: func(ctx context.Context, set exporter.Settings, cfg component.Config) (component.Component, error) {
				return factory.CreateMetrics(ctx, set, cfg)
			},
		},
{{ end }}
{{ if supportsTraces }}
		{
			name: "traces",
			createFn: func(ctx context.Context, set exporter.Settings, cfg component.Config) (component.Component, error) {
				return factory.CreateTraces(ctx, set, cfg)
			},
		},
{{ end }}
	}

	cm, err := confmaptest.LoadConf("metadata.yaml")
	require.NoError(t, err)
	cfg := factory.CreateDefaultConfig()
	sub, err := cm.Sub("tests::config")
	require.NoError(t, err)
	require.NoError(t, sub.Unmarshal(&cfg))

	for _, tt := range tests {
		{{- if not .Tests.SkipShutdown }}
		t.Run(tt.name + "-shutdown", func(t *testing.T) {
			c, err := tt.createFn(context.Background(), exportertest.NewNopSettings(typ), cfg)
			require.NoError(t, err)
			err = c.Shutdown(context.Background())
			require.NoError(t, err)
		})
		{{- end }}

		{{- if not .Tests.SkipLifecycle }}
		t.Run(tt.name + "-lifecycle", func(t *testing.T) {
			c, err := tt.createFn(context.Background(), exportertest.NewNopSettings(typ), cfg)
			require.NoError(t, err)
			host := {{ .Tests.Host }}
			err = c.Start(context.Background(), host)
			require.NoError(t, err)
			require.NotPanics(t, func() {
				switch tt.name {
				case "logs":
					e, ok := c.(exporter.Logs)
					require.True(t, ok)
					logs := generateLifecycleTestLogs()
					if !e.Capabilities().MutatesData {
						logs.MarkReadOnly()
					}
					err = e.ConsumeLogs(context.Background(), logs)
				case "metrics":
					e, ok := c.(exporter.Metrics)
					require.True(t, ok)
					metrics := generateLifecycleTestMetrics()
					if !e.Capabilities().MutatesData {
						metrics.MarkReadOnly()
					}
					err = e.ConsumeMetrics(context.Background(), metrics)
				case "traces":
					e, ok := c.(exporter.Traces)
					require.True(t, ok)
					traces := generateLifecycleTestTraces()
					if !e.Capabilities().MutatesData {
						traces.MarkReadOnly()
					}
					err = e.ConsumeTraces(context.Background(), traces)
				}
			})
			{{ if not expectConsumerError }}
			require.NoError(t, err)
			{{ end }}
			err = c.Shutdown(context.Background())
			require.NoError(t, err)
		})
		{{- end }}
	}
}
{{ end }}

{{ if isProcessor }}
func TestComponentLifecycle(t *testing.T) {
	factory := NewFactory()

	tests := []struct{
		createFn func(ctx context.Context, set processor.Settings, cfg component.Config) (component.Component, error)
		name string
	}{
{{ if supportsLogs }}
		{
			name: "logs",
			createFn: func(ctx context.Context, set processor.Settings, cfg component.Config) (component.Component, error) {
				return factory.CreateLogs(ctx, set, cfg, consumertest.NewNop())
			},
		},
{{ end }}
{{ if supportsMetrics }}
		{
			name: "metrics",
			createFn: func(ctx context.Context, set processor.Settings, cfg component.Config) (component.Component, error) {
				return factory.CreateMetrics(ctx, set, cfg, consumertest.NewNop())
			},
		},
{{ end }}
{{ if supportsTraces }}
		{
			name: "traces",
			createFn: func(ctx context.Context, set processor.Settings, cfg component.Config) (component.Component, error) {
				return factory.CreateTraces(ctx, set, cfg, consumertest.NewNop())
			},
		},
{{ end }}
	}

	cm, err := confmaptest.LoadConf("metadata.yaml")
	require.NoError(t, err)
	cfg := factory.CreateDefaultConfig()
	sub, err := cm.Sub("tests::config")
	require.NoError(t, err)
	require.NoError(t, sub.Unmarshal(&cfg))

	for _, tt := range tests {
		{{- if not .Tests.SkipShutdown }}
		t.Run(tt.name + "-shutdown", func(t *testing.T) {
			c, err := tt.createFn(context.Background(), processortest.NewNopSettings(typ), cfg)
			require.NoError(t, err)
			err = c.Shutdown(context.Background())
			require.NoError(t, err)
		})
		{{- end }}

		{{- if not .Tests.SkipLifecycle }}
		t.Run(tt.name + "-lifecycle", func(t *testing.T) {
			c, err := tt.createFn(context.Background(), processortest.NewNopSettings(typ), cfg)
			require.NoError(t, err)
			host := {{ .Tests.Host }}
			err = c.Start(context.Background(), host)
			require.NoError(t, err)
			require.NotPanics(t, func() {
				switch tt.name {
				case "logs":
					e, ok := c.(processor.Logs)
					require.True(t, ok)
					logs := generateLifecycleTestLogs()
					if !e.Capabilities().MutatesData {
						logs.MarkReadOnly()
					}
					err = e.ConsumeLogs(context.Background(), logs)
				case "metrics":
					e, ok := c.(processor.Metrics)
					require.True(t, ok)
					metrics := generateLifecycleTestMetrics()
					if !e.Capabilities().MutatesData {
						metrics.MarkReadOnly()
					}
					err = e.ConsumeMetrics(context.Background(), metrics)
				case "traces":
					e, ok := c.(processor.Traces)
					require.True(t, ok)
					traces := generateLifecycleTestTraces()
					if !e.Capabilities().MutatesData {
						traces.MarkReadOnly()
					}
					err = e.ConsumeTraces(context.Background(), traces)
				}
			})
			require.NoError(t, err)
			err = c.Shutdown(context.Background())
			require.NoError(t, err)
		})
		{{- end }}
	}
}
{{ end }}

{{ if isReceiver }}
func TestComponentLifecycle(t *testing.T) {
	factory := NewFactory()

	tests := []struct{
		createFn func(ctx context.Context, set receiver.Settings, cfg component.Config) (component.Component, error)
		name string
	}{
{{ if supportsLogs }}
		{
			name: "logs",
			createFn: func(ctx context.Context, set receiver.Settings, cfg component.Config) (component.Component, error) {
				return factory.CreateLogs(ctx, set, cfg, consumertest.NewNop())
			},
		},
{{ end }}
{{ if supportsMetrics }}
		{
			name: "metrics",
			createFn: func(ctx context.Context, set receiver.Settings, cfg component.Config) (component.Component, error) {
				return factory.CreateMetrics(ctx, set, cfg, consumertest.NewNop())
			},
		},
{{ end }}
{{ if supportsTraces }}
		{
			name: "traces",
			createFn: func(ctx context.Context, set receiver.Settings, cfg component.Config) (component.Component, error) {
				return factory.CreateTraces(ctx, set, cfg, consumertest.NewNop())
			},
		},
{{ end }}
	}

	cm, err := confmaptest.LoadConf("metadata.yaml")
	require.NoError(t, err)
	cfg := factory.CreateDefaultConfig()
	sub, err := cm.Sub("tests::config")
	require.NoError(t, err)
	require.NoError(t, sub.Unmarshal(&cfg))

	for _, tt := range tests {
		{{- if not .Tests.SkipShutdown }}
		t.Run(tt.name + "-shutdown", func(t *testing.T) {
			c, err := tt.createFn(context.Background(), receivertest.NewNopSettings(typ), cfg)
			require.NoError(t, err)
			err = c.Shutdown(context.Background())
			require.NoError(t, err)
		})
		{{- end }}

		{{- if not .Tests.SkipLifecycle }}
		t.Run(tt.name + "-lifecycle", func(t *testing.T) {
			firstRcvr, err := tt.createFn(context.Background(), receivertest.NewNopSettings(typ), cfg)
			require.NoError(t, err)
			host := {{ .Tests.Host }}
			require.NoError(t, err)
			require.NoError(t, firstRcvr.Start(context.Background(), host))
			require.NoError(t, firstRcvr.Shutdown(context.Background()))
			secondRcvr, err := tt.createFn(context.Background(), receivertest.NewNopSettings(typ), cfg)
			require.NoError(t, err)
			require.NoError(t, secondRcvr.Start(context.Background(), host))
			require.NoError(t, secondRcvr.Shutdown(context.Background()))
		})
		{{- end }}
	}
}
{{ end }}

{{ if isScraper }}
func TestComponentLifecycle(t *testing.T) {
	factory := NewFactory()

	tests := []struct{
		createFn func(ctx context.Context, set scraper.Settings, cfg component.Config) (component.Component, error)
		name string
	}{
{{ if supportsLogs }}
		{
			name: "logs",
			createFn: func(ctx context.Context, set scraper.Settings, cfg component.Config) (component.Component, error) {
				return factory.CreateLogs(ctx, set, cfg)
			},
		},
{{ end }}
{{ if supportsMetrics }}
		{
			name: "metrics",
			createFn: func(ctx context.Context, set scraper.Settings, cfg component.Config) (component.Component, error) {
				return factory.CreateMetrics(ctx, set, cfg)
			},
		},
{{ end }}
{{ if supportsTraces }}
		{
			name: "traces",
			createFn: func(ctx context.Context, set scraper.Settings, cfg component.Config) (component.Component, error) {
				return factory.CreateTraces(ctx, set, cfg)
			},
		},
{{ end }}
	}

	cm, err := confmaptest.LoadConf("metadata.yaml")
	require.NoError(t, err)
	cfg := factory.CreateDefaultConfig()
	sub, err := cm.Sub("tests::config")
	require.NoError(t, err)
	require.NoError(t, sub.Unmarshal(&cfg))

	for _, tt := range tests {
		{{- if not .Tests.SkipShutdown }}
		t.Run(tt.name + "-shutdown", func(t *testing.T) {
			c, err := tt.createFn(context.Background(), scrapertest.NewNopSettings(typ), cfg)
			require.NoError(t, err)
			err = c.Shutdown(context.Background())
			require.NoError(t, err)
		})
		{{- end }}

		{{- if not .Tests.SkipLifecycle }}
		t.Run(tt.name + "-lifecycle", func(t *testing.T) {
			firstRcvr, err := tt.createFn(context.Background(), scrapertest.NewNopSettings(typ), cfg)
			require.NoError(t, err)
			host := {{ .Tests.Host }}
			require.NoError(t, err)
			require.NoError(t, firstRcvr.Start(context.Background(), host))
			require.NoError(t, firstRcvr.Shutdown(context.Background()))
			secondRcvr, err := tt.createFn(context.Background(), scrapertest.NewNopSettings(typ), cfg)
			require.NoError(t, err)
			require.NoError(t, secondRcvr.Start(context.Background(), host))
			require.NoError(t, secondRcvr.Shutdown(context.Background()))
		})
		{{- end }}
	}
}
{{ end }}

{{ if isExtension }}
func TestComponentLifecycle(t *testing.T) {
	factory := NewFactory()

	cm, err := confmaptest.LoadConf("metadata.yaml")
	require.NoError(t, err)
	cfg := factory.CreateDefaultConfig()
	sub, err := cm.Sub("tests::config")
	require.NoError(t, err)
	require.NoError(t, sub.Unmarshal(&cfg))

	{{- if not .Tests.SkipShutdown }}
	t.Run("shutdown", func(t *testing.T) {
		e, err := factory.Create(context.Background(), extensiontest.NewNopSettings(typ), cfg)
		require.NoError(t, err)
		err = e.Shutdown(context.Background())
		require.NoError(t, err)
	})
	{{- end }}

	{{- if not .Tests.SkipLifecycle }}
	t.Run("lifecycle", func(t *testing.T) {
		firstExt, err := factory.Create(context.Background(), extensiontest.NewNopSettings(typ), cfg)
		require.NoError(t, err)
		require.NoError(t, firstExt.Start(context.Background(), {{ .Tests.Host }}))
		require.NoError(t, firstExt.Shutdown(context.Background()))

		secondExt, err := factory.Create(context.Background(), extensiontest.NewNopSettings(typ), cfg)
		require.NoError(t, err)
		require.NoError(t, secondExt.Start(context.Background(), {{ .Tests.Host }}))
		require.NoError(t, secondExt.Shutdown(context.Background()))
	})
	{{- end }}
}
{{ end }}

{{ if isConnector }}
func TestComponentLifecycle(t *testing.T) {
	factory := NewFactory()

	tests := []struct{
		createFn func(ctx context.Context, set connector.Settings, cfg component.Config) (component.Component, error)
		name string
	}{
{{ if supportsLogsToLogs }}
		{
			name: "logs_to_logs",
			createFn: func(ctx context.Context, set connector.Settings, cfg component.Config) (component.Component, error) {
				router := connector.NewLogsRouter(map[pipeline.ID]consumer.Logs{pipeline.NewID(pipeline.SignalLogs): consumertest.NewNop()})
				return factory.CreateLogsToLogs(ctx, set, cfg, router)
			},
		},
{{ end }}
{{ if supportsLogsToMetrics }}
		{
			name: "logs_to_metrics",
			createFn: func(ctx context.Context, set connector.Settings, cfg component.Config) (component.Component, error) {
				router := connector.NewMetricsRouter(map[pipeline.ID]consumer.Metrics{pipeline.NewID(pipeline.SignalMetrics): consumertest.NewNop()})
				return factory.CreateLogsToMetrics(ctx, set, cfg, router)
			},
		},
{{ end }}
{{ if supportsLogsToTraces }}
		{
			name: "logs_to_traces",
			createFn: func(ctx context.Context, set connector.Settings, cfg component.Config) (component.Component, error) {
				router := connector.NewTracesRouter(map[pipeline.ID]consumer.Traces{pipeline.NewID(pipeline.SignalTraces): consumertest.NewNop()})
				return factory.CreateLogsToTraces(ctx, set, cfg, router)
			},
		},
{{ end }}
{{ if supportsMetricsToLogs }}
		{
			name: "metrics_to_logs",
			createFn: func(ctx context.Context, set connector.Settings, cfg component.Config) (component.Component, error) {
				router := connector.NewLogsRouter(map[pipeline.ID]consumer.Logs{pipeline.NewID(pipeline.SignalLogs): consumertest.NewNop()})
				return factory.CreateMetricsToLogs(ctx, set, cfg, router)
			},
		},
{{ end }}
{{ if supportsMetricsToMetrics }}
		{
			name: "metrics_to_metrics",
			createFn: func(ctx context.Context, set connector.Settings, cfg component.Config) (component.Component, error) {
				router := connector.NewMetricsRouter(map[pipeline.ID]consumer.Metrics{pipeline.NewID(pipeline.SignalMetrics): consumertest.NewNop()})
				return factory.CreateMetricsToMetrics(ctx, set, cfg, router)
			},
		},
{{ end }}
{{ if supportsMetricsToTraces }}
		{
			name: "metrics_to_traces",
			createFn: func(ctx context.Context, set connector.Settings, cfg component.Config) (component.Component, error) {
				router := connector.NewTracesRouter(map[pipeline.ID]consumer.Traces{pipeline.NewID(pipeline.SignalTraces): consumertest.NewNop()})
				return factory.CreateMetricsToTraces(ctx, set, cfg, router)
			},
		},
{{ end }}
{{ if supportsTracesToLogs }}
		{
			name: "traces_to_logs",
			createFn: func(ctx context.Context, set connector.Settings, cfg component.Config) (component.Component, error) {
				router := connector.NewLogsRouter(map[pipeline.ID]consumer.Logs{pipeline.NewID(pipeline.SignalLogs): consumertest.NewNop()})
				return factory.CreateTracesToLogs(ctx, set, cfg, router)
			},
		},
{{ end }}
{{ if supportsTracesToMetrics }}
		{
			name: "traces_to_metrics",
			createFn: func(ctx context.Context, set connector.Settings, cfg component.Config) (component.Component, error) {
				router := connector.NewMetricsRouter(map[pipeline.ID]consumer.Metrics{pipeline.NewID(pipeline.SignalMetrics): consumertest.NewNop()})
				return factory.CreateTracesToMetrics(ctx, set, cfg, router)
			},
		},
{{ end }}
{{ if supportsTracesToTraces }}
		{
			name: "traces_to_traces",
			createFn: func(ctx context.Context, set connector.Settings, cfg component.Config) (component.Component, error) {
				router := connector.NewTracesRouter(map[pipeline.ID]consumer.Traces{pipeline.NewID(pipeline.SignalTraces): consumertest.NewNop()})
				return factory.CreateTracesToTraces(ctx, set, cfg, router)
			},
		},
{{ end }}
	}

	cm, err := confmaptest.LoadConf("metadata.yaml")
	require.NoError(t, err)
	cfg := factory.CreateDefaultConfig()
	sub, err := cm.Sub("tests::config")
	require.NoError(t, err)
	require.NoError(t, sub.Unmarshal(&cfg))

	for _, tt := range tests {
		{{- if not .Tests.SkipShutdown }}
		t.Run(tt.name + "-shutdown", func(t *testing.T) {
			c, err := tt.createFn(context.Background(), connectortest.NewNopSettings(typ), cfg)
			require.NoError(t, err)
			err = c.Shutdown(context.Background())
			require.NoError(t, err)
		})
		{{- end }}

		{{- if not .Tests.SkipLifecycle }}
		t.Run(tt.name + "-lifecycle", func(t *testing.T) {
			firstConnector, err := tt.createFn(context.Background(), connectortest.NewNopSettings(typ), cfg)
			require.NoError(t, err)
			host := {{ .Tests.Host }}
			require.NoError(t, err)
			require.NoError(t, firstConnector.Start(context.Background(), host))
			require.NoError(t, firstConnector.Shutdown(context.Background()))
			secondConnector, err := tt.createFn(context.Background(), connectortest.NewNopSettings(typ), cfg)
			require.NoError(t, err)
			require.NoError(t, secondConnector.Start(context.Background(), host))
			require.NoError(t, secondConnector.Shutdown(context.Background()))
		})
		{{- end }}
	}
}
{{ end }}

{{ if or isExporter isProcessor -}}
func generateLifecycleTestLogs() plog.Logs {
	logs := plog.NewLogs()
	rl := logs.ResourceLogs().AppendEmpty()
	rl.Resource().Attributes().PutStr("resource", "R1")
	l := rl.ScopeLogs().AppendEmpty().LogRecords().AppendEmpty()
	l.Body().SetStr("test log message")
	l.SetTimestamp(pcommon.NewTimestampFromTime(time.Now()))
	return logs
}

func generateLifecycleTestMetrics() pmetric.Metrics {
	metrics := pmetric.NewMetrics()
	rm := metrics.ResourceMetrics().AppendEmpty()
	rm.Resource().Attributes().PutStr("resource", "R1")
	m := rm.ScopeMetrics().AppendEmpty().Metrics().AppendEmpty()
	m.SetName("test_metric")
	dp := m.SetEmptyGauge().DataPoints().AppendEmpty()
	dp.Attributes().PutStr("test_attr", "value_1")
	dp.SetIntValue(123)
	dp.SetTimestamp(pcommon.NewTimestampFromTime(time.Now()))
	return metrics
}

func generateLifecycleTestTraces() ptrace.Traces {
	traces := ptrace.NewTraces()
	rs := traces.ResourceSpans().AppendEmpty()
	rs.Resource().Attributes().PutStr("resource", "R1")
	span := rs.ScopeSpans().AppendEmpty().Spans().AppendEmpty()
	span.Attributes().PutStr("test_attr", "value_1")
	span.SetName("test_span")
	span.SetStartTimestamp(pcommon.NewTimestampFromTime(time.Now().Add(-1 * time.Second)))
	span.SetEndTimestamp(pcommon.NewTimestampFromTime(time.Now()))
	return traces
}
{{- end }}
{{- end }}
