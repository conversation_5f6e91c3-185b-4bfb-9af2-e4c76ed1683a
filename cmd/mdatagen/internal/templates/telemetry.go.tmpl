// Code generated by mdatagen. DO NOT EDIT.

package {{ .Package }}

import (
    {{- if .Telemetry.Metrics }}
        {{- range $_, $metric := .Telemetry.Metrics }}
            {{- if $metric.Data.Async }}
    "context"
                {{- break}}
            {{- end }}
        {{- end }}
    "errors"
    "sync"
    {{- end }}

    "go.opentelemetry.io/otel/metric"
    {{- if .Telemetry.Metrics }}
        {{- range $_, $metric := .Telemetry.Metrics }}
            {{- if $metric.Data.Async }}
    "go.opentelemetry.io/otel/metric/embedded"
                {{- break}}
            {{- end }}
        {{- end }}
    {{- end }}
    "go.opentelemetry.io/otel/trace"

    "go.opentelemetry.io/collector/component"
)

func Meter(settings component.TelemetrySettings) metric.Meter {
	return settings.MeterProvider.Meter("{{ .ScopeName }}")
}

func Tracer(settings component.TelemetrySettings) trace.Tracer {
	return settings.TracerProvider.Tracer("{{ .ScopeName }}")
}
{{- if .Telemetry.Metrics }}

// TelemetryBuilder provides an interface for components to report telemetry
// as defined in metadata and user config.
type TelemetryBuilder struct {
    meter metric.Meter
	mu sync.Mutex
    registrations []metric.Registration
	{{- range $name, $metric := .Telemetry.Metrics }}
	{{ $name.Render }} metric.{{ $metric.Data.Instrument }}
    {{- if and ($metric.Data.Async) (not $metric.Optional) }}
    {{- end }}
	{{- end }}
}

// TelemetryBuilderOption applies changes to default builder.
type TelemetryBuilderOption interface {
    apply(*TelemetryBuilder)
}

type telemetryBuilderOptionFunc func(mb *TelemetryBuilder)

func (tbof telemetryBuilderOptionFunc) apply(mb *TelemetryBuilder) {
  tbof(mb)
}

{{- range $name, $metric := .Telemetry.Metrics }}
    {{ if $metric.Data.Async -}}
// Register{{ $name.Render }}Callback sets callback for observable {{ $name.Render }} metric.
func (builder *TelemetryBuilder) Register{{ $name.Render }}Callback(cb metric.{{ casesTitle $metric.Data.BasicType }}Callback) error {
    reg, err := builder.meter.RegisterCallback(func(ctx context.Context, o metric.Observer) error {
            cb(ctx, &observer{{ casesTitle $metric.Data.BasicType }}{inst : builder.{{ $name.Render }}, obs: o})
            return nil
        }, builder.{{ $name.Render }})
	if err != nil {
		return err
    }
    builder.mu.Lock()
    defer builder.mu.Unlock()
    builder.registrations = append(builder.registrations, reg)
    return nil
}
    {{- end }}

{{- end }}

{{- range $name, $metric := .Telemetry.Metrics }}
{{- if $metric.Data.Async }}
    {{ if eq $metric.Data.BasicType "int64" -}}
type observerInt64 struct {
	embedded.Int64Observer
    inst metric.Int64Observable
    obs metric.Observer
}

func (oi *observerInt64) Observe(value int64, opts ...metric.ObserveOption) {
    oi.obs.ObserveInt64(oi.inst, value, opts...)
}
        {{ break }}
    {{- end }}
{{- end }}
{{- end }}

{{- range $name, $metric := .Telemetry.Metrics }}
{{- if $metric.Data.Async }}
{{ if eq $metric.Data.BasicType "float64" -}}
type observerFloat64 struct {
    embedded.Float64Observer
    inst metric.Float64Observable
    obs metric.Observer
}

func (oi *observerFloat64) Observe(value float64, opts ...metric.ObserveOption) {
    oi.obs.ObserveFloat64(oi.inst, value, opts...)
}
{{ break }}
{{- end }}
{{- end }}
{{- end }}

// Shutdown unregister all registered callbacks for async instruments.
func (builder *TelemetryBuilder) Shutdown() {
    builder.mu.Lock()
	defer builder.mu.Unlock()
	for _, reg := range builder.registrations {
        reg.Unregister()
    }
}

// NewTelemetryBuilder provides a struct with methods to update all internal telemetry
// for a component
func NewTelemetryBuilder(settings component.TelemetrySettings, options ...TelemetryBuilderOption) (*TelemetryBuilder, error) {
    builder := TelemetryBuilder{}
	for _, op := range options {
		op.apply(&builder)
	}
    builder.meter = Meter(settings)
    var err, errs error

    {{- range $name, $metric := .Telemetry.Metrics }}
    builder.{{ $name.Render }}, err = builder.meter.{{ $metric.Data.Instrument }}(
        {{ if $metric.Prefix -}}
        "{{ $metric.Prefix }}{{ $name }}",
        {{ else -}}
        "otelcol_{{ $name }}",
        {{ end -}}
        metric.WithDescription("{{ $metric.Description }}{{ $metric.Stability }}"),
        metric.WithUnit("{{ $metric.Unit }}"),
        {{ if eq $metric.Data.Type "Histogram" -}}
        {{- if $metric.Data.Boundaries -}}metric.WithExplicitBucketBoundaries([]float64{ {{- range $metric.Data.Boundaries }} {{.}}, {{- end }} }...),{{- end }}
        {{- end }}
    )
    errs = errors.Join(errs, err)
    {{- end }}
    return &builder, errs
}

{{- end }}
