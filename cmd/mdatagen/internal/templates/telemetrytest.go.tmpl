// Code generated by mdatagen. DO NOT EDIT.

package {{ .Package }}test

import (
    "testing"

    "github.com/stretchr/testify/require"
    "go.opentelemetry.io/otel/sdk/metric/metricdata"
    "go.opentelemetry.io/otel/sdk/metric/metricdata/metricdatatest"

    {{- if or isConnector isExporter isExtension isProcessor isReceiver isScraper }}
    "go.opentelemetry.io/collector/component"
    {{- end }}
    "go.opentelemetry.io/collector/component/componenttest"
    {{- if or isConnector isExporter isExtension isProcessor isReceiver isScraper }}
    "go.opentelemetry.io/collector/{{ .Status.Class }}"
    "go.opentelemetry.io/collector/{{ .Status.Class }}/{{ .Status.Class }}test"
    {{- end }}
)

{{ if or isConnector isExporter isExtension isProcessor isReceiver isScraper }}
func NewSettings(tt *componenttest.Telemetry) {{ .Status.Class }}.Settings {
set := {{ .Status.Class }}test.NewNopSettings({{ .Status.Class }}test.NopType)
set.ID = component.NewID(component.MustNewType("{{ .Type }}"))
set.TelemetrySettings = tt.NewTelemetrySettings()
return set
}
{{- end }}

{{ range $name, $metric := .Telemetry.Metrics }}

func AssertEqual{{ $name.Render }}(t *testing.T, tt *componenttest.Telemetry, dps []metricdata.{{- if eq $metric.Data.Type "Histogram" }} {{$metric.Data.Type}} {{- end }}DataPoint[{{ $metric.Data.BasicType }}], opts ...metricdatatest.Option) {
	want := metricdata.Metrics{
		{{ if $metric.Prefix -}}
		Name:        "{{ $metric.Prefix }}{{ $name }}",
		{{ else -}}
		Name:        "otelcol_{{ $name }}",
		{{ end -}}
		Description: "{{ $metric.Description }}{{ $metric.Stability }}",
		Unit:        "{{ $metric.Unit }}",
		Data:        metricdata.{{ $metric.Data.Type }}[{{ $metric.Data.BasicType }}]{
			{{- if $metric.Data.HasAggregated }}
			Temporality: metricdata.CumulativeTemporality,
			{{- end }}
			{{- if $metric.Data.HasMonotonic }}
			IsMonotonic: {{ $metric.Data.Monotonic }},
			{{- end }}
			DataPoints: dps,
		},
	}
	{{ if $metric.Prefix -}}
	got, err := tt.GetMetric("{{ $metric.Prefix }}{{ $name }}")
	{{ else -}}
	got, err := tt.GetMetric("otelcol_{{ $name }}")
	{{ end -}}
	require.NoError(t, err)
	metricdatatest.AssertEqual(t, want, got, opts...)
}

{{- end }}
