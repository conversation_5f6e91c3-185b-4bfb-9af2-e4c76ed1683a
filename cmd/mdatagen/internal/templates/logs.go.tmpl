// Code generated by mdatagen. DO NOT EDIT.

package {{ .Package }}

import (
	"go.opentelemetry.io/collector/component"
	"go.opentelemetry.io/collector/pdata/pcommon"
	"go.opentelemetry.io/collector/pdata/plog"
	{{- if or is<PERSON><PERSON><PERSON><PERSON> isScraper }}
	"go.opentelemetry.io/collector/{{ .Status.Class }}"
	{{- end }}
	{{- if .SemConvVersion }}
	conventions "go.opentelemetry.io/otel/semconv/v{{ .SemConvVersion }}"
	{{- end }}
	{{- if .Events }}
	"context"
	"go.opentelemetry.io/otel/trace"
	"go.opentelemetry.io/collector/filter"
	{{- end }}
)


{{ range $name, $event := .Events -}}
type event{{ $name.Render }} struct {
	data     plog.LogRecordSlice // data buffer for generated log records.
	config   EventConfig         // event config provided by user.
}

func (e *event{{ $name.Render }}) recordEvent(ctx context.Context, timestamp pcommon.Timestamp
{{- range $event.Attributes -}}, {{ .RenderUnexported }}AttributeValue {{ (attributeInfo .).Type.Primitive }}{{ end }}) {
	if !e.config.Enabled {
		return
	}
	lr := e.data.AppendEmpty()
	lr.SetEventName("{{ $name }}")
	lr.SetTimestamp(timestamp)

	if span := trace.SpanContextFromContext(ctx); span.IsValid() {
		lr.SetTraceID(pcommon.TraceID(span.TraceID()))
		lr.SetSpanID(pcommon.SpanID(span.SpanID()))
	}

	{{ range $event.Attributes }}
	{{- if eq (attributeInfo .).Type.Primitive "[]byte" }}
	lr.Attributes().PutEmptyBytes("{{ (attributeInfo .).Name }}").FromRaw({{ .RenderUnexported }}AttributeValue)
	{{- else if eq (attributeInfo .).Type.Primitive "[]any" }}
	lr.Attributes().PutEmptySlice("{{ (attributeInfo .).Name }}").FromRaw({{ .RenderUnexported }}AttributeValue)
	{{- else if eq (attributeInfo .).Type.Primitive "map[string]any" }}
	lr.Attributes().PutEmptyMap("{{ (attributeInfo .).Name }}").FromRaw({{ .RenderUnexported }}AttributeValue)
	{{- else }}
	lr.Attributes().Put{{ (attributeInfo .).Type }}("{{ (attributeInfo .).Name }}", {{ .RenderUnexported }}AttributeValue)
	{{- end }}
	{{- end }}
}

// emit appends recorded event data to a events slice and prepares it for recording another set of log records.
func (e *event{{ $name.Render }}) emit(lrs plog.LogRecordSlice) {
    if e.config.Enabled && e.data.Len() > 0 {
		e.data.MoveAndAppendTo(lrs)
	}
}

func newEvent{{ $name.Render }}(cfg EventConfig) event{{ $name.Render }} {
	e := event{{ $name.Render }}{config: cfg}
	if cfg.Enabled {
		e.data = plog.NewLogRecordSlice()
	}
	return e
}

{{ end -}}

// LogsBuilder provides an interface for scrapers to report logs while taking care of all the transformations
// required to produce log representation defined in metadata and user config.
type LogsBuilder struct {
    {{- if .Events }}
	config              LogsBuilderConfig    // config of the logs builder.
	{{- end }}
	logsBuffer          plog.Logs
	logRecordsBuffer    plog.LogRecordSlice
	buildInfo           component.BuildInfo  // contains version information.
	{{- if and .Events .ResourceAttributes }}
	resourceAttributeIncludeFilter map[string]filter.Filter
	resourceAttributeExcludeFilter map[string]filter.Filter
	{{- end }}
	{{- range $name, $event := .Events }}
	event{{ $name.Render }} event{{ $name.Render }}
	{{- end }}
}

// LogBuilderOption applies changes to default logs builder.
type LogBuilderOption interface {
    apply(*LogsBuilder)
}

{{- if or isReceiver isScraper }}
func NewLogsBuilder({{ if .Events }}lbc LogsBuilderConfig, {{ end }}settings {{ .Status.Class }}.Settings) *LogsBuilder {
	{{- range $name, $event := .Events }}
	{{- if $event.Warnings.IfEnabled }}
	if lbc.Events.{{ $name.Render }}.Enabled {
		settings.Logger.Warn("[WARNING] `{{ $name }}` should not be enabled: {{ $event.Warnings.IfEnabled }}")
	}
	{{- end }}
	{{- if $event.Warnings.IfEnabledNotSet }}
	if !lbc.Events.{{ $name.Render }}.enabledSetByUser {
		settings.Logger.Warn("[WARNING] Please set `enabled` field explicitly for `{{ $name }}`: {{ $event.Warnings.IfEnabledNotSet }}")
	}
	{{- end }}
	{{- if $event.Warnings.IfConfigured }}
	if lbc.Events.{{ $name.Render }}.enabledSetByUser {
		settings.Logger.Warn("[WARNING] `{{ $name }}` should not be configured: {{ $event.Warnings.IfConfigured }}")
	}
	{{- end }}
	{{- end }}
	{{- if .Events }}
	{{- range $name, $attr := .ResourceAttributes }}
	{{- if $attr.Warnings.IfEnabled }}
	if lbc.ResourceAttributes.{{ $name.Render }}.Enabled {
		settings.Logger.Warn("[WARNING] `{{ $name }}` should not be enabled: {{ $attr.Warnings.IfEnabled }}")
	}
	{{- end }}
	{{- if $attr.Warnings.IfEnabledNotSet }}
	if !lbc.ResourceAttributes.{{ $name.Render }}.enabledSetByUser {
		settings.Logger.Warn("[WARNING] Please set `enabled` field explicitly for `{{ $name }}`: {{ $attr.Warnings.IfEnabledNotSet }}")
	}
	{{- end }}
	{{- if $attr.Warnings.IfConfigured }}
	if lbc.ResourceAttributes.{{ $name.Render }}.enabledSetByUser || lbc.ResourceAttributes.{{ $name.Render }}.EventsInclude != nil || lbc.ResourceAttributes.{{ $name.Render }}.EventsExclude != nil {
		settings.Logger.Warn("[WARNING] `{{ $name }}` should not be configured: {{ $attr.Warnings.IfConfigured }}")
	}
	{{- end }}
	{{- end }}
	{{- end }}
	lb := &LogsBuilder{
		{{- if .Events }}
		config:        lbc,
		{{- end }}
		logsBuffer:     plog.NewLogs(),
		logRecordsBuffer: plog.NewLogRecordSlice(),
		buildInfo:      settings.BuildInfo,
		{{- range $name, $event := .Events }}
		event{{ $name.Render }}: newEvent{{ $name.Render }}(lbc.Events.{{ $name.Render }}),
		{{- end }}
		{{ if and .Events .ResourceAttributes -}}
		resourceAttributeIncludeFilter: make(map[string]filter.Filter),
		resourceAttributeExcludeFilter: make(map[string]filter.Filter),
		{{- end }}
	}
	{{- if .Events }}
	{{- range $name, $attr := .ResourceAttributes }}
	if lbc.ResourceAttributes.{{ $name.Render }}.EventsInclude != nil {
		lb.resourceAttributeIncludeFilter["{{ $name }}"] = filter.CreateFilter(lbc.ResourceAttributes.{{ $name.Render }}.EventsInclude)
	}
	if lbc.ResourceAttributes.{{ $name.Render }}.EventsExclude != nil {
		lb.resourceAttributeExcludeFilter["{{ $name }}"] = filter.CreateFilter(lbc.ResourceAttributes.{{ $name.Render }}.EventsExclude)
	}
	{{- end }}
	{{- end }}

	return lb
}
{{- end }}

{{- if .ResourceAttributes }}
// NewResourceBuilder returns a new resource builder that should be used to build a resource associated with for the emitted logs.
func (lb *LogsBuilder) NewResourceBuilder() *ResourceBuilder {
	return NewResourceBuilder({{ if .Events }}lb.config.ResourceAttributes{{ else }}ResourceAttributesConfig{}{{ end }})
}
{{- end }}

// ResourceLogsOption applies changes to provided resource logs.
type ResourceLogsOption interface {
    apply(plog.ResourceLogs)
}

type resourceLogsOptionFunc func(plog.ResourceLogs)

func (rlof resourceLogsOptionFunc) apply(rl plog.ResourceLogs) {
    rlof(rl)
}

// WithLogsResource sets the provided resource on the emitted ResourceLogs.
// It's recommended to use ResourceBuilder to create the resource.
func WithLogsResource(res pcommon.Resource) ResourceLogsOption {
	return resourceLogsOptionFunc(func(rl plog.ResourceLogs) {
		res.CopyTo(rl.Resource())
	})
}

// AppendLogRecord adds a log record to the logs builder.
func (lb *LogsBuilder) AppendLogRecord(lr plog.LogRecord) {
	lr.MoveTo(lb.logRecordsBuffer.AppendEmpty())
}

// EmitForResource saves all the generated logs under a new resource and updates the internal state to be ready for
// recording another set of log records as part of another resource. This function can be helpful when one scraper
// needs to emit logs from several resources. Otherwise calling this function is not required,
// just `Emit` function can be called instead.
// Resource attributes should be provided as ResourceLogsOption arguments.
func (lb *LogsBuilder) EmitForResource(options ...ResourceLogsOption) {
	rl := plog.NewResourceLogs()
	{{- if .SemConvVersion }}
	rl.SetSchemaUrl(conventions.SchemaURL)
	{{- end }}
	ils := rl.ScopeLogs().AppendEmpty()
	ils.Scope().SetName(ScopeName)
	ils.Scope().SetVersion(lb.buildInfo.Version)

    {{- range $name, $event := .Events }}
	lb.event{{- $name.Render }}.emit(ils.LogRecords())
	{{- end }}

	for _, op := range options {
		op.apply(rl)
	}

    if lb.logRecordsBuffer.Len() > 0 {
        lb.logRecordsBuffer.MoveAndAppendTo(ils.LogRecords())
        lb.logRecordsBuffer = plog.NewLogRecordSlice()
    }

    {{ if and .Events .ResourceAttributes -}}
	for attr, filter := range lb.resourceAttributeIncludeFilter {
		if val, ok := rl.Resource().Attributes().Get(attr); ok && !filter.Matches(val.AsString()) {
			return
		}
	}
	for attr, filter := range lb.resourceAttributeExcludeFilter {
		if val, ok := rl.Resource().Attributes().Get(attr); ok && filter.Matches(val.AsString()) {
			return
		}
	}
	{{- end }}

	if ils.LogRecords().Len() > 0 {
		rl.MoveTo(lb.logsBuffer.ResourceLogs().AppendEmpty())
	}
}

// Emit returns all the logs accumulated by the logs builder and updates the internal state to be ready for
// recording another set of logs. This function will be responsible for applying all the transformations required to
// produce logs representation defined in metadata and user config.
func (lb *LogsBuilder) Emit(options ...ResourceLogsOption) plog.Logs {
	lb.EmitForResource(options...)
    logs := lb.logsBuffer
    lb.logsBuffer = plog.NewLogs()
	return logs
}

{{ range $name, $event := .Events -}}
// Record{{ $name.Render }}Event adds a log record of {{ $name }} event.
func (lb *LogsBuilder) Record{{ $name.Render }}Event(ctx context.Context, timestamp pcommon.Timestamp
	{{- range $event.Attributes -}}
	, {{ .RenderUnexported }}AttributeValue {{ if (attributeInfo .).Enum }}Attribute{{ .Render }}{{ else }}{{ (attributeInfo .).Type.Primitive }}{{ end }}
	{{- end }}) {
	lb.event{{ $name.Render }}.recordEvent(ctx, timestamp
		{{- range $event.Attributes -}}
		, {{ .RenderUnexported }}AttributeValue{{ if (attributeInfo .).Enum }}.String(){{ end }}
		{{- end }})
}
{{ end }}
