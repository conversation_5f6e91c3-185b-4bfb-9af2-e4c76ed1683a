// Code generated by mdatagen. DO NOT EDIT.

package {{ .Package }}

import (
	"go.opentelemetry.io/collector/component"
)

var (
	Type      = component.MustNewType("{{ .Type }}")
	ScopeName = "{{ .ScopeName }}"
)

const (
	{{- range $stability, $signals := .Status.Stability }}
	{{- range $signal := $signals }}
	{{ toCamelCase $signal }}Stability = component.StabilityLevel{{ casesTitle $stability.String }}
	{{- end }}
	{{- end }}
)
