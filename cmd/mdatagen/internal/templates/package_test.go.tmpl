// Code generated by mdatagen. DO NOT EDIT.

package {{ if isCommand -}}main{{ else }}{{ .Package }}{{- end }}

import (
    {{- if .Tests.GoLeak.Skip }}
    "os"
    {{- end }}
	"testing"

    {{- if not .Tests.GoLeak.Skip }}
    "go.uber.org/goleak"
    {{- end }}
)

func TestMain(m *testing.M) {
    {{- if .Tests.GoLeak.Setup }}
    {{.Tests.GoLeak.Setup}}
    {{- end }}
    {{- if .Tests.GoLeak.Skip }}
    // skipping goleak test as per metadata.yml configuration
    os.Exit(m.Run())
    {{- else }}
	goleak.VerifyTestMain(m {{- range $val := .Tests.GoLeak.Ignore.Top}}, goleak.IgnoreTopFunction("{{$val}}"){{end}}{{- range $val := .Tests.GoLeak.Ignore.Any}}, goleak.IgnoreAnyFunction("{{$val}}"){{end}} )
    {{- end }}
    {{- if .Tests.GoLeak.Teardown }}
    {{.Tests.GoLeak.Teardown}}
    {{- end }}
}
