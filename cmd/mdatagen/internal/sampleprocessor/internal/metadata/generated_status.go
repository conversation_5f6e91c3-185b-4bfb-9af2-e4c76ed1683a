// Code generated by mdatagen. DO NOT EDIT.

package metadata

import (
	"go.opentelemetry.io/collector/component"
)

var (
	Type      = component.MustNewType("sample")
	ScopeName = "go.opentelemetry.io/collector/internal/receiver/samplereceiver"
)

const (
	LogsStability    = component.StabilityLevelDevelopment
	TracesStability  = component.StabilityLevelBeta
	MetricsStability = component.StabilityLevelStable
)
