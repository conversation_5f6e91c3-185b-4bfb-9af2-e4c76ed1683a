[comment]: <> (Code generated by mdatagen. DO NOT EDIT.)

# sample

## Resource Attributes

| Name | Description | Values | Enabled |
| ---- | ----------- | ------ | ------- |
| map.resource.attr | Resource attribute with a map value. | Any Map | true |
| optional.resource.attr | Explicitly disabled ResourceAttribute. | Any Str | false |
| slice.resource.attr | Resource attribute with a slice value. | Any Slice | true |
| string.enum.resource.attr | Resource attribute with a known set of string values. | Str: ``one``, ``two`` | true |
| string.resource.attr | Resource attribute with any string value. | Any Str | true |
| string.resource.attr_disable_warning | Resource attribute with any string value. | Any Str | true |
| string.resource.attr_remove_warning | Resource attribute with any string value. | Any Str | false |
| string.resource.attr_to_be_removed | Resource attribute with any string value. | Any Str | true |
