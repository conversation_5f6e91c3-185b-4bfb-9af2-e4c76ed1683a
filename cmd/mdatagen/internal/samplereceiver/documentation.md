[comment]: <> (Code generated by mdatagen. DO NOT EDIT.)

# sample

## Default Metrics

The following metrics are emitted by default. Each of them can be disabled by applying the following configuration:

```yaml
metrics:
  <metric_name>:
    enabled: false
```

### default.metric

Monotonic cumulative sum int metric enabled by default.

The metric will be become optional soon.

| Unit | Metric Type | Value Type | Aggregation Temporality | Monotonic |
| ---- | ----------- | ---------- | ----------------------- | --------- |
| s | Sum | Int | Cumulative | true |

#### Attributes

| Name | Description | Values |
| ---- | ----------- | ------ |
| string_attr | Attribute with any string value. | Any Str |
| state | Integer attribute with overridden name. | Any Int |
| enum_attr | Attribute with a known set of string values. | Str: ``red``, ``green``, ``blue`` |
| slice_attr | Attribute with a slice value. | Any Slice |
| map_attr | Attribute with a map value. | Any Map |

### default.metric.to_be_removed

[DEPRECATED] Non-monotonic delta sum double metric enabled by default.

The metric will be removed soon.

| Unit | Metric Type | Value Type | Aggregation Temporality | Monotonic |
| ---- | ----------- | ---------- | ----------------------- | --------- |
| s | Sum | Double | Delta | false |

### metric.input_type

Monotonic cumulative sum int metric with string input_type enabled by default.

| Unit | Metric Type | Value Type | Aggregation Temporality | Monotonic |
| ---- | ----------- | ---------- | ----------------------- | --------- |
| s | Sum | Int | Cumulative | true |

#### Attributes

| Name | Description | Values |
| ---- | ----------- | ------ |
| string_attr | Attribute with any string value. | Any Str |
| state | Integer attribute with overridden name. | Any Int |
| enum_attr | Attribute with a known set of string values. | Str: ``red``, ``green``, ``blue`` |
| slice_attr | Attribute with a slice value. | Any Slice |
| map_attr | Attribute with a map value. | Any Map |

## Optional Metrics

The following metrics are not emitted by default. Each of them can be enabled by applying the following configuration:

```yaml
metrics:
  <metric_name>:
    enabled: true
```

### optional.metric

[DEPRECATED] Gauge double metric disabled by default.

| Unit | Metric Type | Value Type |
| ---- | ----------- | ---------- |
| 1 | Gauge | Double |

#### Attributes

| Name | Description | Values |
| ---- | ----------- | ------ |
| string_attr | Attribute with any string value. | Any Str |
| boolean_attr | Attribute with a boolean value. | Any Bool |
| boolean_attr2 | Another attribute with a boolean value. | Any Bool |

### optional.metric.empty_unit

[DEPRECATED] Gauge double metric disabled by default.

| Unit | Metric Type | Value Type |
| ---- | ----------- | ---------- |
|  | Gauge | Double |

#### Attributes

| Name | Description | Values |
| ---- | ----------- | ------ |
| string_attr | Attribute with any string value. | Any Str |
| boolean_attr | Attribute with a boolean value. | Any Bool |

## Default Events

The following events are emitted by default. Each of them can be disabled by applying the following configuration:

```yaml
events:
  <event_name>:
    enabled: false
```

### default.event

Example event enabled by default.

#### Attributes

| Name | Description | Values |
| ---- | ----------- | ------ |
| string_attr | Attribute with any string value. | Any Str |
| state | Integer attribute with overridden name. | Any Int |
| enum_attr | Attribute with a known set of string values. | Str: ``red``, ``green``, ``blue`` |
| slice_attr | Attribute with a slice value. | Any Slice |
| map_attr | Attribute with a map value. | Any Map |

### default.event.to_be_removed

[DEPRECATED] Example to-be-removed event enabled by default.

The event will be removed soon.

#### Attributes

| Name | Description | Values |
| ---- | ----------- | ------ |
| string_attr | Attribute with any string value. | Any Str |
| state | Integer attribute with overridden name. | Any Int |
| enum_attr | Attribute with a known set of string values. | Str: ``red``, ``green``, ``blue`` |
| slice_attr | Attribute with a slice value. | Any Slice |
| map_attr | Attribute with a map value. | Any Map |

## Optional Events

The following events are not emitted by default. Each of them can be enabled by applying the following configuration:

```yaml
events:
  <event_name>:
    enabled: true
```

### default.event.to_be_renamed

[DEPRECATED] Example event disabled by default.

The event will be renamed soon.

#### Attributes

| Name | Description | Values |
| ---- | ----------- | ------ |
| string_attr | Attribute with any string value. | Any Str |
| boolean_attr | Attribute with a boolean value. | Any Bool |
| boolean_attr2 | Another attribute with a boolean value. | Any Bool |

## Resource Attributes

| Name | Description | Values | Enabled |
| ---- | ----------- | ------ | ------- |
| map.resource.attr | Resource attribute with a map value. | Any Map | true |
| optional.resource.attr | Explicitly disabled ResourceAttribute. | Any Str | false |
| slice.resource.attr | Resource attribute with a slice value. | Any Slice | true |
| string.enum.resource.attr | Resource attribute with a known set of string values. | Str: ``one``, ``two`` | true |
| string.resource.attr | Resource attribute with any string value. | Any Str | true |
| string.resource.attr_disable_warning | Resource attribute with any string value. | Any Str | true |
| string.resource.attr_remove_warning | Resource attribute with any string value. | Any Str | false |
| string.resource.attr_to_be_removed | Resource attribute with any string value. | Any Str | true |

## Internal Telemetry

The following telemetry is emitted by this component.

### otelcol_batch_size_trigger_send

Number of times the batch was sent due to a size trigger [deprecated since v0.110.0]

| Unit | Metric Type | Value Type | Monotonic |
| ---- | ----------- | ---------- | --------- |
| {times} | Sum | Int | true |

### otelcol_process_runtime_total_alloc_bytes

Cumulative bytes allocated for heap objects (see 'go doc runtime.MemStats.TotalAlloc')

| Unit | Metric Type | Value Type | Monotonic |
| ---- | ----------- | ---------- | --------- |
| By | Sum | Int | true |

### otelcol_queue_capacity

Queue capacity - sync gauge example.

| Unit | Metric Type | Value Type |
| ---- | ----------- | ---------- |
| {items} | Gauge | Int |

### otelcol_queue_length

This metric is optional and therefore not initialized in NewTelemetryBuilder. [alpha]

For example this metric only exists if feature A is enabled.

| Unit | Metric Type | Value Type |
| ---- | ----------- | ---------- |
| {items} | Gauge | Int |

### otelcol_request_duration

Duration of request [alpha]

| Unit | Metric Type | Value Type |
| ---- | ----------- | ---------- |
| s | Histogram | Double |
