// Code generated by mdatagen. DO NOT EDIT.

package metadata

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
	"go.uber.org/zap/zaptest/observer"

	"go.opentelemetry.io/collector/pdata/pcommon"
	"go.opentelemetry.io/collector/pdata/plog"
	"go.opentelemetry.io/collector/scraper/scrapertest"
)

func TestLogsBuilderAppendLogRecord(t *testing.T) {
	observedZapCore, _ := observer.New(zap.WarnLevel)
	settings := scrapertest.NewNopSettings(scrapertest.NopType)
	settings.Logger = zap.New(observedZapCore)
	lb := NewLogsBuilder(settings)

	rb := lb.NewResourceBuilder()
	rb.SetMapResourceAttr(map[string]any{"key1": "map.resource.attr-val1", "key2": "map.resource.attr-val2"})
	rb.SetOptionalResourceAttr("optional.resource.attr-val")
	rb.SetSliceResourceAttr([]any{"slice.resource.attr-item1", "slice.resource.attr-item2"})
	rb.SetStringEnumResourceAttrOne()
	rb.SetStringResourceAttr("string.resource.attr-val")
	rb.SetStringResourceAttrDisableWarning("string.resource.attr_disable_warning-val")
	rb.SetStringResourceAttrRemoveWarning("string.resource.attr_remove_warning-val")
	rb.SetStringResourceAttrToBeRemoved("string.resource.attr_to_be_removed-val")
	res := rb.Emit()

	// append the first log record
	lr := plog.NewLogRecord()
	lr.SetTimestamp(pcommon.NewTimestampFromTime(time.Now()))
	lr.Attributes().PutStr("type", "log")
	lr.Body().SetStr("the first log record")

	// append the second log record
	lr2 := plog.NewLogRecord()
	lr2.SetTimestamp(pcommon.NewTimestampFromTime(time.Now()))
	lr2.Attributes().PutStr("type", "event")
	lr2.Body().SetStr("the second log record")

	lb.AppendLogRecord(lr)
	lb.AppendLogRecord(lr2)

	logs := lb.Emit(WithLogsResource(res))
	assert.Equal(t, 1, logs.ResourceLogs().Len())

	rl := logs.ResourceLogs().At(0)
	assert.Equal(t, 1, rl.ScopeLogs().Len())

	sl := rl.ScopeLogs().At(0)
	assert.Equal(t, ScopeName, sl.Scope().Name())
	assert.Equal(t, lb.buildInfo.Version, sl.Scope().Version())

	assert.Equal(t, 2, sl.LogRecords().Len())

	attrVal, ok := sl.LogRecords().At(0).Attributes().Get("type")
	assert.True(t, ok)
	assert.Equal(t, "log", attrVal.Str())

	assert.Equal(t, pcommon.ValueTypeStr, sl.LogRecords().At(0).Body().Type())
	assert.Equal(t, "the first log record", sl.LogRecords().At(0).Body().Str())

	attrVal, ok = sl.LogRecords().At(1).Attributes().Get("type")
	assert.True(t, ok)
	assert.Equal(t, "event", attrVal.Str())

	assert.Equal(t, pcommon.ValueTypeStr, sl.LogRecords().At(1).Body().Type())
	assert.Equal(t, "the second log record", sl.LogRecords().At(1).Body().Str())
}
