[comment]: <> (Code generated by mdatagen. DO NOT EDIT.)

# sample

## Default Metrics

The following metrics are emitted by default. Each of them can be disabled by applying the following configuration:

```yaml
metrics:
  <metric_name>:
    enabled: false
```

### default.metric

Monotonic cumulative sum int metric enabled by default.

The metric will be become optional soon.

| Unit | Metric Type | Value Type | Aggregation Temporality | Monotonic |
| ---- | ----------- | ---------- | ----------------------- | --------- |
| s | Sum | Int | Cumulative | true |

#### Attributes

| Name | Description | Values |
| ---- | ----------- | ------ |
| string_attr | Attribute with any string value. | Any Str |
| state | Integer attribute with overridden name. | Any Int |
| enum_attr | Attribute with a known set of string values. | Str: ``red``, ``green``, ``blue`` |
| slice_attr | Attribute with a slice value. | Any Slice |
| map_attr | Attribute with a map value. | Any Map |

### default.metric.to_be_removed

[DEPRECATED] Non-monotonic delta sum double metric enabled by default.

The metric will be removed soon.

| Unit | Metric Type | Value Type | Aggregation Temporality | Monotonic |
| ---- | ----------- | ---------- | ----------------------- | --------- |
| s | Sum | Double | Delta | false |

### metric.input_type

Monotonic cumulative sum int metric with string input_type enabled by default.

| Unit | Metric Type | Value Type | Aggregation Temporality | Monotonic |
| ---- | ----------- | ---------- | ----------------------- | --------- |
| s | Sum | Int | Cumulative | true |

#### Attributes

| Name | Description | Values |
| ---- | ----------- | ------ |
| string_attr | Attribute with any string value. | Any Str |
| state | Integer attribute with overridden name. | Any Int |
| enum_attr | Attribute with a known set of string values. | Str: ``red``, ``green``, ``blue`` |
| slice_attr | Attribute with a slice value. | Any Slice |
| map_attr | Attribute with a map value. | Any Map |

## Optional Metrics

The following metrics are not emitted by default. Each of them can be enabled by applying the following configuration:

```yaml
metrics:
  <metric_name>:
    enabled: true
```

### optional.metric

[DEPRECATED] Gauge double metric disabled by default.

| Unit | Metric Type | Value Type |
| ---- | ----------- | ---------- |
| 1 | Gauge | Double |

#### Attributes

| Name | Description | Values |
| ---- | ----------- | ------ |
| string_attr | Attribute with any string value. | Any Str |
| boolean_attr | Attribute with a boolean value. | Any Bool |
| boolean_attr2 | Another attribute with a boolean value. | Any Bool |

### optional.metric.empty_unit

[DEPRECATED] Gauge double metric disabled by default.

| Unit | Metric Type | Value Type |
| ---- | ----------- | ---------- |
|  | Gauge | Double |

#### Attributes

| Name | Description | Values |
| ---- | ----------- | ------ |
| string_attr | Attribute with any string value. | Any Str |
| boolean_attr | Attribute with a boolean value. | Any Bool |

## Resource Attributes

| Name | Description | Values | Enabled |
| ---- | ----------- | ------ | ------- |
| map.resource.attr | Resource attribute with a map value. | Any Map | true |
| optional.resource.attr | Explicitly disabled ResourceAttribute. | Any Str | false |
| slice.resource.attr | Resource attribute with a slice value. | Any Slice | true |
| string.enum.resource.attr | Resource attribute with a known set of string values. | Str: ``one``, ``two`` | true |
| string.resource.attr | Resource attribute with any string value. | Any Str | true |
| string.resource.attr_disable_warning | Resource attribute with any string value. | Any Str | true |
| string.resource.attr_remove_warning | Resource attribute with any string value. | Any Str | false |
| string.resource.attr_to_be_removed | Resource attribute with any string value. | Any Str | true |
