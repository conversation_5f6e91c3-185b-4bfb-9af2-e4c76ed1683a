type: metricreceiver

status:
  class: receiver
  stability:
    development: [logs]
    beta: [traces]
    stable: [metrics]
  distributions: [contrib]
  warnings:
    - Any additional information that should be brought to the consumer's attention


metrics:
  system.cpu.time:
    enabled: true
    description: Total CPU seconds broken down by different states.
    unit: s
    sum:
      monotonic: true
      aggregation_temporality: cumulative
    attributes:
