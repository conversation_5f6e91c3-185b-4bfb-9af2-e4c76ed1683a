type: file

status:
  class: receiver
  stability:
    development: [logs]
    beta: [traces]
    stable: [metrics]
  distributions: [contrib]
  warnings:
    - Any additional information that should be brought to the consumer's attention


metrics:
  default.metric:
    enabled: true
    extended_documentation: The metric will be become optional soon.
    unit: s
    sum:
      value_type: int
      monotonic: true
      aggregation_temporality: cumulative
