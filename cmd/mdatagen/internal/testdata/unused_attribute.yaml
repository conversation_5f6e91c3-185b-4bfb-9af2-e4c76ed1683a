type: metricreceiver

sem_conv_version: 1.9.0

status:
  class: receiver
  stability:
    development: [logs]
    beta: [traces]
    stable: [metrics]

attributes:
  used_attr_in_metrics_section:
    description: Used attribute.
    type: string
  used_attr_in_telemetry_section:
    description: Used attribute.
    type: string

  unused_attr:
    name_override: state
    description: Unused attribute.
    type: string

metrics:
  metric:
    enabled: true
    description: Metric.
    unit: "1"
    gauge:
      value_type: double
    attributes: [used_attr_in_metrics_section]

telemetry:
  metrics:
    metric:
      enabled: true
      description: Metric.
      unit: "1"
      gauge:
        value_type: double
      attributes: [used_attr_in_telemetry_section]