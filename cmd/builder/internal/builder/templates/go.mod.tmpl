// Code generated by "go.opentelemetry.io/collector/cmd/builder". DO NOT EDIT.

module {{.Distribution.Module}}

go 1.22

require (
	{{- range .ConfmapConverters}}
	{{if .GoMod}}{{.GoMod}}{{end}}
	{{- end}}
	{{- range .ConfmapProviders}}
	{{if .GoMod}}{{.GoMod}}{{end}}
	{{- end}}
	{{- range .Connectors}}
	{{if .GoMod}}{{.GoMod}}{{end}}
	{{- end}}
	{{- range .Extensions}}
	{{if .GoMod}}{{.GoMod}}{{end}}
	{{- end}}
	{{- range .Receivers}}
	{{if .GoMod}}{{.GoMod}}{{end}}
	{{- end}}
	{{- range .Exporters}}
	{{if .GoMod}}{{.GoMod}}{{end}}
	{{- end}}
	{{- range .Processors}}
	{{if .GoMod}}{{.GoMod}}{{end}}
	{{- end}}
	go.opentelemetry.io/collector/otelcol {{.OtelColVersion}}
)

require (
	github.com/knadh/koanf/maps v0.1.1 // indirect
	github.com/knadh/koanf/providers/confmap v0.1.0 // indirect
)

{{- range .ConfmapConverters}}
{{if ne .Path ""}}replace {{.GoMod}} => {{.Path}}{{end}}
{{- end}}
{{- range .ConfmapProviders}}
{{if ne .Path ""}}replace {{.GoMod}} => {{.Path}}{{end}}
{{- end}}
{{- range .Connectors}}
{{if ne .Path ""}}replace {{.GoMod}} => {{.Path}}{{end}}
{{- end}}
{{- range .Extensions}}
{{if ne .Path ""}}replace {{.GoMod}} => {{.Path}}{{end}}
{{- end}}
{{- range .Receivers}}
{{if ne .Path ""}}replace {{.GoMod}} => {{.Path}}{{end}}
{{- end}}
{{- range .Exporters}}
{{if ne .Path ""}}replace {{.GoMod}} => {{.Path}}{{end}}
{{- end}}
{{- range .Processors}}
{{if ne .Path ""}}replace {{.GoMod}} => {{.Path}}{{end}}
{{- end}}
{{- range .Replaces}}
replace {{.}}
{{- end}}
{{- range .Excludes}}
exclude {{.}}
{{- end}}
