// Code generated by "go.opentelemetry.io/collector/cmd/builder". DO NOT EDIT.

package main

import (
	"go.opentelemetry.io/collector/component"
	"go.opentelemetry.io/collector/connector"
	"go.opentelemetry.io/collector/exporter"
	"go.opentelemetry.io/collector/extension"
	"go.opentelemetry.io/collector/otelcol"
	"go.opentelemetry.io/collector/processor"
	"go.opentelemetry.io/collector/receiver"
	{{- range .Connectors}}
	{{.Name}} "{{.Import}}"
	{{- end}}
	{{- range .Exporters}}
	{{.Name}} "{{.Import}}"
	{{- end}}
	{{- range .Extensions}}
	{{.Name}} "{{.Import}}"
	{{- end}}
	{{- range .Processors}}
	{{.Name}} "{{.Import}}"
	{{- end}}
	{{- range .Receivers}}
	{{.Name}} "{{.Import}}"
	{{- end}}
)

func components() (otelcol.Factories, error) {
	var err error
	factories := otelcol.Factories{}

	factories.Extensions, err = otelcol.MakeFactoryMap[extension.Factory](
		{{- range .Extensions}}
		{{.Name}}.NewFactory(),
		{{- end}}
	)
	if err != nil {
		return otelcol.Factories{}, err
	}
	factories.ExtensionModules = make(map[component.Type]string, len(factories.Extensions))
	{{- range .Extensions}}
	factories.ExtensionModules[{{.Name}}.NewFactory().Type()] = "{{.GoMod}}"
	{{- end}}

	factories.Receivers, err = otelcol.MakeFactoryMap[receiver.Factory](
		{{- range .Receivers}}
		{{.Name}}.NewFactory(),
		{{- end}}
	)
	if err != nil {
		return otelcol.Factories{}, err
	}
	factories.ReceiverModules = make(map[component.Type]string, len(factories.Receivers))
	{{- range .Receivers}}
	factories.ReceiverModules[{{.Name}}.NewFactory().Type()] = "{{.GoMod}}"
	{{- end}}

	factories.Exporters, err = otelcol.MakeFactoryMap[exporter.Factory](
		{{- range .Exporters}}
		{{.Name}}.NewFactory(),
		{{- end}}
	)
	if err != nil {
		return otelcol.Factories{}, err
	}
	factories.ExporterModules = make(map[component.Type]string, len(factories.Exporters))
	{{- range .Exporters}}
	factories.ExporterModules[{{.Name}}.NewFactory().Type()] = "{{.GoMod}}"
	{{- end}}

	factories.Processors, err = otelcol.MakeFactoryMap[processor.Factory](
		{{- range .Processors}}
		{{.Name}}.NewFactory(),
		{{- end}}
	)
	if err != nil {
		return otelcol.Factories{}, err
	}
	factories.ProcessorModules = make(map[component.Type]string, len(factories.Processors))
	{{- range .Processors}}
	factories.ProcessorModules[{{.Name}}.NewFactory().Type()] = "{{.GoMod}}"
	{{- end}}

	factories.Connectors, err = otelcol.MakeFactoryMap[connector.Factory](
		{{- range .Connectors}}
		{{.Name}}.NewFactory(),
		{{- end}}
	)
	if err != nil {
		return otelcol.Factories{}, err
	}
	factories.ConnectorModules = make(map[component.Type]string, len(factories.Connectors))
	{{- range .Connectors}}
	factories.ConnectorModules[{{.Name}}.NewFactory().Type()] = "{{.GoMod}}"
	{{- end}}

	return factories, nil
}
