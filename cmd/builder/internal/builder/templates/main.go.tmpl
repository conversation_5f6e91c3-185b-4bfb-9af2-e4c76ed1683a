// Code generated by "go.opentelemetry.io/collector/cmd/builder". DO NOT EDIT.

// Program {{ .Distribution.Name }} is an OpenTelemetry Collector binary.
package main

import (
	"log"

	"go.opentelemetry.io/collector/component"
	"go.opentelemetry.io/collector/confmap"
	{{- range .ConfmapConverters}}
	{{.Name}} "{{.Import}}"
	{{- end}}	
	{{- range .ConfmapProviders}}
	{{.Name}} "{{.Import}}"
	{{- end}}
	"go.opentelemetry.io/collector/otelcol"
)

func main() {
	info := component.BuildInfo{
		Command:     "{{ .Distribution.Name }}",
		Description: "{{ .Distribution.Description }}",
		Version:     "{{ .Distribution.Version }}",
	}

	set := otelcol.CollectorSettings{
		BuildInfo: info,
		Factories: components,
		ConfigProviderSettings: otelcol.ConfigProviderSettings{
			ResolverSettings: confmap.ResolverSettings{
				ProviderFactories: []confmap.ProviderFactory{
					{{- range .ConfmapProviders}}
					{{.Name}}.NewFactory(),
					{{- end}}
				},
				{{- if .ConfmapConverters }}
				ConverterFactories: []confmap.ConverterFactory{
					{{- range .ConfmapConverters}}
					{{.Name}}.NewFactory(),
					{{- end}}
				},
				{{- end }}
				{{- if .ConfResolver.DefaultURIScheme }}
				DefaultScheme: "{{ .ConfResolver.DefaultURIScheme }}",
				{{- end }}
			},
		},
		ProviderModules: map[string]string{
			{{- range .ConfmapProviders}}
			{{.Name}}.NewFactory().Create(confmap.ProviderSettings{}).Scheme(): "{{.GoMod}}",
			{{- end}}
    	},
		ConverterModules: []string{
			{{- range .ConfmapConverters}}
			"{{.GoMod}}",
			{{- end}}
		},
	}

	if err := run(set); err != nil {
		log.Fatal(err)
	}
}

func runInteractive(params otelcol.CollectorSettings) error {
	cmd := otelcol.NewCommand(params)
	if err := cmd.Execute(); err != nil {
		log.Fatalf("collector server run finished with error: %v", err)
	}

	return nil
}
