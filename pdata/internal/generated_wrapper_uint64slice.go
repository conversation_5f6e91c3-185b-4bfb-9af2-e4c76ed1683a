// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package internal

import (

"iter"


"slices"





"go.opentelemetry.io/collector/pdata/internal"


)

type UInt64Slice struct {
	orig *[]uint64
	state *State
}

func GetOrigUInt64Slice(ms UInt64Slice) *[]uint64 {
	return ms.orig
}

func GetUInt64SliceState(ms UInt64Slice) *State {
	return ms.state
}

func NewUInt64Slice(orig *[]uint64, state *State) UInt64Slice {
	return UInt64Slice{orig: orig, state: state}
}

func CopyOrigUInt64Slice(dst, src []uint64) []uint64 {
	dst = dst[:0]
	return append(dst, src...)
}

func FillTestUInt64Slice(tv UInt64Slice) {
}

func GenerateTestUInt64Slice() UInt64Slice {
	state := StateMutable
	var orig []uint64 = nil

	return UInt64Slice{&orig, &state}
}
