// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package internal

import (
	"iter"

	"slices"

	"go.opentelemetry.io/collector/pdata/internal"
)

type Int32Slice struct {
	orig  *[]int32
	state *State
}

func GetOrigInt32Slice(ms Int32Slice) *[]int32 {
	return ms.orig
}

func GetInt32SliceState(ms Int32Slice) *State {
	return ms.state
}

func NewInt32Slice(orig *[]int32, state *State) Int32Slice {
	return Int32Slice{orig: orig, state: state}
}

func CopyOrigInt32Slice(dst, src []int32) []int32 {
	dst = dst[:0]
	return append(dst, src...)
}

func FillTestInt32Slice(tv Int32Slice) {
}

func GenerateTestInt32Slice() Int32Slice {
	state := StateMutable
	var orig []int32 = nil

	return Int32Slice{&orig, &state}
}
