// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package internal

import (
	"iter"

	"slices"

	"go.opentelemetry.io/collector/pdata/internal"
)

type Float64Slice struct {
	orig  *[]float64
	state *State
}

func GetOrigFloat64Slice(ms Float64Slice) *[]float64 {
	return ms.orig
}

func GetFloat64SliceState(ms Float64Slice) *State {
	return ms.state
}

func NewFloat64Slice(orig *[]float64, state *State) Float64Slice {
	return Float64Slice{orig: orig, state: state}
}

func CopyOrigFloat64Slice(dst, src []float64) []float64 {
	dst = dst[:0]
	return append(dst, src...)
}

func FillTestFloat64Slice(tv Float64Slice) {
}

func GenerateTestFloat64Slice() Float64Slice {
	state := StateMutable
	var orig []float64 = nil

	return Float64Slice{&orig, &state}
}
