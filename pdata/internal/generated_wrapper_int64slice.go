// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package internal

import (
	"iter"

	"slices"

	"go.opentelemetry.io/collector/pdata/internal"
)

type Int64Slice struct {
	orig  *[]int64
	state *State
}

func GetOrigInt64Slice(ms Int64Slice) *[]int64 {
	return ms.orig
}

func GetInt64SliceState(ms Int64Slice) *State {
	return ms.state
}

func NewInt64Slice(orig *[]int64, state *State) Int64Slice {
	return Int64Slice{orig: orig, state: state}
}

func CopyOrigInt64Slice(dst, src []int64) []int64 {
	dst = dst[:0]
	return append(dst, src...)
}

func FillTestInt64Slice(tv Int64Slice) {
}

func GenerateTestInt64Slice() Int64Slice {
	state := StateMutable
	var orig []int64 = nil

	return Int64Slice{&orig, &state}
}
