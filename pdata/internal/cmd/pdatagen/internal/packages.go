// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

package internal // import "go.opentelemetry.io/collector/pdata/internal/cmd/pdatagen/internal"

import (
	"os"
	"path/filepath"
	"strings"
	"text/template"
)

const header = `// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".`

// AllPackages is a list of all packages that needs to be generated.
var AllPackages = []*Package{
	pcommon,
	plog,
	plogotlp,
	pmetric,
	pmetricotlp,
	ptrace,
	ptraceotlp,
	pprofile,
	pprofileotlp,
}

// Package is a struct used to generate files.
type Package struct {
	info *PackageInfo
	// Can be any of sliceOfPtrs, sliceOfValues, messageValueStruct.
	structs []baseStruct
}

type PackageInfo struct {
	name        string
	path        string
	imports     []string
	testImports []string
}

// GenerateFiles generates files with the configured data structures for this Package.
func (p *Package) GenerateFiles() error {
	for _, s := range p.structs {
		path := filepath.Join(p.info.path, "generated_"+strings.ToLower(s.getName())+".go")
		if err := os.WriteFile(path, s.generate(p.info), 0o600); err != nil {
			return err
		}
	}

	// Generate compare_options.go file for pcommon and pmetric packages
	if p.info.name == "pcommon" || p.info.name == "pmetric" {
		if err := p.generateCompareOptionsFile(); err != nil {
			return err
		}
	}

	return nil
}

// GenerateTestFiles generates files with tests for the configured data structures for this Package.
func (p *Package) GenerateTestFiles() error {
	for _, s := range p.structs {
		path := filepath.Join(p.info.path, "generated_"+strings.ToLower(s.getName())+"_test.go")
		if err := os.WriteFile(path, s.generateTests(p.info), 0o600); err != nil {
			return err
		}
	}
	return nil
}

// GenerateInternalFiles generates files with internal pdata structures for this Package.
func (p *Package) GenerateInternalFiles() error {
	if !usedByOtherDataTypes(p.info.name) {
		return nil
	}

	for _, s := range p.structs {
		path := filepath.Join("internal", "generated_wrapper_"+strings.ToLower(s.getName())+".go")
		if err := os.WriteFile(path, s.generateInternal(p.info), 0o600); err != nil {
			return err
		}
	}
	return nil
}

// usedByOtherDataTypes defines if the package is used by other data types and orig fields of the package's structs
// need to be accessible from other pdata packages.
func usedByOtherDataTypes(packageName string) bool {
	return packageName == "pcommon"
}

// generateCompareOptionsFile generates the compare_options.go file for the package
func (p *Package) generateCompareOptionsFile() error {
	tmpl, err := template.ParseFiles(filepath.Join("internal", "cmd", "pdatagen", "internal", "templates", "compare_options.go.tmpl"))
	if err != nil {
		return err
	}

	file, err := os.Create(filepath.Join(p.info.path, "compare_options.go"))
	if err != nil {
		return err
	}
	defer file.Close()

	return tmpl.Execute(file, map[string]string{
		"packageName": p.info.name,
	})
}
