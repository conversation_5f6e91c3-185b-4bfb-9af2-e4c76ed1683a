// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package {{ .packageName }}

import (
{{ range $index, $element := .imports -}}
{{ $element }}
{{ end }}
)

// {{ .structName }} logically represents a slice of {{ .elementName }}.
//
// This is a reference type. If passed by value and callee modifies it, the
// caller will see the modification.
//
// Must use New{{ .structName }} function to create new instances.
// Important: zero-initialized instance is not valid for use.
{{- if .isCommon }}
type {{ .structName }} internal.{{ .structName }}
{{- else }}
type {{ .structName }} struct {
	orig *[]{{ .originElementType }}
	state *internal.State
}
{{- end }}

func new{{ .structName }}(orig *[]{{ .originElementType }}, state *internal.State) {{ .structName }} {
	{{- if .isCommon }}
	return {{ .structName }}(internal.New{{ .structName }}(orig, state))
	{{- else }}
	return {{ .structName }}{orig: orig, state: state}
	{{- end }}
}

// New{{ .structName }} creates a {{ .structName }} with 0 elements.
// Can use "EnsureCapacity" to initialize with a given capacity.
func New{{ .structName }}() {{ .structName }} {
	orig := []{{ .originElementType }}(nil)
	state := internal.StateMutable
	return new{{ .structName }}(&orig, &state)
}

// Len returns the number of elements in the slice.
//
// Returns "0" for a newly instance created with "New{{ .structName }}()".
func (es {{ .structName }}) Len() int {
	return len(*es.{{ .origAccessor }})
}

// At returns the element at the given index.
//
// This function is used mostly for iterating over all the values in the slice:
//   for i := 0; i < es.Len(); i++ {
//       e := es.At(i)
//       ... // Do something with the element
//   }
func (es {{ .structName }}) At(i int) {{ .elementName }} {
	return {{ .newElement }}
}

// All returns an iterator over index-value pairs in the slice.
//
//	for i, v := range es.All() {
//	    ... // Do something with index-value pair
//	}
func (es {{ .structName }}) All() iter.Seq2[int, {{ .elementName }}] {
	return func(yield func(int, {{ .elementName }}) bool) {
		for i := 0; i < es.Len(); i++ {
			if !yield(i, es.At(i)) {
				return
			}
		}
	}
}

// EnsureCapacity is an operation that ensures the slice has at least the specified capacity.
// 1. If the newCap <= cap then no change in capacity.
// 2. If the newCap > cap then the slice capacity will be expanded to equal newCap.
//
// Here is how a new {{ .structName }} can be initialized:
//   es := New{{ .structName }}()
//   es.EnsureCapacity(4)
//   for i := 0; i < 4; i++ {
//       e := es.AppendEmpty()
//       // Here should set all the values for e.
//   }
func (es {{ .structName }}) EnsureCapacity(newCap int) {
	es.{{ .stateAccessor }}.AssertMutable()
	oldCap := cap(*es.{{ .origAccessor }})
	if newCap <= oldCap {
		return
	}

	newOrig := make([]{{ .originElementType }}, len(*es.{{ .origAccessor }}), newCap)
	copy(newOrig, *es.{{ .origAccessor }})
	*es.{{ .origAccessor }} = newOrig
}

// AppendEmpty will append to the end of the slice an empty {{ .elementName }}.
// It returns the newly added {{ .elementName }}.
func (es {{ .structName }}) AppendEmpty() {{ .elementName }} {
	es.{{ .stateAccessor }}.AssertMutable()
	*es.{{ .origAccessor }} = append(*es.{{ .origAccessor }}, {{ .emptyOriginElement }})
	return es.At(es.Len() - 1)
}

// MoveAndAppendTo moves all elements from the current slice and appends them to the dest.
// The current slice will be cleared.
func (es {{ .structName }}) MoveAndAppendTo(dest {{ .structName }}) {
	es.{{ .stateAccessor }}.AssertMutable()
	dest.{{ .stateAccessor }}.AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if es.{{ .origAccessor }} == dest.{{ .origAccessor }} {
		return
	}
	if *dest.{{ .origAccessor }} == nil {
		// We can simply move the entire vector and avoid any allocations.
		*dest.{{ .origAccessor }} = *es.{{ .origAccessor }}
	} else {
		*dest.{{ .origAccessor }} = append(*dest.{{ .origAccessor }}, *es.{{ .origAccessor }}...)
	}
	*es.{{ .origAccessor }} = nil
}

// RemoveIf calls f sequentially for each element present in the slice.
// If f returns true, the element is removed from the slice.
func (es {{ .structName }}) RemoveIf(f func({{ .elementName }}) bool) {
	es.{{ .stateAccessor }}.AssertMutable()
	newLen := 0
	for i := 0; i < len(*es.{{ .origAccessor }}); i++ {
		if f(es.At(i)) {
			continue
		}
		if newLen == i {
			// Nothing to move, element is at the right place.
			newLen++
			continue
		}
		(*es.{{ .origAccessor }})[newLen] = (*es.{{ .origAccessor }})[i]
		newLen++
	}
	*es.{{ .origAccessor }} = (*es.{{ .origAccessor }})[:newLen]
}


// CopyTo copies all elements from the current slice overriding the destination.
func (es {{ .structName }}) CopyTo(dest {{ .structName }}) {
	dest.{{ .stateAccessor }}.AssertMutable()
	{{- if .isCommon }}
	*dest.{{ .origAccessor }} = internal.CopyOrig{{ .structName }}(*dest.{{ .origAccessor }}, *es.{{ .origAccessor }})
	{{- else }}
	*dest.{{ .origAccessor }} = copyOrig{{ .structName }}(*dest.{{ .origAccessor }}, *es.{{ .origAccessor }})
	{{- end }}
}

// Equal checks equality with another {{ .structName }}.
// In order to match equality, the order of elements must be the same.
{{- if or (eq .packageName "pcommon") (eq .packageName "pmetric") (eq .packageName "plog") (eq .packageName "ptrace") (eq .packageName "pprofile") }}
// Optionally accepts CompareOption arguments to customize comparison behavior.
func (es {{ .structName }}) Equal(val {{ .structName }}, opts ...CompareOption) bool {
	if es.Len() != val.Len() {
		return false
	}
	for i := 0; i < es.Len(); i++ {
		if !es.At(i).Equal(val.At(i), opts...) {
			return false
		}
	}
	return true
}
	{{- else }}
func (es {{ .structName }}) Equal(val {{ .structName }}) bool {
	if es.Len() != val.Len() {
		return false
	}
	for i := 0; i < es.Len(); i++ {
		if !es.At(i).Equal(val.At(i)) {
			return false
		}
	}
	return true
	}
	{{- end }}

{{ if eq .type "sliceOfPtrs" -}}
// Sort sorts the {{ .elementName }} elements within {{ .structName }} given the
// provided less function so that two instances of {{ .structName }}
// can be compared.
func (es {{ .structName }}) Sort(less func(a, b {{ .elementName }}) bool) {
	es.{{ .stateAccessor }}.AssertMutable()
	sort.SliceStable(*es.{{ .origAccessor }}, func(i, j int) bool { return less(es.At(i), es.At(j)) })
}
{{- end }}

{{ if .isCommon -}}
func (ms {{ .structName }}) getOrig() *[]{{ .originElementType }} {
	return internal.GetOrig{{ .structName }}(internal.{{ .structName }}(ms))
}

func (ms {{ .structName }}) getState() *internal.State {
	return internal.Get{{ .structName }}State(internal.{{ .structName }}(ms))
}
{{- else }}

func copyOrig{{ .structName }}(dest, src []{{ .originElementType }}) []{{ .originElementType }} {
	if cap(dest) < len(src) {
		dest = make([]{{ .originElementType }}, len(src))
		{{- if eq .type "sliceOfPtrs" }}
		data := make([]{{ .originName }}, len(src))
		for i := range src {
			dest[i] = &data[i]
		}
		{{- end }}
	}
	dest = dest[:len(src)]
	for i := range src {
		copyOrig{{ .elementName }}({{ .originElementPtr }}dest[i], {{ .originElementPtr }}src[i])
	}
	return dest
}
{{- end }}
