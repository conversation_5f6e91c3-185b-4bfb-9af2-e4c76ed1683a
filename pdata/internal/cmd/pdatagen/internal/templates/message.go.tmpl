// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package {{ .packageName }}

import (
{{ range $index, $element := .imports -}}
{{ $element }}
{{ end }}
)

{{ .description }}
//
// This is a reference type, if passed by value and callee modifies it the
// caller will see the modification.
//
// Must use New{{ .structName }} function to create new instances.
// Important: zero-initialized instance is not valid for use.
{{- if .isCommon }}
type {{ .structName }} internal.{{ .structName }}
{{- else }}
type {{ .structName }} struct {
	orig *{{ .originName }}
	state *internal.State
}
{{- end }}

func new{{ .structName }}(orig *{{ .originName }}, state *internal.State) {{ .structName }} {
	{{- if .isCommon }}
	return {{ .structName }}(internal.New{{ .structName }}(orig, state))
	{{- else }}
	return {{ .structName }}{orig: orig, state: state}
	{{- end }}
}

// New{{ .structName }} creates a new empty {{ .structName }}.
//
// This must be used only in testing code. Users should use "AppendEmpty" when part of a Slice,
// OR directly access the member if this is embedded in another struct.
func New{{ .structName }}() {{ .structName }} {
	state := internal.StateMutable
	return new{{ .structName }}(&{{ .originName }}{}, &state)
}

// MoveTo moves all properties from the current struct overriding the destination and
// resetting the current instance to its zero value
func (ms {{ .structName }}) MoveTo(dest {{ .structName }}) {
	ms.{{ .stateAccessor }}.AssertMutable()
	dest.{{ .stateAccessor }}.AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if ms.{{ .origAccessor }} == dest.{{ .origAccessor }} {
		return
	}
	*dest.{{ .origAccessor }} = *ms.{{ .origAccessor }}
	*ms.{{ .origAccessor }} = {{ .originName }}{}
}

{{ if .isCommon -}}
func (ms {{ .structName }}) getOrig() *{{ .originName }} {
	return internal.GetOrig{{ .structName }}(internal.{{ .structName }}(ms))
}

func (ms {{ .structName }}) getState() *internal.State {
	return internal.Get{{ .structName }}State(internal.{{ .structName }}(ms))
}
{{- end }}

{{ range .fields -}}
{{ .GenerateAccessors $.messageStruct }}
{{ end }}

// CopyTo copies all properties from the current struct overriding the destination.
func (ms {{ .structName }}) CopyTo(dest {{ .structName }}) {
	dest.{{ .stateAccessor }}.AssertMutable()
    {{- if .isCommon }}
    internal.CopyOrig{{ .structName }}(dest.{{ .origAccessor }}, ms.{{ .origAccessor }})
    {{- else }}
    copyOrig{{ .structName }}(dest.{{ .origAccessor }}, ms.{{ .origAccessor }})
	{{- end }}
}

{{ if not .isCommon -}}
func copyOrig{{ .structName }}(dest, src *{{ .originName }}) {
	{{- range .fields }}
	{{ .GenerateCopyOrig $.messageStruct }}
	{{- end }}
}
{{- end }}

// Equal checks equality with another {{ .structName }}.
{{- if or (eq .packageName "pcommon") (eq .packageName "pmetric") }}
// Optionally accepts CompareOption arguments to customize comparison behavior.
func (ms {{ .structName }}) Equal(val {{ .structName }}, opts ...CompareOption) bool {
	{{- if eq (len .fields) 0 }}
	return true
	{{- else }}
	cfg := NewCompareConfig(opts)
	{{- if eq .packageName "pmetric" }}
	_ = cfg // may not be used in all cases
	{{- end }}
	return {{ range $index, $field := .fields }}{{ if $index }} &&
		{{ end }}{{ $field.GenerateEqualComparison $.messageStruct }}{{ end }}
	{{- end }}
}
{{- else }}
func (ms {{ .structName }}) Equal(val {{ .structName }}) bool {
	{{- if eq (len .fields) 0 }}
	return true
	{{- else }}
	return {{ range $index, $field := .fields }}{{ if $index }} &&
		{{ end }}{{ $field.GenerateEqualComparison $.messageStruct }}{{ end }}
	{{- end }}
}
{{- end }}
