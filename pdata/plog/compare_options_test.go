// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

package plog

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"go.opentelemetry.io/collector/pdata/pcommon"
)

func TestLogRecordCompareOptions(t *testing.T) {
	// Create two identical log records
	lr1 := NewLogRecord()
	lr1.SetTimestamp(pcommon.Timestamp(**********))
	lr1.SetEventName("test-event")
	lr1.SetSeverityText("INFO")
	lr1.Body().SetStr("test message")

	lr2 := NewLogRecord()
	lr2.SetTimestamp(pcommon.Timestamp(**********))
	lr2.SetEventName("test-event")
	lr2.SetSeverityText("INFO")
	lr2.Body().SetStr("test message")

	// Test basic equality
	assert.True(t, lr1.Equal(lr2))

	// Test with different timestamps
	lr2.SetTimestamp(pcommon.Timestamp(**********))
	assert.False(t, lr1.Equal(lr2))

	// Test ignoring timestamp field
	assert.True(t, lr1.Equal(lr2, IgnoreFields("Timestamp")))

	// Test with different event names
	lr2.SetTimestamp(pcommon.Timestamp(**********)) // Reset timestamp
	lr2.SetEventName("different-event")
	assert.False(t, lr1.Equal(lr2))

	// Test ignoring event name field
	assert.True(t, lr1.Equal(lr2, IgnoreFields("EventName")))

	// Test ignoring multiple fields
	lr2.SetSeverityText("ERROR")
	assert.False(t, lr1.Equal(lr2))
	assert.True(t, lr1.Equal(lr2, IgnoreFields("EventName", "SeverityText")))
}

func TestCompareConfigIgnoreFields(t *testing.T) {
	cfg := NewCompareConfig([]CompareOption{
		IgnoreFields("timestamp", "id"),
	})

	assert.True(t, cfg.ShouldIgnoreField("timestamp"))
	assert.True(t, cfg.ShouldIgnoreField("id"))
	assert.False(t, cfg.ShouldIgnoreField("name"))
}

func TestCompareConfigIgnorePaths(t *testing.T) {
	cfg := NewCompareConfig([]CompareOption{
		IgnorePaths("metadata.*", "debug.info"),
	})

	assert.True(t, cfg.ShouldIgnorePath("debug.info"))
	assert.True(t, cfg.ShouldIgnorePath("metadata.version"))
	assert.True(t, cfg.ShouldIgnorePath("metadata.timestamp"))
	assert.False(t, cfg.ShouldIgnorePath("content.body"))
}

func TestMultipleOptionsIntegration(t *testing.T) {
	opts := []CompareOption{
		IgnoreFields("timestamp", "id"),
		IgnorePaths("metadata.*", "debug.info"),
	}

	cfg := NewCompareConfig(opts)

	assert.True(t, cfg.ShouldIgnoreField("timestamp"))
	assert.True(t, cfg.ShouldIgnoreField("id"))
	assert.False(t, cfg.ShouldIgnoreField("name"))

	assert.True(t, cfg.ShouldIgnorePath("debug.info"))
	assert.True(t, cfg.ShouldIgnorePath("metadata.version"))
	assert.False(t, cfg.ShouldIgnorePath("content.body"))
}
