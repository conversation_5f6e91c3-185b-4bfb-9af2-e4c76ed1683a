// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package plog

import (
"testing"
"unsafe"

"github.com/stretchr/testify/assert"

"go.opentelemetry.io/collector/pdata/internal"
"go.opentelemetry.io/collector/pdata/internal/data"
otlplogs "go.opentelemetry.io/collector/pdata/internal/data/protogen/logs/v1"
"go.opentelemetry.io/collector/pdata/pcommon"

)

func TestResourceLogs_MoveTo(t *testing.T) {
	ms := generateTestResourceLogs()
	dest := NewResourceLogs()
	ms.MoveTo(dest)
	assert.Equal(t, NewResourceLogs(), ms)
	assert.Equal(t, generateTestResourceLogs(), dest)
	dest.MoveTo(dest)
	assert.Equal(t, generateTestResourceLogs(), dest)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.MoveTo(newResourceLogs(&otlplogs.ResourceLogs{}, &sharedState)) })
	assert.Panics(t, func() { newResourceLogs(&otlplogs.ResourceLogs{}, &sharedState).MoveTo(dest) })
}

func TestResourceLogs_CopyTo(t *testing.T) {
	ms := NewResourceLogs()
	orig := NewResourceLogs()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	orig = generateTestResourceLogs()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.CopyTo(newResourceLogs(&otlplogs.ResourceLogs{}, &sharedState)) })
}

func TestResourceLogs_Equal(t *testing.T) {
	ms1 := NewResourceLogs()
	ms2 := NewResourceLogs()
	assert.True(t, ms1.Equal(ms2))

	ms1 = generateTestResourceLogs()
	ms2 = generateTestResourceLogs()
	assert.True(t, ms1.Equal(ms2))

	ms2 = NewResourceLogs()
	assert.False(t, ms1.Equal(ms2))
}


func TestResourceLogs_Resource(t *testing.T) {
	ms := NewResourceLogs()
	internal.FillTestResource(internal.Resource(ms.Resource()))
	assert.Equal(t, pcommon.Resource(internal.GenerateTestResource()), ms.Resource())
}

func TestResourceLogs_SchemaUrl(t *testing.T) {
	ms := NewResourceLogs()
	assert.Empty(t, ms.SchemaUrl())
	ms.SetSchemaUrl("https://opentelemetry.io/schemas/1.5.0")
	assert.Equal(t, "https://opentelemetry.io/schemas/1.5.0", ms.SchemaUrl())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newResourceLogs(&otlplogs.ResourceLogs{}, &sharedState).SetSchemaUrl("https://opentelemetry.io/schemas/1.5.0") })
}

func TestResourceLogs_ScopeLogs(t *testing.T) {
	ms := NewResourceLogs()
	assert.Equal(t, NewScopeLogsSlice(), ms.ScopeLogs())
	fillTestScopeLogsSlice(ms.ScopeLogs())
	assert.Equal(t, generateTestScopeLogsSlice(), ms.ScopeLogs())
}


func generateTestResourceLogs() ResourceLogs {
	tv := NewResourceLogs()
	fillTestResourceLogs(tv)
	return tv
}

func fillTestResourceLogs(tv ResourceLogs) {
	internal.FillTestResource(internal.NewResource(&tv.orig.Resource, tv.state))
	tv.orig.SchemaUrl = "https://opentelemetry.io/schemas/1.5.0"
	fillTestScopeLogsSlice(newScopeLogsSlice(&tv.orig.ScopeLogs, tv.state))
}

