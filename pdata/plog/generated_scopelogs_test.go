// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package plog

import (
	"testing"
	"unsafe"

	"github.com/stretchr/testify/assert"

	"go.opentelemetry.io/collector/pdata/internal"
	"go.opentelemetry.io/collector/pdata/internal/data"
	otlplogs "go.opentelemetry.io/collector/pdata/internal/data/protogen/logs/v1"
	"go.opentelemetry.io/collector/pdata/pcommon"
)

func TestScopeLogs_MoveTo(t *testing.T) {
	ms := generateTestScopeLogs()
	dest := NewScopeLogs()
	ms.MoveTo(dest)
	assert.Equal(t, NewScopeLogs(), ms)
	assert.Equal(t, generateTestScopeLogs(), dest)
	dest.MoveTo(dest)
	assert.Equal(t, generateTestScopeLogs(), dest)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.MoveTo(newScopeLogs(&otlplogs.ScopeLogs{}, &sharedState)) })
	assert.Panics(t, func() { newScopeLogs(&otlplogs.ScopeLogs{}, &sharedState).MoveTo(dest) })
}

func TestScopeLogs_CopyTo(t *testing.T) {
	ms := NewScopeLogs()
	orig := NewScopeLogs()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	orig = generateTestScopeLogs()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.CopyTo(newScopeLogs(&otlplogs.ScopeLogs{}, &sharedState)) })
}

func TestScopeLogs_Equal(t *testing.T) {
	ms1 := NewScopeLogs()
	ms2 := NewScopeLogs()
	assert.True(t, ms1.Equal(ms2))

	ms1 = generateTestScopeLogs()
	ms2 = generateTestScopeLogs()
	assert.True(t, ms1.Equal(ms2))

	ms2 = NewScopeLogs()
	assert.False(t, ms1.Equal(ms2))
}

func TestScopeLogs_Scope(t *testing.T) {
	ms := NewScopeLogs()
	internal.FillTestInstrumentationScope(internal.InstrumentationScope(ms.Scope()))
	assert.Equal(t, pcommon.InstrumentationScope(internal.GenerateTestInstrumentationScope()), ms.Scope())
}

func TestScopeLogs_SchemaUrl(t *testing.T) {
	ms := NewScopeLogs()
	assert.Empty(t, ms.SchemaUrl())
	ms.SetSchemaUrl("https://opentelemetry.io/schemas/1.5.0")
	assert.Equal(t, "https://opentelemetry.io/schemas/1.5.0", ms.SchemaUrl())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() {
		newScopeLogs(&otlplogs.ScopeLogs{}, &sharedState).SetSchemaUrl("https://opentelemetry.io/schemas/1.5.0")
	})
}

func TestScopeLogs_LogRecords(t *testing.T) {
	ms := NewScopeLogs()
	assert.Equal(t, NewLogRecordSlice(), ms.LogRecords())
	fillTestLogRecordSlice(ms.LogRecords())
	assert.Equal(t, generateTestLogRecordSlice(), ms.LogRecords())
}

func generateTestScopeLogs() ScopeLogs {
	tv := NewScopeLogs()
	fillTestScopeLogs(tv)
	return tv
}

func fillTestScopeLogs(tv ScopeLogs) {
	internal.FillTestInstrumentationScope(internal.NewInstrumentationScope(&tv.orig.Scope, tv.state))
	tv.orig.SchemaUrl = "https://opentelemetry.io/schemas/1.5.0"
	fillTestLogRecordSlice(newLogRecordSlice(&tv.orig.LogRecords, tv.state))
}
