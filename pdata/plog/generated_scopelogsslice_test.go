// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package plog

import (
	"testing"
	"unsafe"

	"github.com/stretchr/testify/assert"

	"go.opentelemetry.io/collector/pdata/internal"
	"go.opentelemetry.io/collector/pdata/internal/data"
	otlplogs "go.opentelemetry.io/collector/pdata/internal/data/protogen/logs/v1"
	"go.opentelemetry.io/collector/pdata/pcommon"
)

func TestScopeLogsSlice(t *testing.T) {
	es := NewScopeLogsSlice()
	assert.Equal(t, 0, es.Len())
	state := internal.StateMutable
	es = newScopeLogsSlice(&[]*otlplogs.ScopeLogs{}, &state)
	assert.Equal(t, 0, es.Len())

	emptyVal := NewScopeLogs()
	testVal := generateTestScopeLogs()
	for i := 0; i < 7; i++ {
		el := es.AppendEmpty()
		assert.Equal(t, emptyVal, es.At(i))
		fillTestScopeLogs(el)
		assert.Equal(t, testVal, es.At(i))
	}
	assert.Equal(t, 7, es.Len())
}

func TestScopeLogsSliceReadOnly(t *testing.T) {
	sharedState := internal.StateReadOnly
	es := newScopeLogsSlice(&[]*otlplogs.ScopeLogs{}, &sharedState)
	assert.Equal(t, 0, es.Len())
	assert.Panics(t, func() { es.AppendEmpty() })
	assert.Panics(t, func() { es.EnsureCapacity(2) })
	es2 := NewScopeLogsSlice()
	es.CopyTo(es2)
	assert.Panics(t, func() { es2.CopyTo(es) })
	assert.Panics(t, func() { es.MoveAndAppendTo(es2) })
	assert.Panics(t, func() { es2.MoveAndAppendTo(es) })
}

func TestScopeLogsSlice_CopyTo(t *testing.T) {
	dest := NewScopeLogsSlice()
	// Test CopyTo to empty
	NewScopeLogsSlice().CopyTo(dest)
	assert.Equal(t, NewScopeLogsSlice(), dest)

	// Test CopyTo larger slice
	generateTestScopeLogsSlice().CopyTo(dest)
	assert.Equal(t, generateTestScopeLogsSlice(), dest)

	// Test CopyTo same size slice
	generateTestScopeLogsSlice().CopyTo(dest)
	assert.Equal(t, generateTestScopeLogsSlice(), dest)
}

func TestScopeLogsSlice_EnsureCapacity(t *testing.T) {
	es := generateTestScopeLogsSlice()

	// Test ensure smaller capacity.
	const ensureSmallLen = 4
	es.EnsureCapacity(ensureSmallLen)
	assert.Less(t, ensureSmallLen, es.Len())
	assert.Equal(t, es.Len(), cap(*es.orig))
	assert.Equal(t, generateTestScopeLogsSlice(), es)

	// Test ensure larger capacity
	const ensureLargeLen = 9
	es.EnsureCapacity(ensureLargeLen)
	assert.Less(t, generateTestScopeLogsSlice().Len(), ensureLargeLen)
	assert.Equal(t, ensureLargeLen, cap(*es.orig))
	assert.Equal(t, generateTestScopeLogsSlice(), es)
}

func TestScopeLogsSlice_MoveAndAppendTo(t *testing.T) {
	// Test MoveAndAppendTo to empty
	expectedSlice := generateTestScopeLogsSlice()
	dest := NewScopeLogsSlice()
	src := generateTestScopeLogsSlice()
	src.MoveAndAppendTo(dest)
	assert.Equal(t, generateTestScopeLogsSlice(), dest)
	assert.Equal(t, 0, src.Len())
	assert.Equal(t, expectedSlice.Len(), dest.Len())

	// Test MoveAndAppendTo empty slice
	src.MoveAndAppendTo(dest)
	assert.Equal(t, generateTestScopeLogsSlice(), dest)
	assert.Equal(t, 0, src.Len())
	assert.Equal(t, expectedSlice.Len(), dest.Len())

	// Test MoveAndAppendTo not empty slice
	generateTestScopeLogsSlice().MoveAndAppendTo(dest)
	assert.Equal(t, 2*expectedSlice.Len(), dest.Len())
	for i := 0; i < expectedSlice.Len(); i++ {
		assert.Equal(t, expectedSlice.At(i), dest.At(i))
		assert.Equal(t, expectedSlice.At(i), dest.At(i+expectedSlice.Len()))
	}

	dest.MoveAndAppendTo(dest)
	assert.Equal(t, 2*expectedSlice.Len(), dest.Len())
	for i := 0; i < expectedSlice.Len(); i++ {
		assert.Equal(t, expectedSlice.At(i), dest.At(i))
		assert.Equal(t, expectedSlice.At(i), dest.At(i+expectedSlice.Len()))
	}
}

func TestScopeLogsSlice_RemoveIf(t *testing.T) {
	// Test RemoveIf on empty slice
	emptySlice := NewScopeLogsSlice()
	emptySlice.RemoveIf(func(el ScopeLogs) bool {
		t.Fail()
		return false
	})

	// Test RemoveIf
	filtered := generateTestScopeLogsSlice()
	pos := 0
	filtered.RemoveIf(func(el ScopeLogs) bool {
		pos++
		return pos%3 == 0
	})
	assert.Equal(t, 5, filtered.Len())
}

func TestScopeLogsSliceAll(t *testing.T) {
	ms := generateTestScopeLogsSlice()
	assert.NotEmpty(t, ms.Len())

	var c int
	for i, v := range ms.All() {
		assert.Equal(t, ms.At(i), v, "element should match")
		c++
	}
	assert.Equal(t, ms.Len(), c, "All elements should have been visited")
}

func TestScopeLogsSlice_Equal(t *testing.T) {
	es1 := NewScopeLogsSlice()
	es2 := NewScopeLogsSlice()
	assert.True(t, es1.Equal(es2))

	es1 = generateTestScopeLogsSlice()
	es2 = generateTestScopeLogsSlice()
	assert.True(t, es1.Equal(es2))

	es2 = NewScopeLogsSlice()
	assert.False(t, es1.Equal(es2))

	es2.AppendEmpty()
	assert.False(t, es1.Equal(es2))

	// Test element-wise inequality - create two slices with same length but different elements
	if es1.Len() > 0 {
		es1 = generateTestScopeLogsSlice()
		es2 = NewScopeLogsSlice()
		// Make es2 same length as es1 but with empty elements
		for i := 0; i < es1.Len(); i++ {
			es2.AppendEmpty()
		}
		// This should return false since elements are different
		assert.False(t, es1.Equal(es2))
	}
}

func TestScopeLogsSlice_Sort(t *testing.T) {
	es := generateTestScopeLogsSlice()
	es.Sort(func(a, b ScopeLogs) bool {
		return uintptr(unsafe.Pointer(a.orig)) < uintptr(unsafe.Pointer(b.orig))
	})
	for i := 1; i < es.Len(); i++ {
		assert.Less(t, uintptr(unsafe.Pointer(es.At(i-1).orig)), uintptr(unsafe.Pointer(es.At(i).orig)))
	}
	es.Sort(func(a, b ScopeLogs) bool {
		return uintptr(unsafe.Pointer(a.orig)) > uintptr(unsafe.Pointer(b.orig))
	})
	for i := 1; i < es.Len(); i++ {
		assert.Greater(t, uintptr(unsafe.Pointer(es.At(i-1).orig)), uintptr(unsafe.Pointer(es.At(i).orig)))
	}
}

func generateTestScopeLogsSlice() ScopeLogsSlice {
	es := NewScopeLogsSlice()
	fillTestScopeLogsSlice(es)
	return es
}

func fillTestScopeLogsSlice(es ScopeLogsSlice) {
	*es.orig = make([]*otlplogs.ScopeLogs, 7)
	for i := 0; i < 7; i++ {
		(*es.orig)[i] = &otlplogs.ScopeLogs{}
		fillTestScopeLogs(newScopeLogs((*es.orig)[i], es.state))
	}
}
