// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package plog

import (
"iter"
"math"
"slices"
"sort"
"strings"

"go.opentelemetry.io/collector/pdata/internal"
"go.opentelemetry.io/collector/pdata/internal/data"
otlplogs "go.opentelemetry.io/collector/pdata/internal/data/protogen/logs/v1"
"go.opentelemetry.io/collector/pdata/pcommon"

)

// LogRecord are experimental implementation of OpenTelemetry Log Data Model.

//
// This is a reference type, if passed by value and callee modifies it the
// caller will see the modification.
//
// Must use NewLogRecord function to create new instances.
// Important: zero-initialized instance is not valid for use.
type LogRecord struct {
	orig *otlplogs.LogRecord
	state *internal.State
}

func newLogRecord(orig *otlplogs.LogRecord, state *internal.State) LogRecord {
	return LogRecord{orig: orig, state: state}
}

// NewLogRecord creates a new empty LogRecord.
//
// This must be used only in testing code. Users should use "AppendEmpty" when part of a Slice,
// OR directly access the member if this is embedded in another struct.
func NewLogRecord() LogRecord {
	state := internal.StateMutable
	return newLogRecord(&otlplogs.LogRecord{}, &state)
}

// MoveTo moves all properties from the current struct overriding the destination and
// resetting the current instance to its zero value
func (ms LogRecord) MoveTo(dest LogRecord) {
	ms.state.AssertMutable()
	dest.state.AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if ms.orig == dest.orig {
		return
	}
	*dest.orig = *ms.orig
	*ms.orig = otlplogs.LogRecord{}
}



// ObservedTimestamp returns the observedtimestamp associated with this LogRecord.
func (ms LogRecord) ObservedTimestamp() pcommon.Timestamp {
	return pcommon.Timestamp(ms.orig.ObservedTimeUnixNano)
}

// SetObservedTimestamp replaces the observedtimestamp associated with this LogRecord.
func (ms LogRecord) SetObservedTimestamp(v pcommon.Timestamp) {
	ms.state.AssertMutable()
	ms.orig.ObservedTimeUnixNano = uint64(v)
}
// Timestamp returns the timestamp associated with this LogRecord.
func (ms LogRecord) Timestamp() pcommon.Timestamp {
	return pcommon.Timestamp(ms.orig.TimeUnixNano)
}

// SetTimestamp replaces the timestamp associated with this LogRecord.
func (ms LogRecord) SetTimestamp(v pcommon.Timestamp) {
	ms.state.AssertMutable()
	ms.orig.TimeUnixNano = uint64(v)
}
// TraceID returns the traceid associated with this LogRecord.
func (ms LogRecord) TraceID() pcommon.TraceID {
	return pcommon.TraceID(ms.orig.TraceId)
}

// SetTraceID replaces the traceid associated with this LogRecord.
func (ms LogRecord) SetTraceID(v pcommon.TraceID) {
	ms.state.AssertMutable()
	ms.orig.TraceId = data.TraceID(v)
}
// SpanID returns the spanid associated with this LogRecord.
func (ms LogRecord) SpanID() pcommon.SpanID {
	return pcommon.SpanID(ms.orig.SpanId)
}

// SetSpanID replaces the spanid associated with this LogRecord.
func (ms LogRecord) SetSpanID(v pcommon.SpanID) {
	ms.state.AssertMutable()
	ms.orig.SpanId = data.SpanID(v)
}
// Flags returns the flags associated with this LogRecord.
func (ms LogRecord) Flags() LogRecordFlags {
	return LogRecordFlags(ms.orig.Flags)
}

// SetFlags replaces the flags associated with this LogRecord.
func (ms LogRecord) SetFlags(v LogRecordFlags) {
	ms.state.AssertMutable()
	ms.orig.Flags = uint32(v)
}
// EventName returns the eventname associated with this LogRecord.
func (ms LogRecord) EventName() string {
	return ms.orig.EventName
}

// SetEventName replaces the eventname associated with this LogRecord.
func (ms LogRecord) SetEventName(v string) {
	ms.state.AssertMutable()
	ms.orig.EventName = v
}
// SeverityText returns the severitytext associated with this LogRecord.
func (ms LogRecord) SeverityText() string {
	return ms.orig.SeverityText
}

// SetSeverityText replaces the severitytext associated with this LogRecord.
func (ms LogRecord) SetSeverityText(v string) {
	ms.state.AssertMutable()
	ms.orig.SeverityText = v
}
// SeverityNumber returns the severitynumber associated with this LogRecord.
func (ms LogRecord) SeverityNumber() SeverityNumber {
	return SeverityNumber(ms.orig.SeverityNumber)
}

// SetSeverityNumber replaces the severitynumber associated with this LogRecord.
func (ms LogRecord) SetSeverityNumber(v SeverityNumber) {
	ms.state.AssertMutable()
	ms.orig.SeverityNumber = otlplogs.SeverityNumber(v)
}
// Body returns the body associated with this LogRecord.
func (ms LogRecord) Body() pcommon.Value {
	return pcommon.Value(internal.NewValue(&ms.orig.Body, ms.state))
}
// Attributes returns the Attributes associated with this LogRecord.
func (ms LogRecord) Attributes() pcommon.Map {
	return pcommon.Map(internal.NewMap(&ms.orig.Attributes, ms.state))
}
// DroppedAttributesCount returns the droppedattributescount associated with this LogRecord.
func (ms LogRecord) DroppedAttributesCount() uint32 {
	return ms.orig.DroppedAttributesCount
}

// SetDroppedAttributesCount replaces the droppedattributescount associated with this LogRecord.
func (ms LogRecord) SetDroppedAttributesCount(v uint32) {
	ms.state.AssertMutable()
	ms.orig.DroppedAttributesCount = v
}


// CopyTo copies all properties from the current struct overriding the destination.
func (ms LogRecord) CopyTo(dest LogRecord) {
	dest.state.AssertMutable()
    copyOrigLogRecord(dest.orig, ms.orig)
}

func copyOrigLogRecord(dest, src *otlplogs.LogRecord) {
	dest.ObservedTimeUnixNano = src.ObservedTimeUnixNano
	dest.TimeUnixNano = src.TimeUnixNano
	dest.TraceId = src.TraceId
	dest.SpanId = src.SpanId
	dest.Flags = src.Flags
	dest.EventName = src.EventName
	dest.SeverityText = src.SeverityText
	dest.SeverityNumber = src.SeverityNumber
	internal.CopyOrigValue(&dest.Body, &src.Body)
	dest.Attributes =internal.CopyOrigMap(dest.Attributes, src.Attributes)
	dest.DroppedAttributesCount = src.DroppedAttributesCount
}

// Equal checks equality with another LogRecord.
func (ms LogRecord) Equal(val LogRecord) bool {
	return ms.ObservedTimestamp() == val.ObservedTimestamp() &&
		ms.Timestamp() == val.Timestamp() &&
		ms.TraceID() == val.TraceID() &&
		ms.SpanID() == val.SpanID() &&
		ms.Flags() == val.Flags() &&
		ms.EventName() == val.EventName() &&
		ms.SeverityText() == val.SeverityText() &&
		ms.SeverityNumber() == val.SeverityNumber() &&
		ms.Body().Equal(val.Body()) &&
		ms.Attributes().Equal(val.Attributes()) &&
		ms.DroppedAttributesCount() == val.DroppedAttributesCount()
}
