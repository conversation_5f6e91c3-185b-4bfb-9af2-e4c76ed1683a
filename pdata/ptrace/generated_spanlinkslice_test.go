// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package ptrace

import (
	"testing"
	"unsafe"

	"github.com/stretchr/testify/assert"

	"go.opentelemetry.io/collector/pdata/internal"
	"go.opentelemetry.io/collector/pdata/internal/data"
	otlptrace "go.opentelemetry.io/collector/pdata/internal/data/protogen/trace/v1"
	"go.opentelemetry.io/collector/pdata/pcommon"
)

func TestSpanLinkSlice(t *testing.T) {
	es := NewSpanLinkSlice()
	assert.Equal(t, 0, es.Len())
	state := internal.StateMutable
	es = newSpanLinkSlice(&[]*otlptrace.Span_Link{}, &state)
	assert.Equal(t, 0, es.Len())

	emptyVal := NewSpanLink()
	testVal := generateTestSpanLink()
	for i := 0; i < 7; i++ {
		el := es.AppendEmpty()
		assert.Equal(t, emptyVal, es.At(i))
		fillTestSpanLink(el)
		assert.Equal(t, testVal, es.At(i))
	}
	assert.Equal(t, 7, es.Len())
}

func TestSpanLinkSliceReadOnly(t *testing.T) {
	sharedState := internal.StateReadOnly
	es := newSpanLinkSlice(&[]*otlptrace.Span_Link{}, &sharedState)
	assert.Equal(t, 0, es.Len())
	assert.Panics(t, func() { es.AppendEmpty() })
	assert.Panics(t, func() { es.EnsureCapacity(2) })
	es2 := NewSpanLinkSlice()
	es.CopyTo(es2)
	assert.Panics(t, func() { es2.CopyTo(es) })
	assert.Panics(t, func() { es.MoveAndAppendTo(es2) })
	assert.Panics(t, func() { es2.MoveAndAppendTo(es) })
}

func TestSpanLinkSlice_CopyTo(t *testing.T) {
	dest := NewSpanLinkSlice()
	// Test CopyTo to empty
	NewSpanLinkSlice().CopyTo(dest)
	assert.Equal(t, NewSpanLinkSlice(), dest)

	// Test CopyTo larger slice
	generateTestSpanLinkSlice().CopyTo(dest)
	assert.Equal(t, generateTestSpanLinkSlice(), dest)

	// Test CopyTo same size slice
	generateTestSpanLinkSlice().CopyTo(dest)
	assert.Equal(t, generateTestSpanLinkSlice(), dest)
}

func TestSpanLinkSlice_EnsureCapacity(t *testing.T) {
	es := generateTestSpanLinkSlice()

	// Test ensure smaller capacity.
	const ensureSmallLen = 4
	es.EnsureCapacity(ensureSmallLen)
	assert.Less(t, ensureSmallLen, es.Len())
	assert.Equal(t, es.Len(), cap(*es.orig))
	assert.Equal(t, generateTestSpanLinkSlice(), es)

	// Test ensure larger capacity
	const ensureLargeLen = 9
	es.EnsureCapacity(ensureLargeLen)
	assert.Less(t, generateTestSpanLinkSlice().Len(), ensureLargeLen)
	assert.Equal(t, ensureLargeLen, cap(*es.orig))
	assert.Equal(t, generateTestSpanLinkSlice(), es)
}

func TestSpanLinkSlice_MoveAndAppendTo(t *testing.T) {
	// Test MoveAndAppendTo to empty
	expectedSlice := generateTestSpanLinkSlice()
	dest := NewSpanLinkSlice()
	src := generateTestSpanLinkSlice()
	src.MoveAndAppendTo(dest)
	assert.Equal(t, generateTestSpanLinkSlice(), dest)
	assert.Equal(t, 0, src.Len())
	assert.Equal(t, expectedSlice.Len(), dest.Len())

	// Test MoveAndAppendTo empty slice
	src.MoveAndAppendTo(dest)
	assert.Equal(t, generateTestSpanLinkSlice(), dest)
	assert.Equal(t, 0, src.Len())
	assert.Equal(t, expectedSlice.Len(), dest.Len())

	// Test MoveAndAppendTo not empty slice
	generateTestSpanLinkSlice().MoveAndAppendTo(dest)
	assert.Equal(t, 2*expectedSlice.Len(), dest.Len())
	for i := 0; i < expectedSlice.Len(); i++ {
		assert.Equal(t, expectedSlice.At(i), dest.At(i))
		assert.Equal(t, expectedSlice.At(i), dest.At(i+expectedSlice.Len()))
	}

	dest.MoveAndAppendTo(dest)
	assert.Equal(t, 2*expectedSlice.Len(), dest.Len())
	for i := 0; i < expectedSlice.Len(); i++ {
		assert.Equal(t, expectedSlice.At(i), dest.At(i))
		assert.Equal(t, expectedSlice.At(i), dest.At(i+expectedSlice.Len()))
	}
}

func TestSpanLinkSlice_RemoveIf(t *testing.T) {
	// Test RemoveIf on empty slice
	emptySlice := NewSpanLinkSlice()
	emptySlice.RemoveIf(func(el SpanLink) bool {
		t.Fail()
		return false
	})

	// Test RemoveIf
	filtered := generateTestSpanLinkSlice()
	pos := 0
	filtered.RemoveIf(func(el SpanLink) bool {
		pos++
		return pos%3 == 0
	})
	assert.Equal(t, 5, filtered.Len())
}

func TestSpanLinkSliceAll(t *testing.T) {
	ms := generateTestSpanLinkSlice()
	assert.NotEmpty(t, ms.Len())

	var c int
	for i, v := range ms.All() {
		assert.Equal(t, ms.At(i), v, "element should match")
		c++
	}
	assert.Equal(t, ms.Len(), c, "All elements should have been visited")
}

func TestSpanLinkSlice_Equal(t *testing.T) {
	es1 := NewSpanLinkSlice()
	es2 := NewSpanLinkSlice()
	assert.True(t, es1.Equal(es2))

	es1 = generateTestSpanLinkSlice()
	es2 = generateTestSpanLinkSlice()
	assert.True(t, es1.Equal(es2))

	es2 = NewSpanLinkSlice()
	assert.False(t, es1.Equal(es2))

	es2.AppendEmpty()
	assert.False(t, es1.Equal(es2))

	// Test element-wise inequality - create two slices with same length but different elements
	if es1.Len() > 0 {
		es1 = generateTestSpanLinkSlice()
		es2 = NewSpanLinkSlice()
		// Make es2 same length as es1 but with empty elements
		for i := 0; i < es1.Len(); i++ {
			es2.AppendEmpty()
		}
		// This should return false since elements are different
		assert.False(t, es1.Equal(es2))
	}
}

func TestSpanLinkSlice_Sort(t *testing.T) {
	es := generateTestSpanLinkSlice()
	es.Sort(func(a, b SpanLink) bool {
		return uintptr(unsafe.Pointer(a.orig)) < uintptr(unsafe.Pointer(b.orig))
	})
	for i := 1; i < es.Len(); i++ {
		assert.Less(t, uintptr(unsafe.Pointer(es.At(i-1).orig)), uintptr(unsafe.Pointer(es.At(i).orig)))
	}
	es.Sort(func(a, b SpanLink) bool {
		return uintptr(unsafe.Pointer(a.orig)) > uintptr(unsafe.Pointer(b.orig))
	})
	for i := 1; i < es.Len(); i++ {
		assert.Greater(t, uintptr(unsafe.Pointer(es.At(i-1).orig)), uintptr(unsafe.Pointer(es.At(i).orig)))
	}
}

func generateTestSpanLinkSlice() SpanLinkSlice {
	es := NewSpanLinkSlice()
	fillTestSpanLinkSlice(es)
	return es
}

func fillTestSpanLinkSlice(es SpanLinkSlice) {
	*es.orig = make([]*otlptrace.Span_Link, 7)
	for i := 0; i < 7; i++ {
		(*es.orig)[i] = &otlptrace.Span_Link{}
		fillTestSpanLink(newSpanLink((*es.orig)[i], es.state))
	}
}
