// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package ptrace

import (
	"testing"
	"unsafe"

	"github.com/stretchr/testify/assert"

	"go.opentelemetry.io/collector/pdata/internal"
	"go.opentelemetry.io/collector/pdata/internal/data"
	otlptrace "go.opentelemetry.io/collector/pdata/internal/data/protogen/trace/v1"
	"go.opentelemetry.io/collector/pdata/pcommon"
)

func TestSpanEventSlice(t *testing.T) {
	es := NewSpanEventSlice()
	assert.Equal(t, 0, es.Len())
	state := internal.StateMutable
	es = newSpanEventSlice(&[]*otlptrace.Span_Event{}, &state)
	assert.Equal(t, 0, es.Len())

	emptyVal := NewSpanEvent()
	testVal := generateTestSpanEvent()
	for i := 0; i < 7; i++ {
		el := es.AppendEmpty()
		assert.Equal(t, emptyVal, es.At(i))
		fillTestSpanEvent(el)
		assert.Equal(t, testVal, es.At(i))
	}
	assert.Equal(t, 7, es.Len())
}

func TestSpanEventSliceReadOnly(t *testing.T) {
	sharedState := internal.StateReadOnly
	es := newSpanEventSlice(&[]*otlptrace.Span_Event{}, &sharedState)
	assert.Equal(t, 0, es.Len())
	assert.Panics(t, func() { es.AppendEmpty() })
	assert.Panics(t, func() { es.EnsureCapacity(2) })
	es2 := NewSpanEventSlice()
	es.CopyTo(es2)
	assert.Panics(t, func() { es2.CopyTo(es) })
	assert.Panics(t, func() { es.MoveAndAppendTo(es2) })
	assert.Panics(t, func() { es2.MoveAndAppendTo(es) })
}

func TestSpanEventSlice_CopyTo(t *testing.T) {
	dest := NewSpanEventSlice()
	// Test CopyTo to empty
	NewSpanEventSlice().CopyTo(dest)
	assert.Equal(t, NewSpanEventSlice(), dest)

	// Test CopyTo larger slice
	generateTestSpanEventSlice().CopyTo(dest)
	assert.Equal(t, generateTestSpanEventSlice(), dest)

	// Test CopyTo same size slice
	generateTestSpanEventSlice().CopyTo(dest)
	assert.Equal(t, generateTestSpanEventSlice(), dest)
}

func TestSpanEventSlice_EnsureCapacity(t *testing.T) {
	es := generateTestSpanEventSlice()

	// Test ensure smaller capacity.
	const ensureSmallLen = 4
	es.EnsureCapacity(ensureSmallLen)
	assert.Less(t, ensureSmallLen, es.Len())
	assert.Equal(t, es.Len(), cap(*es.orig))
	assert.Equal(t, generateTestSpanEventSlice(), es)

	// Test ensure larger capacity
	const ensureLargeLen = 9
	es.EnsureCapacity(ensureLargeLen)
	assert.Less(t, generateTestSpanEventSlice().Len(), ensureLargeLen)
	assert.Equal(t, ensureLargeLen, cap(*es.orig))
	assert.Equal(t, generateTestSpanEventSlice(), es)
}

func TestSpanEventSlice_MoveAndAppendTo(t *testing.T) {
	// Test MoveAndAppendTo to empty
	expectedSlice := generateTestSpanEventSlice()
	dest := NewSpanEventSlice()
	src := generateTestSpanEventSlice()
	src.MoveAndAppendTo(dest)
	assert.Equal(t, generateTestSpanEventSlice(), dest)
	assert.Equal(t, 0, src.Len())
	assert.Equal(t, expectedSlice.Len(), dest.Len())

	// Test MoveAndAppendTo empty slice
	src.MoveAndAppendTo(dest)
	assert.Equal(t, generateTestSpanEventSlice(), dest)
	assert.Equal(t, 0, src.Len())
	assert.Equal(t, expectedSlice.Len(), dest.Len())

	// Test MoveAndAppendTo not empty slice
	generateTestSpanEventSlice().MoveAndAppendTo(dest)
	assert.Equal(t, 2*expectedSlice.Len(), dest.Len())
	for i := 0; i < expectedSlice.Len(); i++ {
		assert.Equal(t, expectedSlice.At(i), dest.At(i))
		assert.Equal(t, expectedSlice.At(i), dest.At(i+expectedSlice.Len()))
	}

	dest.MoveAndAppendTo(dest)
	assert.Equal(t, 2*expectedSlice.Len(), dest.Len())
	for i := 0; i < expectedSlice.Len(); i++ {
		assert.Equal(t, expectedSlice.At(i), dest.At(i))
		assert.Equal(t, expectedSlice.At(i), dest.At(i+expectedSlice.Len()))
	}
}

func TestSpanEventSlice_RemoveIf(t *testing.T) {
	// Test RemoveIf on empty slice
	emptySlice := NewSpanEventSlice()
	emptySlice.RemoveIf(func(el SpanEvent) bool {
		t.Fail()
		return false
	})

	// Test RemoveIf
	filtered := generateTestSpanEventSlice()
	pos := 0
	filtered.RemoveIf(func(el SpanEvent) bool {
		pos++
		return pos%3 == 0
	})
	assert.Equal(t, 5, filtered.Len())
}

func TestSpanEventSliceAll(t *testing.T) {
	ms := generateTestSpanEventSlice()
	assert.NotEmpty(t, ms.Len())

	var c int
	for i, v := range ms.All() {
		assert.Equal(t, ms.At(i), v, "element should match")
		c++
	}
	assert.Equal(t, ms.Len(), c, "All elements should have been visited")
}

func TestSpanEventSlice_Equal(t *testing.T) {
	es1 := NewSpanEventSlice()
	es2 := NewSpanEventSlice()
	assert.True(t, es1.Equal(es2))

	es1 = generateTestSpanEventSlice()
	es2 = generateTestSpanEventSlice()
	assert.True(t, es1.Equal(es2))

	es2 = NewSpanEventSlice()
	assert.False(t, es1.Equal(es2))

	es2.AppendEmpty()
	assert.False(t, es1.Equal(es2))

	// Test element-wise inequality - create two slices with same length but different elements
	if es1.Len() > 0 {
		es1 = generateTestSpanEventSlice()
		es2 = NewSpanEventSlice()
		// Make es2 same length as es1 but with empty elements
		for i := 0; i < es1.Len(); i++ {
			es2.AppendEmpty()
		}
		// This should return false since elements are different
		assert.False(t, es1.Equal(es2))
	}
}

func TestSpanEventSlice_Sort(t *testing.T) {
	es := generateTestSpanEventSlice()
	es.Sort(func(a, b SpanEvent) bool {
		return uintptr(unsafe.Pointer(a.orig)) < uintptr(unsafe.Pointer(b.orig))
	})
	for i := 1; i < es.Len(); i++ {
		assert.Less(t, uintptr(unsafe.Pointer(es.At(i-1).orig)), uintptr(unsafe.Pointer(es.At(i).orig)))
	}
	es.Sort(func(a, b SpanEvent) bool {
		return uintptr(unsafe.Pointer(a.orig)) > uintptr(unsafe.Pointer(b.orig))
	})
	for i := 1; i < es.Len(); i++ {
		assert.Greater(t, uintptr(unsafe.Pointer(es.At(i-1).orig)), uintptr(unsafe.Pointer(es.At(i).orig)))
	}
}

func generateTestSpanEventSlice() SpanEventSlice {
	es := NewSpanEventSlice()
	fillTestSpanEventSlice(es)
	return es
}

func fillTestSpanEventSlice(es SpanEventSlice) {
	*es.orig = make([]*otlptrace.Span_Event, 7)
	for i := 0; i < 7; i++ {
		(*es.orig)[i] = &otlptrace.Span_Event{}
		fillTestSpanEvent(newSpanEvent((*es.orig)[i], es.state))
	}
}
