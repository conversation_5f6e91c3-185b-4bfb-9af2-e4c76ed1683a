// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package ptrace

import (
	"go.opentelemetry.io/collector/pdata/internal"
	"go.opentelemetry.io/collector/pdata/internal/data"
	otlptrace "go.opentelemetry.io/collector/pdata/internal/data/protogen/trace/v1"
	"go.opentelemetry.io/collector/pdata/pcommon"
)

// SpanLink is a pointer from the current span to another span in the same trace or in a
// different trace.
// See Link definition in OTLP: https://github.com/open-telemetry/opentelemetry-proto/blob/main/opentelemetry/proto/trace/v1/trace.proto
//
// This is a reference type, if passed by value and callee modifies it the
// caller will see the modification.
//
// Must use NewSpanLink function to create new instances.
// Important: zero-initialized instance is not valid for use.
type SpanLink struct {
	orig  *otlptrace.Span_Link
	state *internal.State
}

func newSpanLink(orig *otlptrace.Span_Link, state *internal.State) SpanLink {
	return SpanLink{orig: orig, state: state}
}

// NewSpanLink creates a new empty SpanLink.
//
// This must be used only in testing code. Users should use "AppendEmpty" when part of a Slice,
// OR directly access the member if this is embedded in another struct.
func NewSpanLink() SpanLink {
	state := internal.StateMutable
	return newSpanLink(&otlptrace.Span_Link{}, &state)
}

// MoveTo moves all properties from the current struct overriding the destination and
// resetting the current instance to its zero value
func (ms SpanLink) MoveTo(dest SpanLink) {
	ms.state.AssertMutable()
	dest.state.AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if ms.orig == dest.orig {
		return
	}
	*dest.orig = *ms.orig
	*ms.orig = otlptrace.Span_Link{}
}

// TraceID returns the traceid associated with this SpanLink.
func (ms SpanLink) TraceID() pcommon.TraceID {
	return pcommon.TraceID(ms.orig.TraceId)
}

// SetTraceID replaces the traceid associated with this SpanLink.
func (ms SpanLink) SetTraceID(v pcommon.TraceID) {
	ms.state.AssertMutable()
	ms.orig.TraceId = data.TraceID(v)
}

// SpanID returns the spanid associated with this SpanLink.
func (ms SpanLink) SpanID() pcommon.SpanID {
	return pcommon.SpanID(ms.orig.SpanId)
}

// SetSpanID replaces the spanid associated with this SpanLink.
func (ms SpanLink) SetSpanID(v pcommon.SpanID) {
	ms.state.AssertMutable()
	ms.orig.SpanId = data.SpanID(v)
}

// TraceState returns the tracestate associated with this SpanLink.
func (ms SpanLink) TraceState() pcommon.TraceState {
	return pcommon.TraceState(internal.NewTraceState(&ms.orig.TraceState, ms.state))
}

// Flags returns the flags associated with this SpanLink.
func (ms SpanLink) Flags() uint32 {
	return ms.orig.Flags
}

// SetFlags replaces the flags associated with this SpanLink.
func (ms SpanLink) SetFlags(v uint32) {
	ms.state.AssertMutable()
	ms.orig.Flags = v
}

// Attributes returns the Attributes associated with this SpanLink.
func (ms SpanLink) Attributes() pcommon.Map {
	return pcommon.Map(internal.NewMap(&ms.orig.Attributes, ms.state))
}

// DroppedAttributesCount returns the droppedattributescount associated with this SpanLink.
func (ms SpanLink) DroppedAttributesCount() uint32 {
	return ms.orig.DroppedAttributesCount
}

// SetDroppedAttributesCount replaces the droppedattributescount associated with this SpanLink.
func (ms SpanLink) SetDroppedAttributesCount(v uint32) {
	ms.state.AssertMutable()
	ms.orig.DroppedAttributesCount = v
}

// CopyTo copies all properties from the current struct overriding the destination.
func (ms SpanLink) CopyTo(dest SpanLink) {
	dest.state.AssertMutable()
	copyOrigSpanLink(dest.orig, ms.orig)
}

func copyOrigSpanLink(dest, src *otlptrace.Span_Link) {
	dest.TraceId = src.TraceId
	dest.SpanId = src.SpanId
	internal.CopyOrigTraceState(&dest.TraceState, &src.TraceState)
	dest.Flags = src.Flags
	dest.Attributes = internal.CopyOrigMap(dest.Attributes, src.Attributes)
	dest.DroppedAttributesCount = src.DroppedAttributesCount
}

// Equal checks equality with another SpanLink.
// Optionally accepts CompareOption arguments to customize comparison behavior.
func (ms SpanLink) Equal(val SpanLink, opts ...CompareOption) bool {
	cfg := NewCompareConfig(opts)
	return ms.TraceID() == val.TraceID() &&
		ms.SpanID() == val.SpanID() &&
		ms.TraceState().Equal(val.TraceState()) &&
		ms.Flags() == val.Flags() &&
		ms.Attributes().Equal(val.Attributes()) &&
		ms.DroppedAttributesCount() == val.DroppedAttributesCount()
}
