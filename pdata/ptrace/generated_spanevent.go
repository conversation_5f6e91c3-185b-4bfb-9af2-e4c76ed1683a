// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package ptrace

import (
	"iter"
	"math"
	"slices"
	"sort"
	"strings"

	"go.opentelemetry.io/collector/pdata/internal"
	"go.opentelemetry.io/collector/pdata/internal/data"
	otlptrace "go.opentelemetry.io/collector/pdata/internal/data/protogen/trace/v1"
	"go.opentelemetry.io/collector/pdata/pcommon"
)

// SpanEvent is a time-stamped annotation of the span, consisting of user-supplied
// text description and key-value pairs. See OTLP for event definition.
//
// This is a reference type, if passed by value and callee modifies it the
// caller will see the modification.
//
// Must use NewSpanEvent function to create new instances.
// Important: zero-initialized instance is not valid for use.
type SpanEvent struct {
	orig  *otlptrace.Span_Event
	state *internal.State
}

func newSpanEvent(orig *otlptrace.Span_Event, state *internal.State) SpanEvent {
	return SpanEvent{orig: orig, state: state}
}

// NewSpanEvent creates a new empty SpanEvent.
//
// This must be used only in testing code. Users should use "AppendEmpty" when part of a Slice,
// OR directly access the member if this is embedded in another struct.
func NewSpanEvent() SpanEvent {
	state := internal.StateMutable
	return newSpanEvent(&otlptrace.Span_Event{}, &state)
}

// MoveTo moves all properties from the current struct overriding the destination and
// resetting the current instance to its zero value
func (ms SpanEvent) MoveTo(dest SpanEvent) {
	ms.state.AssertMutable()
	dest.state.AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if ms.orig == dest.orig {
		return
	}
	*dest.orig = *ms.orig
	*ms.orig = otlptrace.Span_Event{}
}

// Timestamp returns the timestamp associated with this SpanEvent.
func (ms SpanEvent) Timestamp() pcommon.Timestamp {
	return pcommon.Timestamp(ms.orig.TimeUnixNano)
}

// SetTimestamp replaces the timestamp associated with this SpanEvent.
func (ms SpanEvent) SetTimestamp(v pcommon.Timestamp) {
	ms.state.AssertMutable()
	ms.orig.TimeUnixNano = uint64(v)
}

// Name returns the name associated with this SpanEvent.
func (ms SpanEvent) Name() string {
	return ms.orig.Name
}

// SetName replaces the name associated with this SpanEvent.
func (ms SpanEvent) SetName(v string) {
	ms.state.AssertMutable()
	ms.orig.Name = v
}

// Attributes returns the Attributes associated with this SpanEvent.
func (ms SpanEvent) Attributes() pcommon.Map {
	return pcommon.Map(internal.NewMap(&ms.orig.Attributes, ms.state))
}

// DroppedAttributesCount returns the droppedattributescount associated with this SpanEvent.
func (ms SpanEvent) DroppedAttributesCount() uint32 {
	return ms.orig.DroppedAttributesCount
}

// SetDroppedAttributesCount replaces the droppedattributescount associated with this SpanEvent.
func (ms SpanEvent) SetDroppedAttributesCount(v uint32) {
	ms.state.AssertMutable()
	ms.orig.DroppedAttributesCount = v
}

// CopyTo copies all properties from the current struct overriding the destination.
func (ms SpanEvent) CopyTo(dest SpanEvent) {
	dest.state.AssertMutable()
	copyOrigSpanEvent(dest.orig, ms.orig)
}

func copyOrigSpanEvent(dest, src *otlptrace.Span_Event) {
	dest.TimeUnixNano = src.TimeUnixNano
	dest.Name = src.Name
	dest.Attributes = internal.CopyOrigMap(dest.Attributes, src.Attributes)
	dest.DroppedAttributesCount = src.DroppedAttributesCount
}

// Equal checks equality with another SpanEvent.
// Optionally accepts CompareOption arguments to customize comparison behavior.
func (ms SpanEvent) Equal(val SpanEvent, opts ...CompareOption) bool {
	cfg := NewCompareConfig(opts)
	return (cfg.ShouldIgnoreField("Timestamp") || ms.Timestamp() == val.Timestamp()) &&
		(cfg.ShouldIgnoreField("Name") || ms.Name() == val.Name()) &&
		(cfg.ShouldIgnoreField("Attributes") || ms.Attributes().Equal(val.Attributes())) &&
		(cfg.ShouldIgnoreField("DroppedAttributesCount") || ms.DroppedAttributesCount() == val.DroppedAttributesCount())
}
