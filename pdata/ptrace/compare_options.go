// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package ptrace

import (
	"math"
	"strings"
)

// CompareOption defines customization for equality comparison
type CompareOption interface {
	apply(*CompareConfig)
}

type CompareConfig struct {
	floatTolerance float64
	ignoredFields  map[string]bool
	ignoredPaths   map[string]bool
}

func NewCompareConfig(opts []CompareOption) *CompareConfig {
	cfg := &CompareConfig{
		floatTolerance: 1e-10, // default tolerance for floating point precision issues
		ignoredFields:  make(map[string]bool),
		ignoredPaths:   make(map[string]bool),
	}
	for _, opt := range opts {
		opt.apply(cfg)
	}
	return cfg
}

// floatToleranceOption sets the tolerance for float64 comparisons
type floatToleranceOption struct {
	tolerance float64
}

func (o floatToleranceOption) apply(cfg *CompareConfig) {
	cfg.floatTolerance = o.tolerance
}

// FloatTolerance sets the tolerance for floating point comparisons
func FloatTolerance(tolerance float64) CompareOption {
	return floatToleranceOption{tolerance: tolerance}
}

// ignoreFieldsOption ignores specific field names during comparison
type ignoreFieldsOption struct {
	fields []string
}

func (o ignoreFieldsOption) apply(cfg *CompareConfig) {
	for _, field := range o.fields {
		cfg.ignoredFields[field] = true
	}
}

// IgnoreFields ignores specific field names during comparison
func IgnoreFields(fields ...string) CompareOption {
	return ignoreFieldsOption{fields: fields}
}

// ignorePathsOption ignores specific nested paths during comparison
type ignorePathsOption struct {
	paths []string
}

func (o ignorePathsOption) apply(cfg *CompareConfig) {
	for _, path := range o.paths {
		cfg.ignoredPaths[path] = true
	}
}

// IgnorePaths ignores specific nested paths during comparison
// Path format: "field.subfield" or "field.*" for wildcards
func IgnorePaths(paths ...string) CompareOption {
	return ignorePathsOption{paths: paths}
}

// ShouldIgnoreField checks if a field should be ignored
func (cfg *CompareConfig) ShouldIgnoreField(fieldName string) bool {
	return cfg.ignoredFields[fieldName]
}

// ShouldIgnorePath checks if a path should be ignored
func (cfg *CompareConfig) ShouldIgnorePath(path string) bool {
	if cfg.ignoredPaths[path] {
		return true
	}
	// Check for wildcard matches
	for ignoredPath := range cfg.ignoredPaths {
		if strings.HasSuffix(ignoredPath, ".*") {
			prefix := strings.TrimSuffix(ignoredPath, ".*")
			if strings.HasPrefix(path, prefix) {
				return true
			}
		}
	}
	return false
}

// CompareFloat64 compares two float64 values with tolerance
// CompareFloat64 compares two float64 values using the configured tolerance.
func (cfg *CompareConfig) CompareFloat64(a, b float64) bool {
	return math.Abs(a-b) <= cfg.floatTolerance
}