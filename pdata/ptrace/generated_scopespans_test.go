// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package ptrace

import (
"testing"
"unsafe"

"github.com/stretchr/testify/assert"

"go.opentelemetry.io/collector/pdata/internal"
"go.opentelemetry.io/collector/pdata/internal/data"
otlptrace "go.opentelemetry.io/collector/pdata/internal/data/protogen/trace/v1"
"go.opentelemetry.io/collector/pdata/pcommon"

)

func TestScopeSpans_MoveTo(t *testing.T) {
	ms := generateTestScopeSpans()
	dest := NewScopeSpans()
	ms.MoveTo(dest)
	assert.Equal(t, NewScopeSpans(), ms)
	assert.Equal(t, generateTestScopeSpans(), dest)
	dest.MoveTo(dest)
	assert.Equal(t, generateTestScopeSpans(), dest)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.MoveTo(newScopeSpans(&otlptrace.ScopeSpans{}, &sharedState)) })
	assert.Panics(t, func() { newScopeSpans(&otlptrace.ScopeSpans{}, &sharedState).MoveTo(dest) })
}

func TestScopeSpans_CopyTo(t *testing.T) {
	ms := NewScopeSpans()
	orig := NewScopeSpans()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	orig = generateTestScopeSpans()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.CopyTo(newScopeSpans(&otlptrace.ScopeSpans{}, &sharedState)) })
}

func TestScopeSpans_Equal(t *testing.T) {
	ms1 := NewScopeSpans()
	ms2 := NewScopeSpans()
	assert.True(t, ms1.Equal(ms2))

	ms1 = generateTestScopeSpans()
	ms2 = generateTestScopeSpans()
	assert.True(t, ms1.Equal(ms2))

	ms2 = NewScopeSpans()
	assert.False(t, ms1.Equal(ms2))
}


func TestScopeSpans_Scope(t *testing.T) {
	ms := NewScopeSpans()
	internal.FillTestInstrumentationScope(internal.InstrumentationScope(ms.Scope()))
	assert.Equal(t, pcommon.InstrumentationScope(internal.GenerateTestInstrumentationScope()), ms.Scope())
}

func TestScopeSpans_SchemaUrl(t *testing.T) {
	ms := NewScopeSpans()
	assert.Empty(t, ms.SchemaUrl())
	ms.SetSchemaUrl("https://opentelemetry.io/schemas/1.5.0")
	assert.Equal(t, "https://opentelemetry.io/schemas/1.5.0", ms.SchemaUrl())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newScopeSpans(&otlptrace.ScopeSpans{}, &sharedState).SetSchemaUrl("https://opentelemetry.io/schemas/1.5.0") })
}

func TestScopeSpans_Spans(t *testing.T) {
	ms := NewScopeSpans()
	assert.Equal(t, NewSpanSlice(), ms.Spans())
	fillTestSpanSlice(ms.Spans())
	assert.Equal(t, generateTestSpanSlice(), ms.Spans())
}


func generateTestScopeSpans() ScopeSpans {
	tv := NewScopeSpans()
	fillTestScopeSpans(tv)
	return tv
}

func fillTestScopeSpans(tv ScopeSpans) {
	internal.FillTestInstrumentationScope(internal.NewInstrumentationScope(&tv.orig.Scope, tv.state))
	tv.orig.SchemaUrl = "https://opentelemetry.io/schemas/1.5.0"
	fillTestSpanSlice(newSpanSlice(&tv.orig.Spans, tv.state))
}

