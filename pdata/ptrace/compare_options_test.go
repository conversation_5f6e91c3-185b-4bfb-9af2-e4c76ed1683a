// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

package ptrace

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"go.opentelemetry.io/collector/pdata/pcommon"
)

func TestSpanCompareOptions(t *testing.T) {
	t.Run("ignore_fields", func(t *testing.T) {
		span1 := NewSpan()
		span1.SetName("test-span")
		span1.SetSpanID(pcommon.SpanID{1, 2, 3, 4, 5, 6, 7, 8})
		span1.SetTraceID(pcommon.TraceID{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16})
		span1.SetFlags(1)

		span2 := NewSpan()
		span2.SetName("test-span")
		span2.SetSpanID(pcommon.SpanID{1, 2, 3, 4, 5, 6, 7, 8})
		span2.SetTraceID(pcommon.TraceID{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16})
		span2.SetFlags(2) // Different flags

		// Should be different with default comparison
		assert.False(t, span1.Equal(span2), "Should be different with default comparison")

		// Should be equal when ignoring Flags field
		assert.True(t, span1.Equal(span2, IgnoreFields("Flags")),
			"Should be equal when ignoring Flags field")
	})

	t.Run("ignore_attributes", func(t *testing.T) {
		span1 := NewSpan()
		span1.SetName("test-span")
		span1.Attributes().PutStr("service.name", "my-service")
		span1.Attributes().PutStr("service.version", "1.0.0")

		span2 := NewSpan()
		span2.SetName("test-span")
		span2.Attributes().PutStr("service.name", "my-service")
		span2.Attributes().PutStr("service.version", "2.0.0") // Different version

		// Should be different with default comparison
		assert.False(t, span1.Equal(span2), "Should be different with default comparison")

		// Should be equal when ignoring the Attributes field entirely
		assert.True(t, span1.Equal(span2, IgnoreFields("Attributes")),
			"Should be equal when ignoring Attributes field")
	})

	t.Run("span_events_comparison", func(t *testing.T) {
		span1 := NewSpan()
		span1.SetName("test-span")
		event1 := span1.Events().AppendEmpty()
		event1.SetName("event1")
		event1.SetTimestamp(pcommon.Timestamp(1234567890))

		span2 := NewSpan()
		span2.SetName("test-span")
		event2 := span2.Events().AppendEmpty()
		event2.SetName("event1")
		event2.SetTimestamp(pcommon.Timestamp(9876543210)) // Different timestamp

		// Should be different with default comparison
		assert.False(t, span1.Equal(span2), "Should be different with default comparison")

		// Should be equal when ignoring Events field entirely
		assert.True(t, span1.Equal(span2, IgnoreFields("Events")),
			"Should be equal when ignoring Events field")
	})
}

func TestSpanEventCompareOptions(t *testing.T) {
	t.Run("ignore_timestamp", func(t *testing.T) {
		event1 := NewSpanEvent()
		event1.SetName("test-event")
		event1.SetTimestamp(pcommon.Timestamp(1234567890))
		event1.Attributes().PutStr("key", "value")

		event2 := NewSpanEvent()
		event2.SetName("test-event")
		event2.SetTimestamp(pcommon.Timestamp(9876543210)) // Different timestamp
		event2.Attributes().PutStr("key", "value")

		// Should be different with default comparison
		assert.False(t, event1.Equal(event2), "Should be different with default comparison")

		// Should be equal when ignoring timestamp
		assert.True(t, event1.Equal(event2, IgnoreFields("Timestamp")),
			"Should be equal when ignoring timestamp")
	})
}

func TestStatusCompareOptions(t *testing.T) {
	t.Run("ignore_message", func(t *testing.T) {
		status1 := NewStatus()
		status1.SetCode(StatusCodeError)
		status1.SetMessage("Error occurred")

		status2 := NewStatus()
		status2.SetCode(StatusCodeError)
		status2.SetMessage("Different error message")

		// Should be different with default comparison
		assert.False(t, status1.Equal(status2), "Should be different with default comparison")

		// Should be equal when ignoring message
		assert.True(t, status1.Equal(status2, IgnoreFields("Message")),
			"Should be equal when ignoring message")
	})
}
