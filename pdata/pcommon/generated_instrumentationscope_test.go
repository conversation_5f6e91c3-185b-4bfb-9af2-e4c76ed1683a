// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pcommon

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"go.opentelemetry.io/collector/pdata/internal"
)

func TestInstrumentationScope_MoveTo(t *testing.T) {
	ms := generateTestInstrumentationScope()
	dest := NewInstrumentationScope()
	ms.MoveTo(dest)
	assert.Equal(t, NewInstrumentationScope(), ms)
	assert.Equal(t, generateTestInstrumentationScope(), dest)
	dest.MoveTo(dest)
	assert.Equal(t, generateTestInstrumentationScope(), dest)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.MoveTo(newInstrumentationScope(&otlpcommon.InstrumentationScope{}, &sharedState)) })
	assert.Panics(t, func() { newInstrumentationScope(&otlpcommon.InstrumentationScope{}, &sharedState).MoveTo(dest) })
}

func TestInstrumentationScope_CopyTo(t *testing.T) {
	ms := NewInstrumentationScope()
	orig := NewInstrumentationScope()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	orig = generateTestInstrumentationScope()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.CopyTo(newInstrumentationScope(&otlpcommon.InstrumentationScope{}, &sharedState)) })
}

func TestInstrumentationScope_Equal(t *testing.T) {
	ms1 := NewInstrumentationScope()
	ms2 := NewInstrumentationScope()
	assert.True(t, ms1.Equal(ms2))

	ms1 = generateTestInstrumentationScope()
	ms2 = generateTestInstrumentationScope()
	assert.True(t, ms1.Equal(ms2))

	ms2 = NewInstrumentationScope()
	assert.False(t, ms1.Equal(ms2))

	// Test with CompareOption
	ms1 = generateTestInstrumentationScope()
	ms2 = generateTestInstrumentationScope()
	assert.True(t, ms1.Equal(ms2, FloatTolerance(0.1)))
}

func TestInstrumentationScope_Name(t *testing.T) {
	ms := NewInstrumentationScope()
	assert.Empty(t, ms.Name())
	ms.SetName("test_name")
	assert.Equal(t, "test_name", ms.Name())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newInstrumentationScope(&otlpcommon.InstrumentationScope{}, &sharedState).SetName("test_name") })
}

func TestInstrumentationScope_Version(t *testing.T) {
	ms := NewInstrumentationScope()
	assert.Empty(t, ms.Version())
	ms.SetVersion("test_version")
	assert.Equal(t, "test_version", ms.Version())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() {
		newInstrumentationScope(&otlpcommon.InstrumentationScope{}, &sharedState).SetVersion("test_version")
	})
}

func TestInstrumentationScope_Attributes(t *testing.T) {
	ms := NewInstrumentationScope()
	assert.Equal(t, NewMap(), ms.Attributes())
	internal.FillTestMap(internal.Map(ms.Attributes()))
	assert.Equal(t, Map(internal.GenerateTestMap()), ms.Attributes())
}

func TestInstrumentationScope_DroppedAttributesCount(t *testing.T) {
	ms := NewInstrumentationScope()
	assert.Equal(t, uint32(0), ms.DroppedAttributesCount())
	ms.SetDroppedAttributesCount(uint32(17))
	assert.Equal(t, uint32(17), ms.DroppedAttributesCount())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() {
		newInstrumentationScope(&otlpcommon.InstrumentationScope{}, &sharedState).SetDroppedAttributesCount(uint32(17))
	})
}

func generateTestInstrumentationScope() InstrumentationScope {
	return InstrumentationScope(internal.GenerateTestInstrumentationScope())
}
