// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pcommon

import (
	"iter"
	"slices"

	"go.opentelemetry.io/collector/pdata/internal"
	otlpcommon "go.opentelemetry.io/collector/pdata/internal/data/protogen/common/v1"
	otlpresource "go.opentelemetry.io/collector/pdata/internal/data/protogen/resource/v1"
)

// InstrumentationScope is a message representing the instrumentation scope information.
//
// This is a reference type, if passed by value and callee modifies it the
// caller will see the modification.
//
// Must use NewInstrumentationScope function to create new instances.
// Important: zero-initialized instance is not valid for use.
type InstrumentationScope internal.InstrumentationScope

func newInstrumentationScope(orig *otlpcommon.InstrumentationScope, state *internal.State) InstrumentationScope {
	return InstrumentationScope(internal.NewInstrumentationScope(orig, state))
}

// NewInstrumentationScope creates a new empty InstrumentationScope.
//
// This must be used only in testing code. Users should use "AppendEmpty" when part of a Slice,
// OR directly access the member if this is embedded in another struct.
func NewInstrumentationScope() InstrumentationScope {
	state := internal.StateMutable
	return newInstrumentationScope(&otlpcommon.InstrumentationScope{}, &state)
}

// MoveTo moves all properties from the current struct overriding the destination and
// resetting the current instance to its zero value
func (ms InstrumentationScope) MoveTo(dest InstrumentationScope) {
	ms.getState().AssertMutable()
	dest.getState().AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if ms.getOrig() == dest.getOrig() {
		return
	}
	*dest.getOrig() = *ms.getOrig()
	*ms.getOrig() = otlpcommon.InstrumentationScope{}
}

func (ms InstrumentationScope) getOrig() *otlpcommon.InstrumentationScope {
	return internal.GetOrigInstrumentationScope(internal.InstrumentationScope(ms))
}

func (ms InstrumentationScope) getState() *internal.State {
	return internal.GetInstrumentationScopeState(internal.InstrumentationScope(ms))
}

// Name returns the name associated with this InstrumentationScope.
func (ms InstrumentationScope) Name() string {
	return ms.getOrig().Name
}

// SetName replaces the name associated with this InstrumentationScope.
func (ms InstrumentationScope) SetName(v string) {
	ms.getState().AssertMutable()
	ms.getOrig().Name = v
}

// Version returns the version associated with this InstrumentationScope.
func (ms InstrumentationScope) Version() string {
	return ms.getOrig().Version
}

// SetVersion replaces the version associated with this InstrumentationScope.
func (ms InstrumentationScope) SetVersion(v string) {
	ms.getState().AssertMutable()
	ms.getOrig().Version = v
}

// Attributes returns the Attributes associated with this InstrumentationScope.
func (ms InstrumentationScope) Attributes() Map {
	return Map(internal.NewMap(&ms.getOrig().Attributes, internal.GetInstrumentationScopeState(internal.InstrumentationScope(ms))))
}

// DroppedAttributesCount returns the droppedattributescount associated with this InstrumentationScope.
func (ms InstrumentationScope) DroppedAttributesCount() uint32 {
	return ms.getOrig().DroppedAttributesCount
}

// SetDroppedAttributesCount replaces the droppedattributescount associated with this InstrumentationScope.
func (ms InstrumentationScope) SetDroppedAttributesCount(v uint32) {
	ms.getState().AssertMutable()
	ms.getOrig().DroppedAttributesCount = v
}

// CopyTo copies all properties from the current struct overriding the destination.
func (ms InstrumentationScope) CopyTo(dest InstrumentationScope) {
	dest.getState().AssertMutable()
	internal.CopyOrigInstrumentationScope(dest.getOrig(), ms.getOrig())
}

// Equal checks equality with another InstrumentationScope.
// Optionally accepts CompareOption arguments to customize comparison behavior.
func (ms InstrumentationScope) Equal(val InstrumentationScope, opts ...CompareOption) bool {
	cfg := NewCompareConfig(opts)
	return (cfg.ShouldIgnoreField("Name") || ms.Name() == val.Name()) &&
		(cfg.ShouldIgnoreField("Version") || ms.Version() == val.Version()) &&
		(cfg.ShouldIgnoreField("Attributes") || ms.Attributes().Equal(val.Attributes(), opts...)) &&
		(cfg.ShouldIgnoreField("DroppedAttributesCount") || ms.DroppedAttributesCount() == val.DroppedAttributesCount())
}
