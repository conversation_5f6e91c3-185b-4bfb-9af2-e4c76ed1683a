// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pcommon

import (
	"iter"
	"slices"

	"go.opentelemetry.io/collector/pdata/internal"
	otlpcommon "go.opentelemetry.io/collector/pdata/internal/data/protogen/common/v1"
	otlpresource "go.opentelemetry.io/collector/pdata/internal/data/protogen/resource/v1"
)

// Resource is a message representing the resource information.
//
// This is a reference type, if passed by value and callee modifies it the
// caller will see the modification.
//
// Must use NewResource function to create new instances.
// Important: zero-initialized instance is not valid for use.
type Resource internal.Resource

func newResource(orig *otlpresource.Resource, state *internal.State) Resource {
	return Resource(internal.NewResource(orig, state))
}

// NewResource creates a new empty Resource.
//
// This must be used only in testing code. Users should use "AppendEmpty" when part of a Slice,
// OR directly access the member if this is embedded in another struct.
func NewResource() Resource {
	state := internal.StateMutable
	return newResource(&otlpresource.Resource{}, &state)
}

// MoveTo moves all properties from the current struct overriding the destination and
// resetting the current instance to its zero value
func (ms Resource) MoveTo(dest Resource) {
	ms.getState().AssertMutable()
	dest.getState().AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if ms.getOrig() == dest.getOrig() {
		return
	}
	*dest.getOrig() = *ms.getOrig()
	*ms.getOrig() = otlpresource.Resource{}
}

func (ms Resource) getOrig() *otlpresource.Resource {
	return internal.GetOrigResource(internal.Resource(ms))
}

func (ms Resource) getState() *internal.State {
	return internal.GetResourceState(internal.Resource(ms))
}

// Attributes returns the Attributes associated with this Resource.
func (ms Resource) Attributes() Map {
	return Map(internal.NewMap(&ms.getOrig().Attributes, internal.GetResourceState(internal.Resource(ms))))
}

// DroppedAttributesCount returns the droppedattributescount associated with this Resource.
func (ms Resource) DroppedAttributesCount() uint32 {
	return ms.getOrig().DroppedAttributesCount
}

// SetDroppedAttributesCount replaces the droppedattributescount associated with this Resource.
func (ms Resource) SetDroppedAttributesCount(v uint32) {
	ms.getState().AssertMutable()
	ms.getOrig().DroppedAttributesCount = v
}

// CopyTo copies all properties from the current struct overriding the destination.
func (ms Resource) CopyTo(dest Resource) {
	dest.getState().AssertMutable()
	internal.CopyOrigResource(dest.getOrig(), ms.getOrig())
}

// Equal checks equality with another Resource.
// Optionally accepts CompareOption arguments to customize comparison behavior.
func (ms Resource) Equal(val Resource, opts ...CompareOption) bool {
	cfg := NewCompareConfig(opts)
	return (cfg.ShouldIgnoreField("Attributes") || ms.Attributes().Equal(val.Attributes(), opts...)) &&
		(cfg.ShouldIgnoreField("DroppedAttributesCount") || ms.DroppedAttributesCount() == val.DroppedAttributesCount())
}
