// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pcommon

import (
"iter"
"slices"

"go.opentelemetry.io/collector/pdata/internal"

)

// ByteSlice represents a []byte slice.
// The instance of ByteSlice can be assigned to multiple objects since it's immutable.
//
// Must use NewByteSlice function to create new instances.
// Important: zero-initialized instance is not valid for use.
type ByteSlice internal.ByteSlice

func (ms ByteSlice) getOrig() *[]byte {
	return internal.GetOrigByteSlice(internal.ByteSlice(ms))
}

func (ms ByteSlice) getState() *internal.State {
	return internal.GetByteSliceState(internal.ByteSlice(ms))
}

// NewByteSlice creates a new empty ByteSlice.
func NewByteSlice() ByteSlice {
	orig := []byte(nil)
	state := internal.StateMutable
	return ByteSlice(internal.NewByteSlice(&orig, &state))
}

// AsRaw returns a copy of the []byte slice.
func (ms ByteSlice) AsRaw() []byte {
	return internal.CopyOrigByteSlice(nil, *ms.getOrig())
}

// FromRaw copies raw []byte into the slice ByteSlice.
func (ms ByteSlice) FromRaw(val []byte) {
	ms.getState().AssertMutable()
	*ms.getOrig() = internal.CopyOrigByteSlice(*ms.getOrig(), val)
}

// Len returns length of the []byte slice value.
// Equivalent of len(byteSlice).
func (ms ByteSlice) Len() int {
	return len(*ms.getOrig())
}

// At returns an item from particular index.
// Equivalent of byteSlice[i].
func (ms ByteSlice) At(i int) byte {
	return (*ms.getOrig())[i]
}

// All returns an iterator over index-value pairs in the slice.
func (ms ByteSlice) All() iter.Seq2[int, byte] {
	return func(yield func(int, byte) bool) {
		for i := 0; i < ms.Len(); i++ {
			if !yield(i, ms.At(i)) {
				return
			}
		}
	}
}

// SetAt sets byte item at particular index.
// Equivalent of byteSlice[i] = val
func (ms ByteSlice) SetAt(i int, val byte) {
	ms.getState().AssertMutable()
	(*ms.getOrig())[i] = val
}

// EnsureCapacity ensures ByteSlice has at least the specified capacity.
// 1. If the newCap <= cap, then is no change in capacity.
// 2. If the newCap > cap, then the slice capacity will be expanded to the provided value which will be equivalent of:
//	buf := make([]byte, len(byteSlice), newCap)
//	copy(buf, byteSlice)
//	byteSlice = buf
func (ms ByteSlice) EnsureCapacity(newCap int) {
	ms.getState().AssertMutable()
	oldCap := cap(*ms.getOrig())
	if newCap <= oldCap {
		return
	}

	newOrig := make([]byte, len(*ms.getOrig()), newCap)
	copy(newOrig, *ms.getOrig())
	*ms.getOrig() = newOrig
}

// Append appends extra elements to ByteSlice.
// Equivalent of byteSlice = append(byteSlice, elms...)
func (ms ByteSlice) Append(elms ...byte) {
	ms.getState().AssertMutable()
	*ms.getOrig() = append(*ms.getOrig(), elms...)
}

// MoveTo moves all elements from the current slice overriding the destination and
// resetting the current instance to its zero value.
func (ms ByteSlice) MoveTo(dest ByteSlice) {
	ms.getState().AssertMutable()
	dest.getState().AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if ms.getOrig() == dest.getOrig() {
		return
	}
	*dest.getOrig() = *ms.getOrig()
	*ms.getOrig() = nil
}

// MoveAndAppendTo moves all elements from the current slice and appends them to the dest.
// The current slice will be cleared.
func (ms ByteSlice) MoveAndAppendTo(dest ByteSlice) {
  ms.getState().AssertMutable()
  dest.getState().AssertMutable()
  if *dest.getOrig() == nil {
    // We can simply move the entire vector and avoid any allocations.
    *dest.getOrig() = *ms.getOrig()
  } else {
    *dest.getOrig() = append(*dest.getOrig(), *ms.getOrig()...)
  }
  *ms.getOrig() = nil
}

// CopyTo copies all elements from the current slice overriding the destination.
func (ms ByteSlice) CopyTo(dest ByteSlice) {
	dest.getState().AssertMutable()
	*dest.getOrig() = internal.CopyOrigByteSlice(*dest.getOrig(), *ms.getOrig())
}

// Equal checks equality with another ByteSlice.
// Optionally accepts CompareOption arguments to customize comparison behavior.
func (ms ByteSlice) Equal(val ByteSlice, opts ...CompareOption) bool {
	return slices.Equal(*ms.getOrig(), *val.getOrig())
}
