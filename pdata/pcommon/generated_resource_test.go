// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pcommon

import (
"testing"

"github.com/stretchr/testify/assert"

"go.opentelemetry.io/collector/pdata/internal"

)

func TestResource_MoveTo(t *testing.T) {
	ms := generateTestResource()
	dest := NewResource()
	ms.MoveTo(dest)
	assert.Equal(t, NewResource(), ms)
	assert.Equal(t, generateTestResource(), dest)
	dest.MoveTo(dest)
	assert.Equal(t, generateTestResource(), dest)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.MoveTo(newResource(&otlpresource.Resource{}, &sharedState)) })
	assert.Panics(t, func() { newResource(&otlpresource.Resource{}, &sharedState).MoveTo(dest) })
}

func TestResource_CopyTo(t *testing.T) {
	ms := NewResource()
	orig := NewResource()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	orig = generateTestResource()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.CopyTo(newResource(&otlpresource.Resource{}, &sharedState)) })
}

func TestResource_Equal(t *testing.T) {
	ms1 := NewResource()
	ms2 := NewResource()
	assert.True(t, ms1.Equal(ms2))

	ms1 = generateTestResource()
	ms2 = generateTestResource()
	assert.True(t, ms1.Equal(ms2))

	ms2 = NewResource()
	assert.False(t, ms1.Equal(ms2))
	
	// Test with CompareOption
	ms1 = generateTestResource()
	ms2 = generateTestResource()
	assert.True(t, ms1.Equal(ms2, FloatTolerance(0.1)))
}


func TestResource_Attributes(t *testing.T) {
	ms := NewResource()
	assert.Equal(t, NewMap(), ms.Attributes())
	internal.FillTestMap(internal.Map(ms.Attributes()))
	assert.Equal(t, Map(internal.GenerateTestMap()), ms.Attributes())
}

func TestResource_DroppedAttributesCount(t *testing.T) {
	ms := NewResource()
	assert.Equal(t, uint32(0), ms.DroppedAttributesCount())
	ms.SetDroppedAttributesCount(uint32(17))
	assert.Equal(t, uint32(17), ms.DroppedAttributesCount())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newResource(&otlpresource.Resource{}, &sharedState).SetDroppedAttributesCount(uint32(17)) })
}


func generateTestResource() Resource {
    return Resource(internal.GenerateTestResource())
}


