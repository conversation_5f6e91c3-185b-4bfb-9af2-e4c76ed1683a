// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pcommon

import (
"testing"

"github.com/stretchr/testify/assert"

"go.opentelemetry.io/collector/pdata/internal"

)

func TestNewByteSlice(t *testing.T) {
	ms := NewByteSlice()
	assert.Equal(t, 0, ms.Len())
	ms.FromRaw([]byte{ 1, 2, 3 })
	assert.Equal(t, 3, ms.Len())
	assert.Equal(t, []byte{ 1, 2, 3 }, ms.AsRaw())
	ms.SetAt(1, byte( 5 ))
	assert.Equal(t, []byte{ 1, 5, 3 }, ms.AsRaw())
	ms.FromRaw([]byte{ 3 })
	assert.Equal(t, 1, ms.Len())
	assert.Equal(t, byte(3), ms.At(0))

	cp := NewByteSlice()
	ms.CopyTo(cp)
	ms.SetAt(0, byte( 2 ))
	assert.Equal(t, byte(2), ms.At(0))
	assert.Equal(t, byte(3), cp.At(0))
	ms.CopyTo(cp)
	assert.Equal(t, byte(2), cp.At(0))

	mv := NewByteSlice()
	ms.MoveTo(mv)
	assert.Equal(t, 0, ms.Len())
	assert.Equal(t, 1, mv.Len())
	assert.Equal(t, byte(2), mv.At(0))
	ms.FromRaw([]byte{ 1, 2, 3 })
	ms.MoveTo(mv)
	assert.Equal(t, 3, mv.Len())
	assert.Equal(t, byte(1), mv.At(0))
	mv.MoveTo(mv)
	assert.Equal(t, 3, mv.Len())
	assert.Equal(t, byte(1), mv.At(0))
}

func TestByteSliceReadOnly(t *testing.T) {
	raw := []byte{ 1, 2, 3}
	state := internal.StateReadOnly
	ms := ByteSlice(internal.NewByteSlice(&raw, &state))

	assert.Equal(t, 3, ms.Len())
	assert.Equal(t, byte(1), ms.At(0))
	assert.Panics(t, func() { ms.Append(1) })
	assert.Panics(t, func() { ms.EnsureCapacity(2) })
	assert.Equal(t, raw, ms.AsRaw())
	assert.Panics(t, func() { ms.FromRaw(raw) })

	ms2 := NewByteSlice()
	ms.CopyTo(ms2)
	assert.Equal(t, ms.AsRaw(), ms2.AsRaw())
	assert.Panics(t, func() { ms2.CopyTo(ms) })

	assert.Panics(t, func() { ms.MoveTo(ms2) })
	assert.Panics(t, func() { ms2.MoveTo(ms) })
}

func TestByteSliceAppend(t *testing.T) {
	ms := NewByteSlice()
	ms.FromRaw([]byte{ 1, 2, 3 })
	ms.Append(5, 5)
	assert.Equal(t, 5, ms.Len())
	assert.Equal(t, byte(5), ms.At(4))
}

func TestByteSliceEnsureCapacity(t *testing.T) {
	ms := NewByteSlice()
	ms.EnsureCapacity(4)
	assert.Equal(t, 4, cap(*ms.getOrig()))
	ms.EnsureCapacity(2)
	assert.Equal(t, 4, cap(*ms.getOrig()))
}

func TestByteSliceAll(t *testing.T) {
	ms := NewByteSlice()
	ms.FromRaw([]byte{ 1, 2, 3 })
	assert.NotEmpty(t, ms.Len())

	var c int
	for i, v := range ms.All() {
		assert.Equal(t, ms.At(i), v, "element should match")
		c++
	}
	assert.Equal(t, ms.Len(), c, "All elements should have been visited")
}


func TestByteSliceMoveAndAppendTo(t *testing.T) {
  // Test moving from an empty slice
  ms := NewByteSlice()
  ms2 := NewByteSlice()
  ms.MoveAndAppendTo(ms2)
  assert.Equal(t, NewByteSlice(), ms2)
  assert.Equal(t, ms.Len(), 0)

  // Test moving to empty slice
  ms.FromRaw([]byte{ 1, 2, 3 })
  ms.MoveAndAppendTo(ms2)
  assert.Equal(t, ms2.Len(), 3)

  // Test moving to a non empty slice
  ms.FromRaw([]byte{ 1, 2, 3 })
  ms.MoveAndAppendTo(ms2)
  assert.Equal(t, ms2.Len(), 6)
}

func TestByteSliceEqual(t *testing.T) {
	ms := NewByteSlice()
	ms2 := NewByteSlice()
	assert.True(t, ms.Equal(ms2))

	ms.Append(1, 2, 3)
	assert.False(t, ms.Equal(ms2))

	ms2.Append(1, 2, 3)
	assert.True(t, ms.Equal(ms2))
	
	// Test with CompareOption
	ms = NewByteSlice()
	ms2 = NewByteSlice()
	ms.Append(1, 2, 3)
	ms2.Append(1, 2, 3)
	assert.True(t, ms.Equal(ms2, FloatTolerance(0.1)))
}

func BenchmarkByteSliceEqual(b *testing.B) {
	ms := NewByteSlice()
	ms.Append(1, 2, 3)
	cmp := NewByteSlice()
	cmp.Append(1, 2, 3)

	b.ResetTimer()
	b.ReportAllocs()

	for n := 0; n < b.N; n++ {
		_ = ms.Equal(cmp)
	}
}
