// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pcommon

import (
"testing"

"github.com/stretchr/testify/assert"

"go.opentelemetry.io/collector/pdata/internal"

)

func TestNewStringSlice(t *testing.T) {
	ms := NewStringSlice()
	assert.Equal(t, 0, ms.Len())
	ms.FromRaw([]string{ "a", "b", "c" })
	assert.Equal(t, 3, ms.Len())
	assert.Equal(t, []string{ "a", "b", "c" }, ms.AsRaw())
	ms.SetAt(1, string( "d" ))
	assert.Equal(t, []string{ "a", "d", "c" }, ms.AsRaw())
	ms.FromRaw([]string{ "c" })
	assert.Equal(t, 1, ms.Len())
	assert.Equal(t, string("c"), ms.At(0))

	cp := NewStringSlice()
	ms.CopyTo(cp)
	ms.SetAt(0, string( "b" ))
	assert.Equal(t, string("b"), ms.At(0))
	assert.Equal(t, string("c"), cp.At(0))
	ms.CopyTo(cp)
	assert.Equal(t, string("b"), cp.At(0))

	mv := NewStringSlice()
	ms.MoveTo(mv)
	assert.Equal(t, 0, ms.Len())
	assert.Equal(t, 1, mv.Len())
	assert.Equal(t, string("b"), mv.At(0))
	ms.FromRaw([]string{ "a", "b", "c" })
	ms.MoveTo(mv)
	assert.Equal(t, 3, mv.Len())
	assert.Equal(t, string("a"), mv.At(0))
	mv.MoveTo(mv)
	assert.Equal(t, 3, mv.Len())
	assert.Equal(t, string("a"), mv.At(0))
}

func TestStringSliceReadOnly(t *testing.T) {
	raw := []string{ "a", "b", "c"}
	state := internal.StateReadOnly
	ms := StringSlice(internal.NewStringSlice(&raw, &state))

	assert.Equal(t, 3, ms.Len())
	assert.Equal(t, string("a"), ms.At(0))
	assert.Panics(t, func() { ms.Append("a") })
	assert.Panics(t, func() { ms.EnsureCapacity(2) })
	assert.Equal(t, raw, ms.AsRaw())
	assert.Panics(t, func() { ms.FromRaw(raw) })

	ms2 := NewStringSlice()
	ms.CopyTo(ms2)
	assert.Equal(t, ms.AsRaw(), ms2.AsRaw())
	assert.Panics(t, func() { ms2.CopyTo(ms) })

	assert.Panics(t, func() { ms.MoveTo(ms2) })
	assert.Panics(t, func() { ms2.MoveTo(ms) })
}

func TestStringSliceAppend(t *testing.T) {
	ms := NewStringSlice()
	ms.FromRaw([]string{ "a", "b", "c" })
	ms.Append("d", "d")
	assert.Equal(t, 5, ms.Len())
	assert.Equal(t, string("d"), ms.At(4))
}

func TestStringSliceEnsureCapacity(t *testing.T) {
	ms := NewStringSlice()
	ms.EnsureCapacity(4)
	assert.Equal(t, 4, cap(*ms.getOrig()))
	ms.EnsureCapacity(2)
	assert.Equal(t, 4, cap(*ms.getOrig()))
}

func TestStringSliceAll(t *testing.T) {
	ms := NewStringSlice()
	ms.FromRaw([]string{ "a", "b", "c" })
	assert.NotEmpty(t, ms.Len())

	var c int
	for i, v := range ms.All() {
		assert.Equal(t, ms.At(i), v, "element should match")
		c++
	}
	assert.Equal(t, ms.Len(), c, "All elements should have been visited")
}


func TestStringSliceMoveAndAppendTo(t *testing.T) {
  // Test moving from an empty slice
  ms := NewStringSlice()
  ms2 := NewStringSlice()
  ms.MoveAndAppendTo(ms2)
  assert.Equal(t, NewStringSlice(), ms2)
  assert.Equal(t, ms.Len(), 0)

  // Test moving to empty slice
  ms.FromRaw([]string{ "a", "b", "c" })
  ms.MoveAndAppendTo(ms2)
  assert.Equal(t, ms2.Len(), 3)

  // Test moving to a non empty slice
  ms.FromRaw([]string{ "a", "b", "c" })
  ms.MoveAndAppendTo(ms2)
  assert.Equal(t, ms2.Len(), 6)
}

func TestStringSliceEqual(t *testing.T) {
	ms := NewStringSlice()
	ms2 := NewStringSlice()
	assert.True(t, ms.Equal(ms2))

	ms.Append("a", "b", "c")
	assert.False(t, ms.Equal(ms2))

	ms2.Append("a", "b", "c")
	assert.True(t, ms.Equal(ms2))
	
	// Test with CompareOption
	ms = NewStringSlice()
	ms2 = NewStringSlice()
	ms.Append("a", "b", "c")
	ms2.Append("a", "b", "c")
	assert.True(t, ms.Equal(ms2, FloatTolerance(0.1)))
}

func BenchmarkStringSliceEqual(b *testing.B) {
	ms := NewStringSlice()
	ms.Append("a", "b", "c")
	cmp := NewStringSlice()
	cmp.Append("a", "b", "c")

	b.ResetTimer()
	b.ReportAllocs()

	for n := 0; n < b.N; n++ {
		_ = ms.Equal(cmp)
	}
}
