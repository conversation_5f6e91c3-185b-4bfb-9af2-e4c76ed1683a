// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pcommon

import (
"iter"
"slices"

"go.opentelemetry.io/collector/pdata/internal"

)

// Int32Slice represents a []int32 slice.
// The instance of Int32Slice can be assigned to multiple objects since it's immutable.
//
// Must use NewInt32Slice function to create new instances.
// Important: zero-initialized instance is not valid for use.
type Int32Slice internal.Int32Slice

func (ms Int32Slice) getOrig() *[]int32 {
	return internal.GetOrigInt32Slice(internal.Int32Slice(ms))
}

func (ms Int32Slice) getState() *internal.State {
	return internal.GetInt32SliceState(internal.Int32Slice(ms))
}

// NewInt32Slice creates a new empty Int32Slice.
func NewInt32Slice() Int32Slice {
	orig := []int32(nil)
	state := internal.StateMutable
	return Int32Slice(internal.NewInt32Slice(&orig, &state))
}

// AsRaw returns a copy of the []int32 slice.
func (ms Int32Slice) AsRaw() []int32 {
	return internal.CopyOrigInt32Slice(nil, *ms.getOrig())
}

// FromRaw copies raw []int32 into the slice Int32Slice.
func (ms Int32Slice) FromRaw(val []int32) {
	ms.getState().AssertMutable()
	*ms.getOrig() = internal.CopyOrigInt32Slice(*ms.getOrig(), val)
}

// Len returns length of the []int32 slice value.
// Equivalent of len(int32Slice).
func (ms Int32Slice) Len() int {
	return len(*ms.getOrig())
}

// At returns an item from particular index.
// Equivalent of int32Slice[i].
func (ms Int32Slice) At(i int) int32 {
	return (*ms.getOrig())[i]
}

// All returns an iterator over index-value pairs in the slice.
func (ms Int32Slice) All() iter.Seq2[int, int32] {
	return func(yield func(int, int32) bool) {
		for i := 0; i < ms.Len(); i++ {
			if !yield(i, ms.At(i)) {
				return
			}
		}
	}
}

// SetAt sets int32 item at particular index.
// Equivalent of int32Slice[i] = val
func (ms Int32Slice) SetAt(i int, val int32) {
	ms.getState().AssertMutable()
	(*ms.getOrig())[i] = val
}

// EnsureCapacity ensures Int32Slice has at least the specified capacity.
// 1. If the newCap <= cap, then is no change in capacity.
// 2. If the newCap > cap, then the slice capacity will be expanded to the provided value which will be equivalent of:
//	buf := make([]int32, len(int32Slice), newCap)
//	copy(buf, int32Slice)
//	int32Slice = buf
func (ms Int32Slice) EnsureCapacity(newCap int) {
	ms.getState().AssertMutable()
	oldCap := cap(*ms.getOrig())
	if newCap <= oldCap {
		return
	}

	newOrig := make([]int32, len(*ms.getOrig()), newCap)
	copy(newOrig, *ms.getOrig())
	*ms.getOrig() = newOrig
}

// Append appends extra elements to Int32Slice.
// Equivalent of int32Slice = append(int32Slice, elms...)
func (ms Int32Slice) Append(elms ...int32) {
	ms.getState().AssertMutable()
	*ms.getOrig() = append(*ms.getOrig(), elms...)
}

// MoveTo moves all elements from the current slice overriding the destination and
// resetting the current instance to its zero value.
func (ms Int32Slice) MoveTo(dest Int32Slice) {
	ms.getState().AssertMutable()
	dest.getState().AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if ms.getOrig() == dest.getOrig() {
		return
	}
	*dest.getOrig() = *ms.getOrig()
	*ms.getOrig() = nil
}

// MoveAndAppendTo moves all elements from the current slice and appends them to the dest.
// The current slice will be cleared.
func (ms Int32Slice) MoveAndAppendTo(dest Int32Slice) {
  ms.getState().AssertMutable()
  dest.getState().AssertMutable()
  if *dest.getOrig() == nil {
    // We can simply move the entire vector and avoid any allocations.
    *dest.getOrig() = *ms.getOrig()
  } else {
    *dest.getOrig() = append(*dest.getOrig(), *ms.getOrig()...)
  }
  *ms.getOrig() = nil
}

// CopyTo copies all elements from the current slice overriding the destination.
func (ms Int32Slice) CopyTo(dest Int32Slice) {
	dest.getState().AssertMutable()
	*dest.getOrig() = internal.CopyOrigInt32Slice(*dest.getOrig(), *ms.getOrig())
}

// Equal checks equality with another Int32Slice.
// Optionally accepts CompareOption arguments to customize comparison behavior.
func (ms Int32Slice) Equal(val Int32Slice, opts ...CompareOption) bool {
	return slices.Equal(*ms.getOrig(), *val.getOrig())
}
