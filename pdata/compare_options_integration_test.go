package pdata

import (
	"testing"

	"go.opentelemetry.io/collector/pdata/pcommon"
	"go.opentelemetry.io/collector/pdata/plog"
	"go.opentelemetry.io/collector/pdata/pmetric"
	"go.opentelemetry.io/collector/pdata/ptrace"
)

// TestCompareOptionIntegration verifies that CompareOption functionality
// works correctly across all pdata packages that should support it.
func TestCompareOptionIntegration(t *testing.T) {
	t.Run("pcommon_compare_options", func(t *testing.T) {
		// Test pcommon.Value with float tolerance
		val1 := pcommon.NewValueDouble(1.0000000001)
		val2 := pcommon.NewValueDouble(1.0000000002)

		// Should fail with default comparison
		if val1.Equal(val2) {
			t.Error("Expected values to be different with default comparison")
		}

		// Should pass with tolerance
		if !val1.Equal(val2, pcommon.FloatTolerance(1e-8)) {
			t.Error("Expected values to be equal with tolerance")
		}
	})

	t.Run("pmetric_compare_options", func(t *testing.T) {
		// Test pmetric histogram with float tolerance
		h1 := pmetric.NewHistogramDataPoint()
		h1.SetSum(1345.32)

		h2 := pmetric.NewHistogramDataPoint()
		h2.SetSum(1345.33) // Larger difference that exceeds default tolerance

		// Should fail with default comparison
		if h1.Equal(h2) {
			t.Error("Expected histogram data points to be different with default comparison")
		}

		// Should pass with larger tolerance
		if !h1.Equal(h2, pmetric.FloatTolerance(0.1)) {
			t.Error("Expected histogram data points to be equal with tolerance")
		}
	})

	t.Run("plog_compare_options", func(t *testing.T) {
		// Test plog.LogRecord with field ignoring
		log1 := plog.NewLogRecord()
		log1.SetSeverityText("INFO")
		log1.SetTimestamp(1234567890)

		log2 := plog.NewLogRecord()
		log2.SetSeverityText("DEBUG") // Different severity
		log2.SetTimestamp(1234567890)

		// Should fail with default comparison
		if log1.Equal(log2) {
			t.Error("Expected log records to be different with default comparison")
		}

		// Should pass when ignoring SeverityText field
		if !log1.Equal(log2, plog.IgnoreFields("SeverityText")) {
			t.Error("Expected log records to be equal when ignoring SeverityText")
		}
	})

	t.Run("ptrace_compare_options", func(t *testing.T) {
		// Test ptrace.Span with field ignoring
		span1 := ptrace.NewSpan()
		span1.SetName("test-span")
		span1.SetFlags(1)

		span2 := ptrace.NewSpan()
		span2.SetName("test-span")
		span2.SetFlags(2) // Different flags

		// Should fail with default comparison
		if span1.Equal(span2) {
			t.Error("Expected spans to be different with default comparison")
		}

		// Should pass when ignoring Flags field
		if !span1.Equal(span2, ptrace.IgnoreFields("Flags")) {
			t.Error("Expected spans to be equal when ignoring Flags")
		}
	})
}
