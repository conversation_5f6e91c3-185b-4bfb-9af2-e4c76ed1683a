// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pmetric

import (
"testing"
"unsafe"

"github.com/stretchr/testify/assert"

"go.opentelemetry.io/collector/pdata/internal"
"go.opentelemetry.io/collector/pdata/internal/data"
otlpmetrics "go.opentelemetry.io/collector/pdata/internal/data/protogen/metrics/v1"
"go.opentelemetry.io/collector/pdata/pcommon"

)

func TestGauge_MoveTo(t *testing.T) {
	ms := generateTestGauge()
	dest := NewGauge()
	ms.MoveTo(dest)
	assert.Equal(t, NewGauge(), ms)
	assert.Equal(t, generateTestGauge(), dest)
	dest.MoveTo(dest)
	assert.Equal(t, generateTestGauge(), dest)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.MoveTo(newGauge(&otlpmetrics.Gauge{}, &sharedState)) })
	assert.Panics(t, func() { newGauge(&otlpmetrics.Gauge{}, &sharedState).MoveTo(dest) })
}

func TestGauge_CopyTo(t *testing.T) {
	ms := NewGauge()
	orig := NewGauge()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	orig = generateTestGauge()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.CopyTo(newGauge(&otlpmetrics.Gauge{}, &sharedState)) })
}

func TestGauge_Equal(t *testing.T) {
	ms1 := NewGauge()
	ms2 := NewGauge()
	assert.True(t, ms1.Equal(ms2))

	ms1 = generateTestGauge()
	ms2 = generateTestGauge()
	assert.True(t, ms1.Equal(ms2))

	ms2 = NewGauge()
	assert.False(t, ms1.Equal(ms2))
}


func TestGauge_DataPoints(t *testing.T) {
	ms := NewGauge()
	assert.Equal(t, .NewNumberDataPointSlice(), ms.DataPoints())
	fillTestNumberDataPointSlice(ms.DataPoints())
	assert.Equal(t, generateTestNumberDataPointSlice(), ms.DataPoints())
}


func generateTestGauge() Gauge {
	tv := NewGauge()
	fillTestGauge(tv)
	return tv
}

func fillTestGauge(tv Gauge) {
	fillTestNumberDataPointSlice(newNumberDataPointSlice(&tv.orig.DataPoints, tv.state))
}

