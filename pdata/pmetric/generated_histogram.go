// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pmetric

import (
	"go.opentelemetry.io/collector/pdata/internal"
	otlpmetrics "go.opentelemetry.io/collector/pdata/internal/data/protogen/metrics/v1"
)

// Histogram represents the type of a metric that is calculated by aggregating as a Histogram of all reported measurements over a time interval.
//
// This is a reference type, if passed by value and callee modifies it the
// caller will see the modification.
//
// Must use NewHistogram function to create new instances.
// Important: zero-initialized instance is not valid for use.
type Histogram struct {
	orig  *otlpmetrics.Histogram
	state *internal.State
}

func newHistogram(orig *otlpmetrics.Histogram, state *internal.State) Histogram {
	return Histogram{orig: orig, state: state}
}

// NewHistogram creates a new empty Histogram.
//
// This must be used only in testing code. Users should use "AppendEmpty" when part of a Slice,
// OR directly access the member if this is embedded in another struct.
func NewHistogram() Histogram {
	state := internal.StateMutable
	return newHistogram(&otlpmetrics.Histogram{}, &state)
}

// MoveTo moves all properties from the current struct overriding the destination and
// resetting the current instance to its zero value
func (ms Histogram) MoveTo(dest Histogram) {
	ms.state.AssertMutable()
	dest.state.AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if ms.orig == dest.orig {
		return
	}
	*dest.orig = *ms.orig
	*ms.orig = otlpmetrics.Histogram{}
}

// AggregationTemporality returns the aggregationtemporality associated with this Histogram.
func (ms Histogram) AggregationTemporality() AggregationTemporality {
	return AggregationTemporality(ms.orig.AggregationTemporality)
}

// SetAggregationTemporality replaces the aggregationtemporality associated with this Histogram.
func (ms Histogram) SetAggregationTemporality(v AggregationTemporality) {
	ms.state.AssertMutable()
	ms.orig.AggregationTemporality = otlpmetrics.AggregationTemporality(v)
}

// DataPoints returns the DataPoints associated with this Histogram.
func (ms Histogram) DataPoints() HistogramDataPointSlice {
	return newHistogramDataPointSlice(&ms.orig.DataPoints, ms.state)
}

// CopyTo copies all properties from the current struct overriding the destination.
func (ms Histogram) CopyTo(dest Histogram) {
	dest.state.AssertMutable()
	copyOrigHistogram(dest.orig, ms.orig)
}

func copyOrigHistogram(dest, src *otlpmetrics.Histogram) {
	dest.AggregationTemporality = src.AggregationTemporality
	dest.DataPoints = copyOrigHistogramDataPointSlice(dest.DataPoints, src.DataPoints)
}

// Equal checks equality with another Histogram.
// Optionally accepts CompareOption arguments to customize comparison behavior.
func (ms Histogram) Equal(val Histogram, opts ...CompareOption) bool {
	cfg := NewCompareConfig(opts)
	_ = cfg // may not be used in all cases
	return (cfg.ShouldIgnoreField("AggregationTemporality") || ms.AggregationTemporality() == val.AggregationTemporality()) &&
		(cfg.ShouldIgnoreField("DataPoints") || ms.DataPoints().Equal(val.DataPoints(), opts...))
}
