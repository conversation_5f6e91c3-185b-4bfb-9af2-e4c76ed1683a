// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pmetric

import (
	"go.opentelemetry.io/collector/pdata/internal"
	otlpmetrics "go.opentelemetry.io/collector/pdata/internal/data/protogen/metrics/v1"
	"go.opentelemetry.io/collector/pdata/pcommon"
)

// ExponentialHistogramDataPoint is a single data point in a timeseries that describes the
// time-varying values of a ExponentialHistogram of double values. A ExponentialHistogram contains
// summary statistics for a population of values, it may optionally contain the
// distribution of those values across a set of buckets.
//
// This is a reference type, if passed by value and callee modifies it the
// caller will see the modification.
//
// Must use NewExponentialHistogramDataPoint function to create new instances.
// Important: zero-initialized instance is not valid for use.
type ExponentialHistogramDataPoint struct {
	orig  *otlpmetrics.ExponentialHistogramDataPoint
	state *internal.State
}

func newExponentialHistogramDataPoint(orig *otlpmetrics.ExponentialHistogramDataPoint, state *internal.State) ExponentialHistogramDataPoint {
	return ExponentialHistogramDataPoint{orig: orig, state: state}
}

// NewExponentialHistogramDataPoint creates a new empty ExponentialHistogramDataPoint.
//
// This must be used only in testing code. Users should use "AppendEmpty" when part of a Slice,
// OR directly access the member if this is embedded in another struct.
func NewExponentialHistogramDataPoint() ExponentialHistogramDataPoint {
	state := internal.StateMutable
	return newExponentialHistogramDataPoint(&otlpmetrics.ExponentialHistogramDataPoint{}, &state)
}

// MoveTo moves all properties from the current struct overriding the destination and
// resetting the current instance to its zero value
func (ms ExponentialHistogramDataPoint) MoveTo(dest ExponentialHistogramDataPoint) {
	ms.state.AssertMutable()
	dest.state.AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if ms.orig == dest.orig {
		return
	}
	*dest.orig = *ms.orig
	*ms.orig = otlpmetrics.ExponentialHistogramDataPoint{}
}

// Attributes returns the Attributes associated with this ExponentialHistogramDataPoint.
func (ms ExponentialHistogramDataPoint) Attributes() pcommon.Map {
	return pcommon.Map(internal.NewMap(&ms.orig.Attributes, ms.state))
}

// StartTimestamp returns the starttimestamp associated with this ExponentialHistogramDataPoint.
func (ms ExponentialHistogramDataPoint) StartTimestamp() pcommon.Timestamp {
	return pcommon.Timestamp(ms.orig.StartTimeUnixNano)
}

// SetStartTimestamp replaces the starttimestamp associated with this ExponentialHistogramDataPoint.
func (ms ExponentialHistogramDataPoint) SetStartTimestamp(v pcommon.Timestamp) {
	ms.state.AssertMutable()
	ms.orig.StartTimeUnixNano = uint64(v)
}

// Timestamp returns the timestamp associated with this ExponentialHistogramDataPoint.
func (ms ExponentialHistogramDataPoint) Timestamp() pcommon.Timestamp {
	return pcommon.Timestamp(ms.orig.TimeUnixNano)
}

// SetTimestamp replaces the timestamp associated with this ExponentialHistogramDataPoint.
func (ms ExponentialHistogramDataPoint) SetTimestamp(v pcommon.Timestamp) {
	ms.state.AssertMutable()
	ms.orig.TimeUnixNano = uint64(v)
}

// Count returns the count associated with this ExponentialHistogramDataPoint.
func (ms ExponentialHistogramDataPoint) Count() uint64 {
	return ms.orig.Count
}

// SetCount replaces the count associated with this ExponentialHistogramDataPoint.
func (ms ExponentialHistogramDataPoint) SetCount(v uint64) {
	ms.state.AssertMutable()
	ms.orig.Count = v
}

// Scale returns the scale associated with this ExponentialHistogramDataPoint.
func (ms ExponentialHistogramDataPoint) Scale() int32 {
	return ms.orig.Scale
}

// SetScale replaces the scale associated with this ExponentialHistogramDataPoint.
func (ms ExponentialHistogramDataPoint) SetScale(v int32) {
	ms.state.AssertMutable()
	ms.orig.Scale = v
}

// ZeroCount returns the zerocount associated with this ExponentialHistogramDataPoint.
func (ms ExponentialHistogramDataPoint) ZeroCount() uint64 {
	return ms.orig.ZeroCount
}

// SetZeroCount replaces the zerocount associated with this ExponentialHistogramDataPoint.
func (ms ExponentialHistogramDataPoint) SetZeroCount(v uint64) {
	ms.state.AssertMutable()
	ms.orig.ZeroCount = v
}

// Positive returns the positive associated with this ExponentialHistogramDataPoint.
func (ms ExponentialHistogramDataPoint) Positive() ExponentialHistogramDataPointBuckets {
	return newExponentialHistogramDataPointBuckets(&ms.orig.Positive, ms.state)
}

// Negative returns the negative associated with this ExponentialHistogramDataPoint.
func (ms ExponentialHistogramDataPoint) Negative() ExponentialHistogramDataPointBuckets {
	return newExponentialHistogramDataPointBuckets(&ms.orig.Negative, ms.state)
}

// Exemplars returns the Exemplars associated with this ExponentialHistogramDataPoint.
func (ms ExponentialHistogramDataPoint) Exemplars() ExemplarSlice {
	return newExemplarSlice(&ms.orig.Exemplars, ms.state)
}

// Flags returns the flags associated with this ExponentialHistogramDataPoint.
func (ms ExponentialHistogramDataPoint) Flags() DataPointFlags {
	return DataPointFlags(ms.orig.Flags)
}

// SetFlags replaces the flags associated with this ExponentialHistogramDataPoint.
func (ms ExponentialHistogramDataPoint) SetFlags(v DataPointFlags) {
	ms.state.AssertMutable()
	ms.orig.Flags = uint32(v)
}

// Sum returns the sum associated with this ExponentialHistogramDataPoint.
func (ms ExponentialHistogramDataPoint) Sum() float64 {
	return ms.orig.GetSum()
}

// HasSum returns true if the ExponentialHistogramDataPoint contains a
// Sum value, false otherwise.
func (ms ExponentialHistogramDataPoint) HasSum() bool {
	return ms.orig.Sum_ != nil
}

// SetSum replaces the sum associated with this ExponentialHistogramDataPoint.
func (ms ExponentialHistogramDataPoint) SetSum(v float64) {
	ms.state.AssertMutable()
	ms.orig.Sum_ = &otlpmetrics.ExponentialHistogramDataPoint_Sum{Sum: v}
}

// RemoveSum removes the sum associated with this ExponentialHistogramDataPoint.
func (ms ExponentialHistogramDataPoint) RemoveSum() {
	ms.state.AssertMutable()
	ms.orig.Sum_ = nil
}

// Min returns the min associated with this ExponentialHistogramDataPoint.
func (ms ExponentialHistogramDataPoint) Min() float64 {
	return ms.orig.GetMin()
}

// HasMin returns true if the ExponentialHistogramDataPoint contains a
// Min value, false otherwise.
func (ms ExponentialHistogramDataPoint) HasMin() bool {
	return ms.orig.Min_ != nil
}

// SetMin replaces the min associated with this ExponentialHistogramDataPoint.
func (ms ExponentialHistogramDataPoint) SetMin(v float64) {
	ms.state.AssertMutable()
	ms.orig.Min_ = &otlpmetrics.ExponentialHistogramDataPoint_Min{Min: v}
}

// RemoveMin removes the min associated with this ExponentialHistogramDataPoint.
func (ms ExponentialHistogramDataPoint) RemoveMin() {
	ms.state.AssertMutable()
	ms.orig.Min_ = nil
}

// Max returns the max associated with this ExponentialHistogramDataPoint.
func (ms ExponentialHistogramDataPoint) Max() float64 {
	return ms.orig.GetMax()
}

// HasMax returns true if the ExponentialHistogramDataPoint contains a
// Max value, false otherwise.
func (ms ExponentialHistogramDataPoint) HasMax() bool {
	return ms.orig.Max_ != nil
}

// SetMax replaces the max associated with this ExponentialHistogramDataPoint.
func (ms ExponentialHistogramDataPoint) SetMax(v float64) {
	ms.state.AssertMutable()
	ms.orig.Max_ = &otlpmetrics.ExponentialHistogramDataPoint_Max{Max: v}
}

// RemoveMax removes the max associated with this ExponentialHistogramDataPoint.
func (ms ExponentialHistogramDataPoint) RemoveMax() {
	ms.state.AssertMutable()
	ms.orig.Max_ = nil
}

// ZeroThreshold returns the zerothreshold associated with this ExponentialHistogramDataPoint.
func (ms ExponentialHistogramDataPoint) ZeroThreshold() float64 {
	return ms.orig.ZeroThreshold
}

// SetZeroThreshold replaces the zerothreshold associated with this ExponentialHistogramDataPoint.
func (ms ExponentialHistogramDataPoint) SetZeroThreshold(v float64) {
	ms.state.AssertMutable()
	ms.orig.ZeroThreshold = v
}

// CopyTo copies all properties from the current struct overriding the destination.
func (ms ExponentialHistogramDataPoint) CopyTo(dest ExponentialHistogramDataPoint) {
	dest.state.AssertMutable()
	copyOrigExponentialHistogramDataPoint(dest.orig, ms.orig)
}

func copyOrigExponentialHistogramDataPoint(dest, src *otlpmetrics.ExponentialHistogramDataPoint) {
	dest.Attributes = internal.CopyOrigMap(dest.Attributes, src.Attributes)
	dest.StartTimeUnixNano = src.StartTimeUnixNano
	dest.TimeUnixNano = src.TimeUnixNano
	dest.Count = src.Count
	dest.Scale = src.Scale
	dest.ZeroCount = src.ZeroCount
	copyOrigExponentialHistogramDataPointBuckets(&dest.Positive, &src.Positive)
	copyOrigExponentialHistogramDataPointBuckets(&dest.Negative, &src.Negative)
	dest.Exemplars = copyOrigExemplarSlice(dest.Exemplars, src.Exemplars)
	dest.Flags = src.Flags
	if src.Sum_ == nil {
		dest.Sum_ = nil
	} else {
		dest.Sum_ = &otlpmetrics.ExponentialHistogramDataPoint_Sum{Sum: src.GetSum()}
	}
	if src.Min_ == nil {
		dest.Min_ = nil
	} else {
		dest.Min_ = &otlpmetrics.ExponentialHistogramDataPoint_Min{Min: src.GetMin()}
	}
	if src.Max_ == nil {
		dest.Max_ = nil
	} else {
		dest.Max_ = &otlpmetrics.ExponentialHistogramDataPoint_Max{Max: src.GetMax()}
	}
	dest.ZeroThreshold = src.ZeroThreshold
}

// Equal checks equality with another ExponentialHistogramDataPoint.
// Optionally accepts CompareOption arguments to customize comparison behavior.
func (ms ExponentialHistogramDataPoint) Equal(val ExponentialHistogramDataPoint, opts ...CompareOption) bool {
	cfg := NewCompareConfig(opts)
	_ = cfg // may not be used in all cases
	return ms.Attributes().Equal(val.Attributes()) &&
		ms.StartTimestamp() == val.StartTimestamp() &&
		ms.Timestamp() == val.Timestamp() &&
		ms.Count() == val.Count() &&
		ms.Scale() == val.Scale() &&
		ms.ZeroCount() == val.ZeroCount() &&
		ms.Positive().Equal(val.Positive()) &&
		ms.Negative().Equal(val.Negative()) &&
		ms.Exemplars().Equal(val.Exemplars()) &&
		ms.Flags() == val.Flags() &&
		(ms.HasSum() == val.HasSum() && (!ms.HasSum() || ms.Sum() == val.Sum())) &&
		(ms.HasMin() == val.HasMin() && (!ms.HasMin() || ms.Min() == val.Min())) &&
		(ms.HasMax() == val.HasMax() && (!ms.HasMax() || ms.Max() == val.Max())) &&
		ms.ZeroThreshold() == val.ZeroThreshold()
}
