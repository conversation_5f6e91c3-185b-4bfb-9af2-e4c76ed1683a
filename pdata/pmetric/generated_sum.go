// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pmetric

import (
"iter"
"math"
"slices"
"sort"
"strings"

"go.opentelemetry.io/collector/pdata/internal"
"go.opentelemetry.io/collector/pdata/internal/data"
otlpmetrics "go.opentelemetry.io/collector/pdata/internal/data/protogen/metrics/v1"
"go.opentelemetry.io/collector/pdata/pcommon"

)

// Sum represents the type of a numeric metric that is calculated as a sum of all reported measurements over a time interval.
//
// This is a reference type, if passed by value and callee modifies it the
// caller will see the modification.
//
// Must use NewSum function to create new instances.
// Important: zero-initialized instance is not valid for use.
type Sum struct {
	orig *otlpmetrics.Sum
	state *internal.State
}

func newSum(orig *otlpmetrics.Sum, state *internal.State) Sum {
	return Sum{orig: orig, state: state}
}

// NewSum creates a new empty Sum.
//
// This must be used only in testing code. Users should use "AppendEmpty" when part of a Slice,
// OR directly access the member if this is embedded in another struct.
func NewSum() Sum {
	state := internal.StateMutable
	return newSum(&otlpmetrics.Sum{}, &state)
}

// MoveTo moves all properties from the current struct overriding the destination and
// resetting the current instance to its zero value
func (ms Sum) MoveTo(dest Sum) {
	ms.state.AssertMutable()
	dest.state.AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if ms.orig == dest.orig {
		return
	}
	*dest.orig = *ms.orig
	*ms.orig = otlpmetrics.Sum{}
}



// AggregationTemporality returns the aggregationtemporality associated with this Sum.
func (ms Sum) AggregationTemporality() AggregationTemporality {
	return AggregationTemporality(ms.orig.AggregationTemporality)
}

// SetAggregationTemporality replaces the aggregationtemporality associated with this Sum.
func (ms Sum) SetAggregationTemporality(v AggregationTemporality) {
	ms.state.AssertMutable()
	ms.orig.AggregationTemporality = otlpmetrics.AggregationTemporality(v)
}
// IsMonotonic returns the ismonotonic associated with this Sum.
func (ms Sum) IsMonotonic() bool {
	return ms.orig.IsMonotonic
}

// SetIsMonotonic replaces the ismonotonic associated with this Sum.
func (ms Sum) SetIsMonotonic(v bool) {
	ms.state.AssertMutable()
	ms.orig.IsMonotonic = v
}
// DataPoints returns the DataPoints associated with this Sum.
func (ms Sum) DataPoints() NumberDataPointSlice {
	return newNumberDataPointSlice(&ms.orig.DataPoints, ms.state)
}


// CopyTo copies all properties from the current struct overriding the destination.
func (ms Sum) CopyTo(dest Sum) {
	dest.state.AssertMutable()
    copyOrigSum(dest.orig, ms.orig)
}

func copyOrigSum(dest, src *otlpmetrics.Sum) {
	dest.AggregationTemporality = src.AggregationTemporality
	dest.IsMonotonic = src.IsMonotonic
	dest.DataPoints =copyOrigNumberDataPointSlice(dest.DataPoints, src.DataPoints)
}

// Equal checks equality with another Sum.
// Optionally accepts CompareOption arguments to customize comparison behavior.
func (ms Sum) Equal(val Sum, opts ...CompareOption) bool {
	cfg := NewCompareConfig(opts)
	_ = cfg // may not be used in all cases
	return ms.AggregationTemporality() == val.AggregationTemporality() &&
		ms.IsMonotonic() == val.IsMonotonic() &&
		ms.DataPoints().Equal(val.DataPoints())
}
