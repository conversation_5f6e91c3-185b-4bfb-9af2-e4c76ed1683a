// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pmetric

import (
	"go.opentelemetry.io/collector/pdata/internal"
	otlpmetrics "go.opentelemetry.io/collector/pdata/internal/data/protogen/metrics/v1"
	"go.opentelemetry.io/collector/pdata/pcommon"
)

// NumberDataPoint is a single data point in a timeseries that describes the time-varying value of a number metric.
//
// This is a reference type, if passed by value and callee modifies it the
// caller will see the modification.
//
// Must use NewNumberDataPoint function to create new instances.
// Important: zero-initialized instance is not valid for use.
type NumberDataPoint struct {
	orig  *otlpmetrics.NumberDataPoint
	state *internal.State
}

func newNumberDataPoint(orig *otlpmetrics.NumberDataPoint, state *internal.State) NumberDataPoint {
	return NumberDataPoint{orig: orig, state: state}
}

// NewNumberDataPoint creates a new empty NumberDataPoint.
//
// This must be used only in testing code. Users should use "AppendEmpty" when part of a Slice,
// OR directly access the member if this is embedded in another struct.
func NewNumberDataPoint() NumberDataPoint {
	state := internal.StateMutable
	return newNumberDataPoint(&otlpmetrics.NumberDataPoint{}, &state)
}

// MoveTo moves all properties from the current struct overriding the destination and
// resetting the current instance to its zero value
func (ms NumberDataPoint) MoveTo(dest NumberDataPoint) {
	ms.state.AssertMutable()
	dest.state.AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if ms.orig == dest.orig {
		return
	}
	*dest.orig = *ms.orig
	*ms.orig = otlpmetrics.NumberDataPoint{}
}

// Attributes returns the Attributes associated with this NumberDataPoint.
func (ms NumberDataPoint) Attributes() pcommon.Map {
	return pcommon.Map(internal.NewMap(&ms.orig.Attributes, ms.state))
}

// StartTimestamp returns the starttimestamp associated with this NumberDataPoint.
func (ms NumberDataPoint) StartTimestamp() pcommon.Timestamp {
	return pcommon.Timestamp(ms.orig.StartTimeUnixNano)
}

// SetStartTimestamp replaces the starttimestamp associated with this NumberDataPoint.
func (ms NumberDataPoint) SetStartTimestamp(v pcommon.Timestamp) {
	ms.state.AssertMutable()
	ms.orig.StartTimeUnixNano = uint64(v)
}

// Timestamp returns the timestamp associated with this NumberDataPoint.
func (ms NumberDataPoint) Timestamp() pcommon.Timestamp {
	return pcommon.Timestamp(ms.orig.TimeUnixNano)
}

// SetTimestamp replaces the timestamp associated with this NumberDataPoint.
func (ms NumberDataPoint) SetTimestamp(v pcommon.Timestamp) {
	ms.state.AssertMutable()
	ms.orig.TimeUnixNano = uint64(v)
}

// ValueType returns the type of the value for this NumberDataPoint.
// Calling this function on zero-initialized NumberDataPoint will cause a panic.
func (ms NumberDataPoint) ValueType() NumberDataPointValueType {
	switch ms.orig.Value.(type) {
	case *otlpmetrics.NumberDataPoint_AsDouble:
		return NumberDataPointValueTypeDouble
	case *otlpmetrics.NumberDataPoint_AsInt:
		return NumberDataPointValueTypeInt
	}
	return NumberDataPointValueTypeEmpty
}

// DoubleValue returns the double associated with this NumberDataPoint.
func (ms NumberDataPoint) DoubleValue() float64 {
	return ms.orig.GetAsDouble()
}

// SetDoubleValue replaces the double associated with this NumberDataPoint.
func (ms NumberDataPoint) SetDoubleValue(v float64) {
	ms.state.AssertMutable()
	ms.orig.Value = &otlpmetrics.NumberDataPoint_AsDouble{
		AsDouble: v,
	}
}

// IntValue returns the int associated with this NumberDataPoint.
func (ms NumberDataPoint) IntValue() int64 {
	return ms.orig.GetAsInt()
}

// SetIntValue replaces the int associated with this NumberDataPoint.
func (ms NumberDataPoint) SetIntValue(v int64) {
	ms.state.AssertMutable()
	ms.orig.Value = &otlpmetrics.NumberDataPoint_AsInt{
		AsInt: v,
	}
}

// Exemplars returns the Exemplars associated with this NumberDataPoint.
func (ms NumberDataPoint) Exemplars() ExemplarSlice {
	return newExemplarSlice(&ms.orig.Exemplars, ms.state)
}

// Flags returns the flags associated with this NumberDataPoint.
func (ms NumberDataPoint) Flags() DataPointFlags {
	return DataPointFlags(ms.orig.Flags)
}

// SetFlags replaces the flags associated with this NumberDataPoint.
func (ms NumberDataPoint) SetFlags(v DataPointFlags) {
	ms.state.AssertMutable()
	ms.orig.Flags = uint32(v)
}

// CopyTo copies all properties from the current struct overriding the destination.
func (ms NumberDataPoint) CopyTo(dest NumberDataPoint) {
	dest.state.AssertMutable()
	copyOrigNumberDataPoint(dest.orig, ms.orig)
}

func copyOrigNumberDataPoint(dest, src *otlpmetrics.NumberDataPoint) {
	dest.Attributes = internal.CopyOrigMap(dest.Attributes, src.Attributes)
	dest.StartTimeUnixNano = src.StartTimeUnixNano
	dest.TimeUnixNano = src.TimeUnixNano
	switch t := src.Value.(type) {
	case *otlpmetrics.NumberDataPoint_AsDouble:
		dest.Value = &otlpmetrics.NumberDataPoint_AsDouble{AsDouble: t.AsDouble}
	case *otlpmetrics.NumberDataPoint_AsInt:
		dest.Value = &otlpmetrics.NumberDataPoint_AsInt{AsInt: t.AsInt}
	}
	dest.Exemplars = copyOrigExemplarSlice(dest.Exemplars, src.Exemplars)
	dest.Flags = src.Flags
}

// Equal checks equality with another NumberDataPoint.
// Optionally accepts CompareOption arguments to customize comparison behavior.
func (ms NumberDataPoint) Equal(val NumberDataPoint, opts ...CompareOption) bool {
	cfg := NewCompareConfig(opts)
	_ = cfg // may not be used in all cases
	return ms.Attributes().Equal(val.Attributes()) &&
		ms.StartTimestamp() == val.StartTimestamp() &&
		ms.Timestamp() == val.Timestamp() &&
		(ms.ValueType() == val.ValueType() && ((ms.ValueType() == NumberDataPointValueTypeDouble && val.ValueType() == NumberDataPointValueTypeDouble && ms.DoubleValue() == val.DoubleValue()) || (ms.ValueType() == NumberDataPointValueTypeInt && val.ValueType() == NumberDataPointValueTypeInt && ms.IntValue() == val.IntValue()) || ms.ValueType() == NumberDataPointValueTypeEmpty)) &&
		ms.Exemplars().Equal(val.Exemplars()) &&
		ms.Flags() == val.Flags()
}
