// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pmetric

import (
	"go.opentelemetry.io/collector/pdata/internal"
	otlpmetrics "go.opentelemetry.io/collector/pdata/internal/data/protogen/metrics/v1"
	"go.opentelemetry.io/collector/pdata/pcommon"
)

// Metric represents one metric as a collection of datapoints.
// See Metric definition in OTLP: https://github.com/open-telemetry/opentelemetry-proto/blob/main/opentelemetry/proto/metrics/v1/metrics.proto
//
// This is a reference type, if passed by value and callee modifies it the
// caller will see the modification.
//
// Must use NewMetric function to create new instances.
// Important: zero-initialized instance is not valid for use.
type Metric struct {
	orig  *otlpmetrics.Metric
	state *internal.State
}

func newMetric(orig *otlpmetrics.Metric, state *internal.State) Metric {
	return Metric{orig: orig, state: state}
}

// NewMetric creates a new empty Metric.
//
// This must be used only in testing code. Users should use "AppendEmpty" when part of a Slice,
// OR directly access the member if this is embedded in another struct.
func NewMetric() Metric {
	state := internal.StateMutable
	return newMetric(&otlpmetrics.Metric{}, &state)
}

// MoveTo moves all properties from the current struct overriding the destination and
// resetting the current instance to its zero value
func (ms Metric) MoveTo(dest Metric) {
	ms.state.AssertMutable()
	dest.state.AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if ms.orig == dest.orig {
		return
	}
	*dest.orig = *ms.orig
	*ms.orig = otlpmetrics.Metric{}
}

// Name returns the name associated with this Metric.
func (ms Metric) Name() string {
	return ms.orig.Name
}

// SetName replaces the name associated with this Metric.
func (ms Metric) SetName(v string) {
	ms.state.AssertMutable()
	ms.orig.Name = v
}

// Description returns the description associated with this Metric.
func (ms Metric) Description() string {
	return ms.orig.Description
}

// SetDescription replaces the description associated with this Metric.
func (ms Metric) SetDescription(v string) {
	ms.state.AssertMutable()
	ms.orig.Description = v
}

// Unit returns the unit associated with this Metric.
func (ms Metric) Unit() string {
	return ms.orig.Unit
}

// SetUnit replaces the unit associated with this Metric.
func (ms Metric) SetUnit(v string) {
	ms.state.AssertMutable()
	ms.orig.Unit = v
}

// Metadata returns the Metadata associated with this Metric.
func (ms Metric) Metadata() pcommon.Map {
	return pcommon.Map(internal.NewMap(&ms.orig.Metadata, ms.state))
}

// Type returns the type of the data for this Metric.
// Calling this function on zero-initialized Metric will cause a panic.
func (ms Metric) Type() MetricType {
	switch ms.orig.Data.(type) {
	case *otlpmetrics.Metric_Gauge:
		return MetricTypeGauge
	case *otlpmetrics.Metric_Sum:
		return MetricTypeSum
	case *otlpmetrics.Metric_Histogram:
		return MetricTypeHistogram
	case *otlpmetrics.Metric_ExponentialHistogram:
		return MetricTypeExponentialHistogram
	case *otlpmetrics.Metric_Summary:
		return MetricTypeSummary
	}
	return MetricTypeEmpty
}

// Gauge returns the gauge associated with this Metric.
//
// Calling this function when Type() != MetricTypeGauge returns an invalid
// zero-initialized instance of Gauge. Note that using such Gauge instance can cause panic.
//
// Calling this function on zero-initialized Metric will cause a panic.
func (ms Metric) Gauge() Gauge {
	v, ok := ms.orig.GetData().(*otlpmetrics.Metric_Gauge)
	if !ok {
		return Gauge{}
	}
	return newGauge(v.Gauge, ms.state)
}

// SetEmptyGauge sets an empty gauge to this Metric.
//
// After this, Type() function will return MetricTypeGauge".
//
// Calling this function on zero-initialized Metric will cause a panic.
func (ms Metric) SetEmptyGauge() Gauge {
	ms.state.AssertMutable()
	val := &otlpmetrics.Gauge{}
	ms.orig.Data = &otlpmetrics.Metric_Gauge{Gauge: val}
	return newGauge(val, ms.state)
}

// Sum returns the sum associated with this Metric.
//
// Calling this function when Type() != MetricTypeSum returns an invalid
// zero-initialized instance of Sum. Note that using such Sum instance can cause panic.
//
// Calling this function on zero-initialized Metric will cause a panic.
func (ms Metric) Sum() Sum {
	v, ok := ms.orig.GetData().(*otlpmetrics.Metric_Sum)
	if !ok {
		return Sum{}
	}
	return newSum(v.Sum, ms.state)
}

// SetEmptySum sets an empty sum to this Metric.
//
// After this, Type() function will return MetricTypeSum".
//
// Calling this function on zero-initialized Metric will cause a panic.
func (ms Metric) SetEmptySum() Sum {
	ms.state.AssertMutable()
	val := &otlpmetrics.Sum{}
	ms.orig.Data = &otlpmetrics.Metric_Sum{Sum: val}
	return newSum(val, ms.state)
}

// Histogram returns the histogram associated with this Metric.
//
// Calling this function when Type() != MetricTypeHistogram returns an invalid
// zero-initialized instance of Histogram. Note that using such Histogram instance can cause panic.
//
// Calling this function on zero-initialized Metric will cause a panic.
func (ms Metric) Histogram() Histogram {
	v, ok := ms.orig.GetData().(*otlpmetrics.Metric_Histogram)
	if !ok {
		return Histogram{}
	}
	return newHistogram(v.Histogram, ms.state)
}

// SetEmptyHistogram sets an empty histogram to this Metric.
//
// After this, Type() function will return MetricTypeHistogram".
//
// Calling this function on zero-initialized Metric will cause a panic.
func (ms Metric) SetEmptyHistogram() Histogram {
	ms.state.AssertMutable()
	val := &otlpmetrics.Histogram{}
	ms.orig.Data = &otlpmetrics.Metric_Histogram{Histogram: val}
	return newHistogram(val, ms.state)
}

// ExponentialHistogram returns the exponentialhistogram associated with this Metric.
//
// Calling this function when Type() != MetricTypeExponentialHistogram returns an invalid
// zero-initialized instance of ExponentialHistogram. Note that using such ExponentialHistogram instance can cause panic.
//
// Calling this function on zero-initialized Metric will cause a panic.
func (ms Metric) ExponentialHistogram() ExponentialHistogram {
	v, ok := ms.orig.GetData().(*otlpmetrics.Metric_ExponentialHistogram)
	if !ok {
		return ExponentialHistogram{}
	}
	return newExponentialHistogram(v.ExponentialHistogram, ms.state)
}

// SetEmptyExponentialHistogram sets an empty exponentialhistogram to this Metric.
//
// After this, Type() function will return MetricTypeExponentialHistogram".
//
// Calling this function on zero-initialized Metric will cause a panic.
func (ms Metric) SetEmptyExponentialHistogram() ExponentialHistogram {
	ms.state.AssertMutable()
	val := &otlpmetrics.ExponentialHistogram{}
	ms.orig.Data = &otlpmetrics.Metric_ExponentialHistogram{ExponentialHistogram: val}
	return newExponentialHistogram(val, ms.state)
}

// Summary returns the summary associated with this Metric.
//
// Calling this function when Type() != MetricTypeSummary returns an invalid
// zero-initialized instance of Summary. Note that using such Summary instance can cause panic.
//
// Calling this function on zero-initialized Metric will cause a panic.
func (ms Metric) Summary() Summary {
	v, ok := ms.orig.GetData().(*otlpmetrics.Metric_Summary)
	if !ok {
		return Summary{}
	}
	return newSummary(v.Summary, ms.state)
}

// SetEmptySummary sets an empty summary to this Metric.
//
// After this, Type() function will return MetricTypeSummary".
//
// Calling this function on zero-initialized Metric will cause a panic.
func (ms Metric) SetEmptySummary() Summary {
	ms.state.AssertMutable()
	val := &otlpmetrics.Summary{}
	ms.orig.Data = &otlpmetrics.Metric_Summary{Summary: val}
	return newSummary(val, ms.state)
}

// CopyTo copies all properties from the current struct overriding the destination.
func (ms Metric) CopyTo(dest Metric) {
	dest.state.AssertMutable()
	copyOrigMetric(dest.orig, ms.orig)
}

func copyOrigMetric(dest, src *otlpmetrics.Metric) {
	dest.Name = src.Name
	dest.Description = src.Description
	dest.Unit = src.Unit
	dest.Metadata = internal.CopyOrigMap(dest.Metadata, src.Metadata)
	switch t := src.Data.(type) {
	case *otlpmetrics.Metric_Gauge:
		gauge := &otlpmetrics.Gauge{}
		copyOrigGauge(gauge, t.Gauge)
		dest.Data = &otlpmetrics.Metric_Gauge{
			Gauge: gauge,
		}
	case *otlpmetrics.Metric_Sum:
		sum := &otlpmetrics.Sum{}
		copyOrigSum(sum, t.Sum)
		dest.Data = &otlpmetrics.Metric_Sum{
			Sum: sum,
		}
	case *otlpmetrics.Metric_Histogram:
		histogram := &otlpmetrics.Histogram{}
		copyOrigHistogram(histogram, t.Histogram)
		dest.Data = &otlpmetrics.Metric_Histogram{
			Histogram: histogram,
		}
	case *otlpmetrics.Metric_ExponentialHistogram:
		exponentialhistogram := &otlpmetrics.ExponentialHistogram{}
		copyOrigExponentialHistogram(exponentialhistogram, t.ExponentialHistogram)
		dest.Data = &otlpmetrics.Metric_ExponentialHistogram{
			ExponentialHistogram: exponentialhistogram,
		}
	case *otlpmetrics.Metric_Summary:
		summary := &otlpmetrics.Summary{}
		copyOrigSummary(summary, t.Summary)
		dest.Data = &otlpmetrics.Metric_Summary{
			Summary: summary,
		}
	}
}

// Equal checks equality with another Metric.
// Optionally accepts CompareOption arguments to customize comparison behavior.
func (ms Metric) Equal(val Metric, opts ...CompareOption) bool {
	cfg := NewCompareConfig(opts)
	return ms.Name() == val.Name() &&
		ms.Description() == val.Description() &&
		ms.Unit() == val.Unit() &&
		ms.Metadata().Equal(val.Metadata()) &&
		(ms.Type() == val.Type() && ((ms.Type() == MetricTypeGauge && val.Type() == MetricTypeGauge && ms.Gauge().Equal(val.Gauge())) || (ms.Type() == MetricTypeSum && val.Type() == MetricTypeSum && ms.Sum().Equal(val.Sum())) || (ms.Type() == MetricTypeHistogram && val.Type() == MetricTypeHistogram && ms.Histogram().Equal(val.Histogram())) || (ms.Type() == MetricTypeExponentialHistogram && val.Type() == MetricTypeExponentialHistogram && ms.ExponentialHistogram().Equal(val.ExponentialHistogram())) || (ms.Type() == MetricTypeSummary && val.Type() == MetricTypeSummary && ms.Summary().Equal(val.Summary())) || ms.Type() == MetricTypeEmpty))
}
