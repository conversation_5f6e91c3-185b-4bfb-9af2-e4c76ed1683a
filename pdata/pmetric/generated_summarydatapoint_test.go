// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pmetric

import (
"testing"
"unsafe"

"github.com/stretchr/testify/assert"

"go.opentelemetry.io/collector/pdata/internal"
"go.opentelemetry.io/collector/pdata/internal/data"
otlpmetrics "go.opentelemetry.io/collector/pdata/internal/data/protogen/metrics/v1"
"go.opentelemetry.io/collector/pdata/pcommon"

)

func TestSummaryDataPoint_MoveTo(t *testing.T) {
	ms := generateTestSummaryDataPoint()
	dest := NewSummaryDataPoint()
	ms.MoveTo(dest)
	assert.Equal(t, NewSummaryDataPoint(), ms)
	assert.Equal(t, generateTestSummaryDataPoint(), dest)
	dest.MoveTo(dest)
	assert.Equal(t, generateTestSummaryDataPoint(), dest)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.MoveTo(newSummaryDataPoint(&otlpmetrics.SummaryDataPoint{}, &sharedState)) })
	assert.Panics(t, func() { newSummaryDataPoint(&otlpmetrics.SummaryDataPoint{}, &sharedState).MoveTo(dest) })
}

func TestSummaryDataPoint_CopyTo(t *testing.T) {
	ms := NewSummaryDataPoint()
	orig := NewSummaryDataPoint()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	orig = generateTestSummaryDataPoint()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.CopyTo(newSummaryDataPoint(&otlpmetrics.SummaryDataPoint{}, &sharedState)) })
}

func TestSummaryDataPoint_Equal(t *testing.T) {
	ms1 := NewSummaryDataPoint()
	ms2 := NewSummaryDataPoint()
	assert.True(t, ms1.Equal(ms2))

	ms1 = generateTestSummaryDataPoint()
	ms2 = generateTestSummaryDataPoint()
	assert.True(t, ms1.Equal(ms2))

	ms2 = NewSummaryDataPoint()
	assert.False(t, ms1.Equal(ms2))
}


func TestSummaryDataPoint_Attributes(t *testing.T) {
	ms := NewSummaryDataPoint()
	assert.Equal(t, pcommon.NewMap(), ms.Attributes())
	internal.FillTestMap(internal.Map(ms.Attributes()))
	assert.Equal(t, pcommon.Map(internal.GenerateTestMap()), ms.Attributes())
}

func TestSummaryDataPoint_StartTimestamp(t *testing.T) {
	ms := NewSummaryDataPoint()
	assert.Equal(t, pcommon.Timestamp(0), ms.StartTimestamp())
	testValStartTimestamp := pcommon.Timestamp(1234567890)
	ms.SetStartTimestamp(testValStartTimestamp)
	assert.Equal(t, testValStartTimestamp, ms.StartTimestamp())
}

func TestSummaryDataPoint_Timestamp(t *testing.T) {
	ms := NewSummaryDataPoint()
	assert.Equal(t, pcommon.Timestamp(0), ms.Timestamp())
	testValTimestamp := pcommon.Timestamp(1234567890)
	ms.SetTimestamp(testValTimestamp)
	assert.Equal(t, testValTimestamp, ms.Timestamp())
}

func TestSummaryDataPoint_Count(t *testing.T) {
	ms := NewSummaryDataPoint()
	assert.Equal(t, uint64(0), ms.Count())
	ms.SetCount(uint64(17))
	assert.Equal(t, uint64(17), ms.Count())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newSummaryDataPoint(&otlpmetrics.SummaryDataPoint{}, &sharedState).SetCount(uint64(17)) })
}

func TestSummaryDataPoint_Sum(t *testing.T) {
	ms := NewSummaryDataPoint()
	assert.InDelta(t, float64(0.0), ms.Sum(), 0.01)
	ms.SetSum(float64(17.13))
	assert.InDelta(t, float64(17.13), ms.Sum(), 0.01)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newSummaryDataPoint(&otlpmetrics.SummaryDataPoint{}, &sharedState).SetSum(float64(17.13)) })
}

func TestSummaryDataPoint_QuantileValues(t *testing.T) {
	ms := NewSummaryDataPoint()
	assert.Equal(t, NewSummaryDataPointValueAtQuantileSlice(), ms.QuantileValues())
	fillTestSummaryDataPointValueAtQuantileSlice(ms.QuantileValues())
	assert.Equal(t, generateTestSummaryDataPointValueAtQuantileSlice(), ms.QuantileValues())
}

func TestSummaryDataPoint_Flags(t *testing.T) {
	ms := NewSummaryDataPoint()
	assert.Equal(t, DataPointFlags(0), ms.Flags())
	testValFlags := DataPointFlags(1)
	ms.SetFlags(testValFlags)
	assert.Equal(t, testValFlags, ms.Flags())
}


func generateTestSummaryDataPoint() SummaryDataPoint {
	tv := NewSummaryDataPoint()
	fillTestSummaryDataPoint(tv)
	return tv
}

func fillTestSummaryDataPoint(tv SummaryDataPoint) {
	internal.FillTestMap(internal.NewMap(&tv.orig.Attributes, tv.state))
	tv.orig.StartTimeUnixNano = 1234567890
	tv.orig.TimeUnixNano = 1234567890
	tv.orig.Count = uint64(17)
	tv.orig.Sum = float64(17.13)
	fillTestSummaryDataPointValueAtQuantileSlice(newSummaryDataPointValueAtQuantileSlice(&tv.orig.QuantileValues, tv.state))
	tv.orig.Flags = 1
}

