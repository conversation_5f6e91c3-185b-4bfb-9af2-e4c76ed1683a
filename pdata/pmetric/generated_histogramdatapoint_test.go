// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pmetric

import (
"testing"
"unsafe"

"github.com/stretchr/testify/assert"

"go.opentelemetry.io/collector/pdata/internal"
"go.opentelemetry.io/collector/pdata/internal/data"
otlpmetrics "go.opentelemetry.io/collector/pdata/internal/data/protogen/metrics/v1"
"go.opentelemetry.io/collector/pdata/pcommon"

)

func TestHistogramDataPoint_MoveTo(t *testing.T) {
	ms := generateTestHistogramDataPoint()
	dest := NewHistogramDataPoint()
	ms.MoveTo(dest)
	assert.Equal(t, NewHistogramDataPoint(), ms)
	assert.Equal(t, generateTestHistogramDataPoint(), dest)
	dest.MoveTo(dest)
	assert.Equal(t, generateTestHistogramDataPoint(), dest)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.MoveTo(newHistogramDataPoint(&otlpmetrics.HistogramDataPoint{}, &sharedState)) })
	assert.Panics(t, func() { newHistogramDataPoint(&otlpmetrics.HistogramDataPoint{}, &sharedState).MoveTo(dest) })
}

func TestHistogramDataPoint_CopyTo(t *testing.T) {
	ms := NewHistogramDataPoint()
	orig := NewHistogramDataPoint()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	orig = generateTestHistogramDataPoint()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.CopyTo(newHistogramDataPoint(&otlpmetrics.HistogramDataPoint{}, &sharedState)) })
}

func TestHistogramDataPoint_Equal(t *testing.T) {
	ms1 := NewHistogramDataPoint()
	ms2 := NewHistogramDataPoint()
	assert.True(t, ms1.Equal(ms2))

	ms1 = generateTestHistogramDataPoint()
	ms2 = generateTestHistogramDataPoint()
	assert.True(t, ms1.Equal(ms2))

	ms2 = NewHistogramDataPoint()
	assert.False(t, ms1.Equal(ms2))
}


func TestHistogramDataPoint_Attributes(t *testing.T) {
	ms := NewHistogramDataPoint()
	assert.Equal(t, pcommon.NewMap(), ms.Attributes())
	internal.FillTestMap(internal.Map(ms.Attributes()))
	assert.Equal(t, pcommon.Map(internal.GenerateTestMap()), ms.Attributes())
}

func TestHistogramDataPoint_StartTimestamp(t *testing.T) {
	ms := NewHistogramDataPoint()
	assert.Equal(t, pcommon.Timestamp(0), ms.StartTimestamp())
	testValStartTimestamp := pcommon.Timestamp(1234567890)
	ms.SetStartTimestamp(testValStartTimestamp)
	assert.Equal(t, testValStartTimestamp, ms.StartTimestamp())
}

func TestHistogramDataPoint_Timestamp(t *testing.T) {
	ms := NewHistogramDataPoint()
	assert.Equal(t, pcommon.Timestamp(0), ms.Timestamp())
	testValTimestamp := pcommon.Timestamp(1234567890)
	ms.SetTimestamp(testValTimestamp)
	assert.Equal(t, testValTimestamp, ms.Timestamp())
}

func TestHistogramDataPoint_Count(t *testing.T) {
	ms := NewHistogramDataPoint()
	assert.Equal(t, uint64(0), ms.Count())
	ms.SetCount(uint64(17))
	assert.Equal(t, uint64(17), ms.Count())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newHistogramDataPoint(&otlpmetrics.HistogramDataPoint{}, &sharedState).SetCount(uint64(17)) })
}

func TestHistogramDataPoint_BucketCounts(t *testing.T) {
	ms := NewHistogramDataPoint()
	assert.Equal(t, []uint64(nil), ms.BucketCounts().AsRaw())
	ms.BucketCounts().FromRaw([]uint64{1, 2, 3})
	assert.Equal(t, []uint64{1, 2, 3}, ms.BucketCounts().AsRaw())
}

func TestHistogramDataPoint_ExplicitBounds(t *testing.T) {
	ms := NewHistogramDataPoint()
	assert.Equal(t, []float64(nil), ms.ExplicitBounds().AsRaw())
	ms.ExplicitBounds().FromRaw([]float64{1, 2, 3})
	assert.Equal(t, []float64{1, 2, 3}, ms.ExplicitBounds().AsRaw())
}

func TestHistogramDataPoint_Exemplars(t *testing.T) {
	ms := NewHistogramDataPoint()
	assert.Equal(t, NewExemplarSlice(), ms.Exemplars())
	fillTestExemplarSlice(ms.Exemplars())
	assert.Equal(t, generateTestExemplarSlice(), ms.Exemplars())
}

func TestHistogramDataPoint_Flags(t *testing.T) {
	ms := NewHistogramDataPoint()
	assert.Equal(t, DataPointFlags(0), ms.Flags())
	testValFlags := DataPointFlags(1)
	ms.SetFlags(testValFlags)
	assert.Equal(t, testValFlags, ms.Flags())
}

func TestHistogramDataPoint_Sum(t *testing.T) {
	ms := NewHistogramDataPoint()
	assert.InDelta(t, float64(0.0), ms.Sum() , 0.01)
	ms.SetSum(float64(17.13))
	assert.True(t, ms.HasSum())
	assert.InDelta(t, float64(17.13), ms.Sum(), 0.01)
	ms.RemoveSum()
	assert.False(t, ms.HasSum())
	dest := NewHistogramDataPoint()
	dest.SetSum(float64(17.13))
	ms.CopyTo(dest)
	assert.False(t, dest.HasSum())
}

func TestHistogramDataPoint_Min(t *testing.T) {
	ms := NewHistogramDataPoint()
	assert.InDelta(t, float64(0.0), ms.Min() , 0.01)
	ms.SetMin(float64(9.23))
	assert.True(t, ms.HasMin())
	assert.InDelta(t, float64(9.23), ms.Min(), 0.01)
	ms.RemoveMin()
	assert.False(t, ms.HasMin())
	dest := NewHistogramDataPoint()
	dest.SetMin(float64(9.23))
	ms.CopyTo(dest)
	assert.False(t, dest.HasMin())
}

func TestHistogramDataPoint_Max(t *testing.T) {
	ms := NewHistogramDataPoint()
	assert.InDelta(t, float64(0.0), ms.Max() , 0.01)
	ms.SetMax(float64(182.55))
	assert.True(t, ms.HasMax())
	assert.InDelta(t, float64(182.55), ms.Max(), 0.01)
	ms.RemoveMax()
	assert.False(t, ms.HasMax())
	dest := NewHistogramDataPoint()
	dest.SetMax(float64(182.55))
	ms.CopyTo(dest)
	assert.False(t, dest.HasMax())
}


func generateTestHistogramDataPoint() HistogramDataPoint {
	tv := NewHistogramDataPoint()
	fillTestHistogramDataPoint(tv)
	return tv
}

func fillTestHistogramDataPoint(tv HistogramDataPoint) {
	internal.FillTestMap(internal.NewMap(&tv.orig.Attributes, tv.state))
	tv.orig.StartTimeUnixNano = 1234567890
	tv.orig.TimeUnixNano = 1234567890
	tv.orig.Count = uint64(17)
	tv.orig.BucketCounts = []uint64{1, 2, 3}
	tv.orig.ExplicitBounds = []float64{1, 2, 3}
	fillTestExemplarSlice(newExemplarSlice(&tv.orig.Exemplars, tv.state))
	tv.orig.Flags = 1
	tv.orig.Sum_ = &otlpmetrics.HistogramDataPoint_Sum{Sum: float64(17.13)}
	tv.orig.Min_ = &otlpmetrics.HistogramDataPoint_Min{Min: float64(9.23)}
	tv.orig.Max_ = &otlpmetrics.HistogramDataPoint_Max{Max: float64(182.55)}
}

