// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pmetric

import (
"testing"
"unsafe"

"github.com/stretchr/testify/assert"

"go.opentelemetry.io/collector/pdata/internal"
"go.opentelemetry.io/collector/pdata/internal/data"
otlpmetrics "go.opentelemetry.io/collector/pdata/internal/data/protogen/metrics/v1"
"go.opentelemetry.io/collector/pdata/pcommon"

)

func TestHistogram_MoveTo(t *testing.T) {
	ms := generateTestHistogram()
	dest := NewHistogram()
	ms.MoveTo(dest)
	assert.Equal(t, NewHistogram(), ms)
	assert.Equal(t, generateTestHistogram(), dest)
	dest.MoveTo(dest)
	assert.Equal(t, generateTestHistogram(), dest)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.MoveTo(newHistogram(&otlpmetrics.Histogram{}, &sharedState)) })
	assert.Panics(t, func() { newHistogram(&otlpmetrics.Histogram{}, &sharedState).MoveTo(dest) })
}

func TestHistogram_CopyTo(t *testing.T) {
	ms := NewHistogram()
	orig := NewHistogram()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	orig = generateTestHistogram()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.CopyTo(newHistogram(&otlpmetrics.Histogram{}, &sharedState)) })
}

func TestHistogram_Equal(t *testing.T) {
	ms1 := NewHistogram()
	ms2 := NewHistogram()
	assert.True(t, ms1.Equal(ms2))

	ms1 = generateTestHistogram()
	ms2 = generateTestHistogram()
	assert.True(t, ms1.Equal(ms2))

	ms2 = NewHistogram()
	assert.False(t, ms1.Equal(ms2))
}


func TestHistogram_AggregationTemporality(t *testing.T) {
	ms := NewHistogram()
	assert.Equal(t, .AggregationTemporality(otlpmetrics.AggregationTemporality(0)), ms.AggregationTemporality())
	testValAggregationTemporality := .AggregationTemporality(otlpmetrics.AggregationTemporality(1))
	ms.SetAggregationTemporality(testValAggregationTemporality)
	assert.Equal(t, testValAggregationTemporality, ms.AggregationTemporality())
}

func TestHistogram_DataPoints(t *testing.T) {
	ms := NewHistogram()
	assert.Equal(t, .NewHistogramDataPointSlice(), ms.DataPoints())
	fillTestHistogramDataPointSlice(ms.DataPoints())
	assert.Equal(t, generateTestHistogramDataPointSlice(), ms.DataPoints())
}


func generateTestHistogram() Histogram {
	tv := NewHistogram()
	fillTestHistogram(tv)
	return tv
}

func fillTestHistogram(tv Histogram) {
	tv.orig.AggregationTemporality = otlpmetrics.AggregationTemporality(1)
	fillTestHistogramDataPointSlice(newHistogramDataPointSlice(&tv.orig.DataPoints, tv.state))
}

