// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pmetric

import (
"testing"
"unsafe"

"github.com/stretchr/testify/assert"

"go.opentelemetry.io/collector/pdata/internal"
"go.opentelemetry.io/collector/pdata/internal/data"
otlpmetrics "go.opentelemetry.io/collector/pdata/internal/data/protogen/metrics/v1"
"go.opentelemetry.io/collector/pdata/pcommon"

)

func TestSummaryDataPointValueAtQuantile_MoveTo(t *testing.T) {
	ms := generateTestSummaryDataPointValueAtQuantile()
	dest := NewSummaryDataPointValueAtQuantile()
	ms.MoveTo(dest)
	assert.Equal(t, NewSummaryDataPointValueAtQuantile(), ms)
	assert.Equal(t, generateTestSummaryDataPointValueAtQuantile(), dest)
	dest.MoveTo(dest)
	assert.Equal(t, generateTestSummaryDataPointValueAtQuantile(), dest)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.MoveTo(newSummaryDataPointValueAtQuantile(&otlpmetrics.SummaryDataPoint_ValueAtQuantile{}, &sharedState)) })
	assert.Panics(t, func() { newSummaryDataPointValueAtQuantile(&otlpmetrics.SummaryDataPoint_ValueAtQuantile{}, &sharedState).MoveTo(dest) })
}

func TestSummaryDataPointValueAtQuantile_CopyTo(t *testing.T) {
	ms := NewSummaryDataPointValueAtQuantile()
	orig := NewSummaryDataPointValueAtQuantile()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	orig = generateTestSummaryDataPointValueAtQuantile()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.CopyTo(newSummaryDataPointValueAtQuantile(&otlpmetrics.SummaryDataPoint_ValueAtQuantile{}, &sharedState)) })
}

func TestSummaryDataPointValueAtQuantile_Equal(t *testing.T) {
	ms1 := NewSummaryDataPointValueAtQuantile()
	ms2 := NewSummaryDataPointValueAtQuantile()
	assert.True(t, ms1.Equal(ms2))

	ms1 = generateTestSummaryDataPointValueAtQuantile()
	ms2 = generateTestSummaryDataPointValueAtQuantile()
	assert.True(t, ms1.Equal(ms2))

	ms2 = NewSummaryDataPointValueAtQuantile()
	assert.False(t, ms1.Equal(ms2))
}


func TestSummaryDataPointValueAtQuantile_Quantile(t *testing.T) {
	ms := NewSummaryDataPointValueAtQuantile()
	assert.InDelta(t, float64(0.0), ms.Quantile(), 0.01)
	ms.SetQuantile(float64(17.13))
	assert.InDelta(t, float64(17.13), ms.Quantile(), 0.01)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newSummaryDataPointValueAtQuantile(&otlpmetrics.SummaryDataPoint_ValueAtQuantile{}, &sharedState).SetQuantile(float64(17.13)) })
}

func TestSummaryDataPointValueAtQuantile_Value(t *testing.T) {
	ms := NewSummaryDataPointValueAtQuantile()
	assert.InDelta(t, float64(0.0), ms.Value(), 0.01)
	ms.SetValue(float64(17.13))
	assert.InDelta(t, float64(17.13), ms.Value(), 0.01)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newSummaryDataPointValueAtQuantile(&otlpmetrics.SummaryDataPoint_ValueAtQuantile{}, &sharedState).SetValue(float64(17.13)) })
}


func generateTestSummaryDataPointValueAtQuantile() SummaryDataPointValueAtQuantile {
	tv := NewSummaryDataPointValueAtQuantile()
	fillTestSummaryDataPointValueAtQuantile(tv)
	return tv
}

func fillTestSummaryDataPointValueAtQuantile(tv SummaryDataPointValueAtQuantile) {
	tv.orig.Quantile = float64(17.13)
	tv.orig.Value = float64(17.13)
}

