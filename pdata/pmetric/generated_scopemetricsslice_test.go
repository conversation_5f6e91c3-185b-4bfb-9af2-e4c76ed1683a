// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pmetric

import (
	"testing"
	"unsafe"

	"github.com/stretchr/testify/assert"

	"go.opentelemetry.io/collector/pdata/internal"
	"go.opentelemetry.io/collector/pdata/internal/data"
	otlpmetrics "go.opentelemetry.io/collector/pdata/internal/data/protogen/metrics/v1"
	"go.opentelemetry.io/collector/pdata/pcommon"
)

func TestScopeMetricsSlice(t *testing.T) {
	es := NewScopeMetricsSlice()
	assert.Equal(t, 0, es.Len())
	state := internal.StateMutable
	es = newScopeMetricsSlice(&[]*otlpmetrics.ScopeMetrics{}, &state)
	assert.Equal(t, 0, es.Len())

	emptyVal := NewScopeMetrics()
	testVal := generateTestScopeMetrics()
	for i := 0; i < 7; i++ {
		el := es.AppendEmpty()
		assert.Equal(t, emptyVal, es.At(i))
		fillTestScopeMetrics(el)
		assert.Equal(t, testVal, es.At(i))
	}
	assert.Equal(t, 7, es.Len())
}

func TestScopeMetricsSliceReadOnly(t *testing.T) {
	sharedState := internal.StateReadOnly
	es := newScopeMetricsSlice(&[]*otlpmetrics.ScopeMetrics{}, &sharedState)
	assert.Equal(t, 0, es.Len())
	assert.Panics(t, func() { es.AppendEmpty() })
	assert.Panics(t, func() { es.EnsureCapacity(2) })
	es2 := NewScopeMetricsSlice()
	es.CopyTo(es2)
	assert.Panics(t, func() { es2.CopyTo(es) })
	assert.Panics(t, func() { es.MoveAndAppendTo(es2) })
	assert.Panics(t, func() { es2.MoveAndAppendTo(es) })
}

func TestScopeMetricsSlice_CopyTo(t *testing.T) {
	dest := NewScopeMetricsSlice()
	// Test CopyTo to empty
	NewScopeMetricsSlice().CopyTo(dest)
	assert.Equal(t, NewScopeMetricsSlice(), dest)

	// Test CopyTo larger slice
	generateTestScopeMetricsSlice().CopyTo(dest)
	assert.Equal(t, generateTestScopeMetricsSlice(), dest)

	// Test CopyTo same size slice
	generateTestScopeMetricsSlice().CopyTo(dest)
	assert.Equal(t, generateTestScopeMetricsSlice(), dest)
}

func TestScopeMetricsSlice_EnsureCapacity(t *testing.T) {
	es := generateTestScopeMetricsSlice()

	// Test ensure smaller capacity.
	const ensureSmallLen = 4
	es.EnsureCapacity(ensureSmallLen)
	assert.Less(t, ensureSmallLen, es.Len())
	assert.Equal(t, es.Len(), cap(*es.orig))
	assert.Equal(t, generateTestScopeMetricsSlice(), es)

	// Test ensure larger capacity
	const ensureLargeLen = 9
	es.EnsureCapacity(ensureLargeLen)
	assert.Less(t, generateTestScopeMetricsSlice().Len(), ensureLargeLen)
	assert.Equal(t, ensureLargeLen, cap(*es.orig))
	assert.Equal(t, generateTestScopeMetricsSlice(), es)
}

func TestScopeMetricsSlice_MoveAndAppendTo(t *testing.T) {
	// Test MoveAndAppendTo to empty
	expectedSlice := generateTestScopeMetricsSlice()
	dest := NewScopeMetricsSlice()
	src := generateTestScopeMetricsSlice()
	src.MoveAndAppendTo(dest)
	assert.Equal(t, generateTestScopeMetricsSlice(), dest)
	assert.Equal(t, 0, src.Len())
	assert.Equal(t, expectedSlice.Len(), dest.Len())

	// Test MoveAndAppendTo empty slice
	src.MoveAndAppendTo(dest)
	assert.Equal(t, generateTestScopeMetricsSlice(), dest)
	assert.Equal(t, 0, src.Len())
	assert.Equal(t, expectedSlice.Len(), dest.Len())

	// Test MoveAndAppendTo not empty slice
	generateTestScopeMetricsSlice().MoveAndAppendTo(dest)
	assert.Equal(t, 2*expectedSlice.Len(), dest.Len())
	for i := 0; i < expectedSlice.Len(); i++ {
		assert.Equal(t, expectedSlice.At(i), dest.At(i))
		assert.Equal(t, expectedSlice.At(i), dest.At(i+expectedSlice.Len()))
	}

	dest.MoveAndAppendTo(dest)
	assert.Equal(t, 2*expectedSlice.Len(), dest.Len())
	for i := 0; i < expectedSlice.Len(); i++ {
		assert.Equal(t, expectedSlice.At(i), dest.At(i))
		assert.Equal(t, expectedSlice.At(i), dest.At(i+expectedSlice.Len()))
	}
}

func TestScopeMetricsSlice_RemoveIf(t *testing.T) {
	// Test RemoveIf on empty slice
	emptySlice := NewScopeMetricsSlice()
	emptySlice.RemoveIf(func(el ScopeMetrics) bool {
		t.Fail()
		return false
	})

	// Test RemoveIf
	filtered := generateTestScopeMetricsSlice()
	pos := 0
	filtered.RemoveIf(func(el ScopeMetrics) bool {
		pos++
		return pos%3 == 0
	})
	assert.Equal(t, 5, filtered.Len())
}

func TestScopeMetricsSliceAll(t *testing.T) {
	ms := generateTestScopeMetricsSlice()
	assert.NotEmpty(t, ms.Len())

	var c int
	for i, v := range ms.All() {
		assert.Equal(t, ms.At(i), v, "element should match")
		c++
	}
	assert.Equal(t, ms.Len(), c, "All elements should have been visited")
}

func TestScopeMetricsSlice_Equal(t *testing.T) {
	es1 := NewScopeMetricsSlice()
	es2 := NewScopeMetricsSlice()
	assert.True(t, es1.Equal(es2))

	es1 = generateTestScopeMetricsSlice()
	es2 = generateTestScopeMetricsSlice()
	assert.True(t, es1.Equal(es2))

	es2 = NewScopeMetricsSlice()
	assert.False(t, es1.Equal(es2))

	es2.AppendEmpty()
	assert.False(t, es1.Equal(es2))

	// Test element-wise inequality - create two slices with same length but different elements
	if es1.Len() > 0 {
		es1 = generateTestScopeMetricsSlice()
		es2 = NewScopeMetricsSlice()
		// Make es2 same length as es1 but with empty elements
		for i := 0; i < es1.Len(); i++ {
			es2.AppendEmpty()
		}
		// This should return false since elements are different
		assert.False(t, es1.Equal(es2))
	}
}

func TestScopeMetricsSlice_Sort(t *testing.T) {
	es := generateTestScopeMetricsSlice()
	es.Sort(func(a, b ScopeMetrics) bool {
		return uintptr(unsafe.Pointer(a.orig)) < uintptr(unsafe.Pointer(b.orig))
	})
	for i := 1; i < es.Len(); i++ {
		assert.Less(t, uintptr(unsafe.Pointer(es.At(i-1).orig)), uintptr(unsafe.Pointer(es.At(i).orig)))
	}
	es.Sort(func(a, b ScopeMetrics) bool {
		return uintptr(unsafe.Pointer(a.orig)) > uintptr(unsafe.Pointer(b.orig))
	})
	for i := 1; i < es.Len(); i++ {
		assert.Greater(t, uintptr(unsafe.Pointer(es.At(i-1).orig)), uintptr(unsafe.Pointer(es.At(i).orig)))
	}
}

func generateTestScopeMetricsSlice() ScopeMetricsSlice {
	es := NewScopeMetricsSlice()
	fillTestScopeMetricsSlice(es)
	return es
}

func fillTestScopeMetricsSlice(es ScopeMetricsSlice) {
	*es.orig = make([]*otlpmetrics.ScopeMetrics, 7)
	for i := 0; i < 7; i++ {
		(*es.orig)[i] = &otlpmetrics.ScopeMetrics{}
		fillTestScopeMetrics(newScopeMetrics((*es.orig)[i], es.state))
	}
}
