// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pmetric

import (
"testing"
"unsafe"

"github.com/stretchr/testify/assert"

"go.opentelemetry.io/collector/pdata/internal"
"go.opentelemetry.io/collector/pdata/internal/data"
otlpmetrics "go.opentelemetry.io/collector/pdata/internal/data/protogen/metrics/v1"
"go.opentelemetry.io/collector/pdata/pcommon"

)

func TestResourceMetricsSlice(t *testing.T) {
	es := NewResourceMetricsSlice()
	assert.Equal(t, 0, es.Len())
	state := internal.StateMutable
	es = newResourceMetricsSlice(&[]*otlpmetrics.ResourceMetrics{}, &state)
	assert.Equal(t, 0, es.Len())

	emptyVal := NewResourceMetrics()
	testVal := generateTestResourceMetrics()
	for i := 0; i < 7; i++ {
		el := es.AppendEmpty()
		assert.Equal(t, emptyVal, es.At(i))
		fillTestResourceMetrics(el)
		assert.Equal(t, testVal, es.At(i))
	}
	assert.Equal(t, 7, es.Len())
}

func TestResourceMetricsSliceReadOnly(t *testing.T) {
	sharedState := internal.StateReadOnly
	es := newResourceMetricsSlice(&[]*otlpmetrics.ResourceMetrics{}, &sharedState)
	assert.Equal(t, 0, es.Len())
	assert.Panics(t, func() { es.AppendEmpty() })
	assert.Panics(t, func() { es.EnsureCapacity(2) })
	es2 := NewResourceMetricsSlice()
	es.CopyTo(es2)
	assert.Panics(t, func() { es2.CopyTo(es) })
	assert.Panics(t, func() { es.MoveAndAppendTo(es2) })
	assert.Panics(t, func() { es2.MoveAndAppendTo(es) })
}

func TestResourceMetricsSlice_CopyTo(t *testing.T) {
	dest := NewResourceMetricsSlice()
	// Test CopyTo to empty
	NewResourceMetricsSlice().CopyTo(dest)
	assert.Equal(t, NewResourceMetricsSlice(), dest)

	// Test CopyTo larger slice
	generateTestResourceMetricsSlice().CopyTo(dest)
	assert.Equal(t, generateTestResourceMetricsSlice(), dest)

	// Test CopyTo same size slice
	generateTestResourceMetricsSlice().CopyTo(dest)
	assert.Equal(t, generateTestResourceMetricsSlice(), dest)
}

func TestResourceMetricsSlice_EnsureCapacity(t *testing.T) {
	es := generateTestResourceMetricsSlice()

	// Test ensure smaller capacity.
	const ensureSmallLen = 4
	es.EnsureCapacity(ensureSmallLen)
	assert.Less(t, ensureSmallLen, es.Len())
	assert.Equal(t, es.Len(), cap(*es.orig))
	assert.Equal(t, generateTestResourceMetricsSlice(), es)

	// Test ensure larger capacity
	const ensureLargeLen = 9
	es.EnsureCapacity(ensureLargeLen)
	assert.Less(t, generateTestResourceMetricsSlice().Len(), ensureLargeLen)
	assert.Equal(t, ensureLargeLen, cap(*es.orig))
	assert.Equal(t, generateTestResourceMetricsSlice(), es)
}

func TestResourceMetricsSlice_MoveAndAppendTo(t *testing.T) {
	// Test MoveAndAppendTo to empty
	expectedSlice := generateTestResourceMetricsSlice()
	dest := NewResourceMetricsSlice()
	src := generateTestResourceMetricsSlice()
	src.MoveAndAppendTo(dest)
	assert.Equal(t, generateTestResourceMetricsSlice(), dest)
	assert.Equal(t, 0, src.Len())
	assert.Equal(t, expectedSlice.Len(), dest.Len())

	// Test MoveAndAppendTo empty slice
	src.MoveAndAppendTo(dest)
	assert.Equal(t, generateTestResourceMetricsSlice(), dest)
	assert.Equal(t, 0, src.Len())
	assert.Equal(t, expectedSlice.Len(), dest.Len())

	// Test MoveAndAppendTo not empty slice
	generateTestResourceMetricsSlice().MoveAndAppendTo(dest)
	assert.Equal(t, 2*expectedSlice.Len(), dest.Len())
	for i := 0; i < expectedSlice.Len(); i++ {
		assert.Equal(t, expectedSlice.At(i), dest.At(i))
		assert.Equal(t, expectedSlice.At(i), dest.At(i+expectedSlice.Len()))
	}

	dest.MoveAndAppendTo(dest)
	assert.Equal(t, 2*expectedSlice.Len(), dest.Len())
	for i := 0; i < expectedSlice.Len(); i++ {
		assert.Equal(t, expectedSlice.At(i), dest.At(i))
		assert.Equal(t, expectedSlice.At(i), dest.At(i+expectedSlice.Len()))
	}
}

func TestResourceMetricsSlice_RemoveIf(t *testing.T) {
	// Test RemoveIf on empty slice
	emptySlice := NewResourceMetricsSlice()
	emptySlice.RemoveIf(func(el ResourceMetrics) bool {
		t.Fail()
		return false
	})

	// Test RemoveIf
	filtered := generateTestResourceMetricsSlice()
	pos := 0
	filtered.RemoveIf(func(el ResourceMetrics) bool {
		pos++
		return pos%3 == 0
	})
	assert.Equal(t, 5, filtered.Len())
}

func TestResourceMetricsSliceAll(t *testing.T) {
	ms := generateTestResourceMetricsSlice()
	assert.NotEmpty(t, ms.Len())

	var c int
	for i, v := range ms.All() {
		assert.Equal(t, ms.At(i), v, "element should match")
		c++
	}
	assert.Equal(t, ms.Len(), c, "All elements should have been visited")
}

func TestResourceMetricsSlice_Equal(t *testing.T) {
	es1 := NewResourceMetricsSlice()
	es2 := NewResourceMetricsSlice()
	assert.True(t, es1.Equal(es2))

	es1 = generateTestResourceMetricsSlice()
	es2 = generateTestResourceMetricsSlice()
	assert.True(t, es1.Equal(es2))

	es2 = NewResourceMetricsSlice()
	assert.False(t, es1.Equal(es2))

	es2.AppendEmpty()
	assert.False(t, es1.Equal(es2))

	// Test element-wise inequality - create two slices with same length but different elements
	if es1.Len() > 0 {
		es1 = generateTestResourceMetricsSlice()
		es2 = NewResourceMetricsSlice()
		// Make es2 same length as es1 but with empty elements
		for i := 0; i < es1.Len(); i++ {
			es2.AppendEmpty()
		}
		// This should return false since elements are different
		assert.False(t, es1.Equal(es2))
	}
}

func TestResourceMetricsSlice_Sort(t *testing.T) {
	es := generateTestResourceMetricsSlice()
	es.Sort(func(a, b ResourceMetrics) bool {
		return uintptr(unsafe.Pointer(a.orig)) < uintptr(unsafe.Pointer(b.orig))
	})
	for i := 1; i < es.Len(); i++ {
		assert.Less(t, uintptr(unsafe.Pointer(es.At(i-1).orig)), uintptr(unsafe.Pointer(es.At(i).orig)))
	}
	es.Sort(func(a, b ResourceMetrics) bool {
		return uintptr(unsafe.Pointer(a.orig)) > uintptr(unsafe.Pointer(b.orig))
	})
	for i := 1; i < es.Len(); i++ {
		assert.Greater(t, uintptr(unsafe.Pointer(es.At(i-1).orig)), uintptr(unsafe.Pointer(es.At(i).orig)))
	}
}

func generateTestResourceMetricsSlice() ResourceMetricsSlice {
	es := NewResourceMetricsSlice()
	fillTestResourceMetricsSlice(es)
	return es
}

func fillTestResourceMetricsSlice(es ResourceMetricsSlice) {
	*es.orig = make([]*otlpmetrics.ResourceMetrics, 7)
	for i := 0; i < 7; i++ {
		(*es.orig)[i] = &otlpmetrics.ResourceMetrics{}
		fillTestResourceMetrics(newResourceMetrics((*es.orig)[i], es.state))
	}
}
