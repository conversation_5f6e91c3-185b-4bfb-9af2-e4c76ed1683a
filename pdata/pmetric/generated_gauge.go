// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pmetric

import (
"iter"
"math"
"slices"
"sort"
"strings"

"go.opentelemetry.io/collector/pdata/internal"
"go.opentelemetry.io/collector/pdata/internal/data"
otlpmetrics "go.opentelemetry.io/collector/pdata/internal/data/protogen/metrics/v1"
"go.opentelemetry.io/collector/pdata/pcommon"

)

// Gauge represents the type of a numeric metric that always exports the "current value" for every data point.
//
// This is a reference type, if passed by value and callee modifies it the
// caller will see the modification.
//
// Must use NewGauge function to create new instances.
// Important: zero-initialized instance is not valid for use.
type Gauge struct {
	orig *otlpmetrics.Gauge
	state *internal.State
}

func newGauge(orig *otlpmetrics.Gauge, state *internal.State) Gauge {
	return Gauge{orig: orig, state: state}
}

// NewGauge creates a new empty Gauge.
//
// This must be used only in testing code. Users should use "AppendEmpty" when part of a Slice,
// OR directly access the member if this is embedded in another struct.
func NewGauge() Gauge {
	state := internal.StateMutable
	return newGauge(&otlpmetrics.Gauge{}, &state)
}

// MoveTo moves all properties from the current struct overriding the destination and
// resetting the current instance to its zero value
func (ms Gauge) MoveTo(dest Gauge) {
	ms.state.AssertMutable()
	dest.state.AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if ms.orig == dest.orig {
		return
	}
	*dest.orig = *ms.orig
	*ms.orig = otlpmetrics.Gauge{}
}



// DataPoints returns the DataPoints associated with this Gauge.
func (ms Gauge) DataPoints() NumberDataPointSlice {
	return newNumberDataPointSlice(&ms.orig.DataPoints, ms.state)
}


// CopyTo copies all properties from the current struct overriding the destination.
func (ms Gauge) CopyTo(dest Gauge) {
	dest.state.AssertMutable()
    copyOrigGauge(dest.orig, ms.orig)
}

func copyOrigGauge(dest, src *otlpmetrics.Gauge) {
	dest.DataPoints =copyOrigNumberDataPointSlice(dest.DataPoints, src.DataPoints)
}

// Equal checks equality with another Gauge.
// Optionally accepts CompareOption arguments to customize comparison behavior.
func (ms Gauge) Equal(val Gauge, opts ...CompareOption) bool {
	cfg := NewCompareConfig(opts)
	return ms.DataPoints().Equal(val.DataPoints())
}
