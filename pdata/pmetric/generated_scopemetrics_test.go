// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pmetric

import (
"testing"
"unsafe"

"github.com/stretchr/testify/assert"

"go.opentelemetry.io/collector/pdata/internal"
"go.opentelemetry.io/collector/pdata/internal/data"
otlpmetrics "go.opentelemetry.io/collector/pdata/internal/data/protogen/metrics/v1"
"go.opentelemetry.io/collector/pdata/pcommon"

)

func TestScopeMetrics_MoveTo(t *testing.T) {
	ms := generateTestScopeMetrics()
	dest := NewScopeMetrics()
	ms.MoveTo(dest)
	assert.Equal(t, NewScopeMetrics(), ms)
	assert.Equal(t, generateTestScopeMetrics(), dest)
	dest.MoveTo(dest)
	assert.Equal(t, generateTestScopeMetrics(), dest)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.MoveTo(newScopeMetrics(&otlpmetrics.ScopeMetrics{}, &sharedState)) })
	assert.Panics(t, func() { newScopeMetrics(&otlpmetrics.ScopeMetrics{}, &sharedState).MoveTo(dest) })
}

func TestScopeMetrics_CopyTo(t *testing.T) {
	ms := NewScopeMetrics()
	orig := NewScopeMetrics()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	orig = generateTestScopeMetrics()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.CopyTo(newScopeMetrics(&otlpmetrics.ScopeMetrics{}, &sharedState)) })
}

func TestScopeMetrics_Equal(t *testing.T) {
	ms1 := NewScopeMetrics()
	ms2 := NewScopeMetrics()
	assert.True(t, ms1.Equal(ms2))

	ms1 = generateTestScopeMetrics()
	ms2 = generateTestScopeMetrics()
	assert.True(t, ms1.Equal(ms2))

	ms2 = NewScopeMetrics()
	assert.False(t, ms1.Equal(ms2))
}


func TestScopeMetrics_Scope(t *testing.T) {
	ms := NewScopeMetrics()
	internal.FillTestInstrumentationScope(internal.InstrumentationScope(ms.Scope()))
	assert.Equal(t, pcommon.InstrumentationScope(internal.GenerateTestInstrumentationScope()), ms.Scope())
}

func TestScopeMetrics_SchemaUrl(t *testing.T) {
	ms := NewScopeMetrics()
	assert.Empty(t, ms.SchemaUrl())
	ms.SetSchemaUrl("https://opentelemetry.io/schemas/1.5.0")
	assert.Equal(t, "https://opentelemetry.io/schemas/1.5.0", ms.SchemaUrl())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newScopeMetrics(&otlpmetrics.ScopeMetrics{}, &sharedState).SetSchemaUrl("https://opentelemetry.io/schemas/1.5.0") })
}

func TestScopeMetrics_Metrics(t *testing.T) {
	ms := NewScopeMetrics()
	assert.Equal(t, NewMetricSlice(), ms.Metrics())
	fillTestMetricSlice(ms.Metrics())
	assert.Equal(t, generateTestMetricSlice(), ms.Metrics())
}


func generateTestScopeMetrics() ScopeMetrics {
	tv := NewScopeMetrics()
	fillTestScopeMetrics(tv)
	return tv
}

func fillTestScopeMetrics(tv ScopeMetrics) {
	internal.FillTestInstrumentationScope(internal.NewInstrumentationScope(&tv.orig.Scope, tv.state))
	tv.orig.SchemaUrl = "https://opentelemetry.io/schemas/1.5.0"
	fillTestMetricSlice(newMetricSlice(&tv.orig.Metrics, tv.state))
}

