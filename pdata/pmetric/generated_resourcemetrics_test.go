// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pmetric

import (
"testing"
"unsafe"

"github.com/stretchr/testify/assert"

"go.opentelemetry.io/collector/pdata/internal"
"go.opentelemetry.io/collector/pdata/internal/data"
otlpmetrics "go.opentelemetry.io/collector/pdata/internal/data/protogen/metrics/v1"
"go.opentelemetry.io/collector/pdata/pcommon"

)

func TestResourceMetrics_MoveTo(t *testing.T) {
	ms := generateTestResourceMetrics()
	dest := NewResourceMetrics()
	ms.MoveTo(dest)
	assert.Equal(t, NewResourceMetrics(), ms)
	assert.Equal(t, generateTestResourceMetrics(), dest)
	dest.MoveTo(dest)
	assert.Equal(t, generateTestResourceMetrics(), dest)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.MoveTo(newResourceMetrics(&otlpmetrics.ResourceMetrics{}, &sharedState)) })
	assert.Panics(t, func() { newResourceMetrics(&otlpmetrics.ResourceMetrics{}, &sharedState).MoveTo(dest) })
}

func TestResourceMetrics_CopyTo(t *testing.T) {
	ms := NewResourceMetrics()
	orig := NewResourceMetrics()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	orig = generateTestResourceMetrics()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.CopyTo(newResourceMetrics(&otlpmetrics.ResourceMetrics{}, &sharedState)) })
}

func TestResourceMetrics_Equal(t *testing.T) {
	ms1 := NewResourceMetrics()
	ms2 := NewResourceMetrics()
	assert.True(t, ms1.Equal(ms2))

	ms1 = generateTestResourceMetrics()
	ms2 = generateTestResourceMetrics()
	assert.True(t, ms1.Equal(ms2))

	ms2 = NewResourceMetrics()
	assert.False(t, ms1.Equal(ms2))
}


func TestResourceMetrics_Resource(t *testing.T) {
	ms := NewResourceMetrics()
	internal.FillTestResource(internal.Resource(ms.Resource()))
	assert.Equal(t, pcommon.Resource(internal.GenerateTestResource()), ms.Resource())
}

func TestResourceMetrics_SchemaUrl(t *testing.T) {
	ms := NewResourceMetrics()
	assert.Empty(t, ms.SchemaUrl())
	ms.SetSchemaUrl("https://opentelemetry.io/schemas/1.5.0")
	assert.Equal(t, "https://opentelemetry.io/schemas/1.5.0", ms.SchemaUrl())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newResourceMetrics(&otlpmetrics.ResourceMetrics{}, &sharedState).SetSchemaUrl("https://opentelemetry.io/schemas/1.5.0") })
}

func TestResourceMetrics_ScopeMetrics(t *testing.T) {
	ms := NewResourceMetrics()
	assert.Equal(t, NewScopeMetricsSlice(), ms.ScopeMetrics())
	fillTestScopeMetricsSlice(ms.ScopeMetrics())
	assert.Equal(t, generateTestScopeMetricsSlice(), ms.ScopeMetrics())
}


func generateTestResourceMetrics() ResourceMetrics {
	tv := NewResourceMetrics()
	fillTestResourceMetrics(tv)
	return tv
}

func fillTestResourceMetrics(tv ResourceMetrics) {
	internal.FillTestResource(internal.NewResource(&tv.orig.Resource, tv.state))
	tv.orig.SchemaUrl = "https://opentelemetry.io/schemas/1.5.0"
	fillTestScopeMetricsSlice(newScopeMetricsSlice(&tv.orig.ScopeMetrics, tv.state))
}

