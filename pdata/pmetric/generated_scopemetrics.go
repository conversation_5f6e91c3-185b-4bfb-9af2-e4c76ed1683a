// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pmetric

import (
"iter"
"math"
"slices"
"sort"
"strings"

"go.opentelemetry.io/collector/pdata/internal"
"go.opentelemetry.io/collector/pdata/internal/data"
otlpmetrics "go.opentelemetry.io/collector/pdata/internal/data/protogen/metrics/v1"
"go.opentelemetry.io/collector/pdata/pcommon"

)

// ScopeMetrics is a collection of metrics from a LibraryInstrumentation.
//
// This is a reference type, if passed by value and callee modifies it the
// caller will see the modification.
//
// Must use NewScopeMetrics function to create new instances.
// Important: zero-initialized instance is not valid for use.
type ScopeMetrics struct {
	orig *otlpmetrics.ScopeMetrics
	state *internal.State
}

func newScopeMetrics(orig *otlpmetrics.ScopeMetrics, state *internal.State) ScopeMetrics {
	return ScopeMetrics{orig: orig, state: state}
}

// NewScopeMetrics creates a new empty ScopeMetrics.
//
// This must be used only in testing code. Users should use "AppendEmpty" when part of a Slice,
// OR directly access the member if this is embedded in another struct.
func NewScopeMetrics() ScopeMetrics {
	state := internal.StateMutable
	return newScopeMetrics(&otlpmetrics.ScopeMetrics{}, &state)
}

// MoveTo moves all properties from the current struct overriding the destination and
// resetting the current instance to its zero value
func (ms ScopeMetrics) MoveTo(dest ScopeMetrics) {
	ms.state.AssertMutable()
	dest.state.AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if ms.orig == dest.orig {
		return
	}
	*dest.orig = *ms.orig
	*ms.orig = otlpmetrics.ScopeMetrics{}
}



// Scope returns the scope associated with this ScopeMetrics.
func (ms ScopeMetrics) Scope() pcommon.InstrumentationScope {
	return pcommon.InstrumentationScope(internal.NewInstrumentationScope(&ms.orig.Scope, ms.state))
}
// SchemaUrl returns the schemaurl associated with this ScopeMetrics.
func (ms ScopeMetrics) SchemaUrl() string {
	return ms.orig.SchemaUrl
}

// SetSchemaUrl replaces the schemaurl associated with this ScopeMetrics.
func (ms ScopeMetrics) SetSchemaUrl(v string) {
	ms.state.AssertMutable()
	ms.orig.SchemaUrl = v
}
// Metrics returns the Metrics associated with this ScopeMetrics.
func (ms ScopeMetrics) Metrics() .MetricSlice {
	return newMetricSlice(&ms.orig.Metrics, ms.state)
}


// CopyTo copies all properties from the current struct overriding the destination.
func (ms ScopeMetrics) CopyTo(dest ScopeMetrics) {
	dest.state.AssertMutable()
    copyOrigScopeMetrics(dest.orig, ms.orig)
}

func copyOrigScopeMetrics(dest, src *otlpmetrics.ScopeMetrics) {
	internal.CopyOrigInstrumentationScope(&dest.Scope, &src.Scope)
	dest.SchemaUrl = src.SchemaUrl
	dest.Metrics =copyOrigMetricSlice(dest.Metrics, src.Metrics)
}

// Equal checks equality with another ScopeMetrics.
// Optionally accepts CompareOption arguments to customize comparison behavior.
func (ms ScopeMetrics) Equal(val ScopeMetrics, opts ...CompareOption) bool {
	cfg := NewCompareConfig(opts)
	_ = cfg // may not be used in all cases
	return (cfg.ShouldIgnoreField("Scope") || ms.Scope().Equal(val.Scope())) &&
		(cfg.ShouldIgnoreField("SchemaUrl") || ms.SchemaUrl() == val.SchemaUrl()) &&
		(cfg.ShouldIgnoreField("Metrics") || ms.Metrics().Equal(val.Metrics()))
}
