// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

package pmetric

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestDeltatocumulativeFloatPrecisionFix tests the specific floating point precision issue
// that was causing failures in the deltatocumulativeprocessor contrib module.
// This reproduces the "no-counts" test case that was failing.
func TestDeltatocumulativeFloatPrecisionFix(t *testing.T) {
	t.Run("no-counts", func(t *testing.T) {
		// Create first histogram data point
		dp := NewHistogramDataPoint()
		dp.SetCount(42)
		dp.SetSum(777.12)
		dp.SetMin(12.3)
		dp.SetMax(66.8)

		// Create second histogram data point with precision issues
		in := NewHistogramDataPoint()
		in.SetCount(33)
		in.SetSum(568.2)
		in.SetMin(8.21)
		in.SetMax(23.6)

		// Create expected result after "adding" the histograms
		want := NewHistogramDataPoint()
		want.SetCount(42 + 33)      // 75
		want.SetSum(777.12 + 568.2) // 1345.32
		want.SetMin(8.21)           // min of the two mins
		want.SetMax(66.8)           // max of the two maxes

		// Simulate the addition operation that would happen in deltatocumulativeprocessor
		result := NewHistogramDataPoint()
		result.SetCount(dp.Count() + in.Count())
		result.SetSum(dp.Sum() + in.Sum()) // This creates the precision issue: 1345.3200000000002

		// Choose the minimum of the two minimums
		if dp.Min() < in.Min() {
			result.SetMin(dp.Min())
		} else {
			result.SetMin(in.Min())
		}

		// Choose the maximum of the two maximums
		if dp.Max() > in.Max() {
			result.SetMax(dp.Max())
		} else {
			result.SetMax(in.Max())
		}

		// Before our fix, this would fail because:
		// want.Sum() = 1345.32
		// result.Sum() = 1345.3200000000002 (due to floating point precision)

		// With our fix using tolerance-based comparison, this should pass
		assert.True(t, want.Equal(result),
			"HistogramDataPoint.Equal should handle floating point precision issues")

		// Verify the actual values to show the precision issue exists
		assert.NotEqual(t, want.Sum(), result.Sum(),
			"Sum values should be different due to floating point precision")
		assert.InDelta(t, want.Sum(), result.Sum(), 1e-10,
			"But they should be within reasonable tolerance")

		// Test with explicit strict comparison (FloatTolerance(0) means use strict equality)
		assert.False(t, want.Equal(result, FloatTolerance(0)),
			"With strict comparison, should detect the precision difference")
	})

	t.Run("exponential_histogram_precision", func(t *testing.T) {
		// Test the same issue with ExponentialHistogramDataPoint
		dp := NewExponentialHistogramDataPoint()
		dp.SetCount(42)
		dp.SetSum(777.12)
		dp.SetMin(12.3)
		dp.SetMax(66.8)

		in := NewExponentialHistogramDataPoint()
		in.SetCount(33)
		in.SetSum(568.2)
		in.SetMin(8.21)
		in.SetMax(23.6)

		want := NewExponentialHistogramDataPoint()
		want.SetCount(75)
		want.SetSum(1345.32)
		want.SetMin(8.21)
		want.SetMax(66.8)

		result := NewExponentialHistogramDataPoint()
		result.SetCount(dp.Count() + in.Count())
		result.SetSum(dp.Sum() + in.Sum()) // Creates precision issue

		if dp.Min() < in.Min() {
			result.SetMin(dp.Min())
		} else {
			result.SetMin(in.Min())
		}

		if dp.Max() > in.Max() {
			result.SetMax(dp.Max())
		} else {
			result.SetMax(in.Max())
		}

		// With our fix, this should pass
		assert.True(t, want.Equal(result),
			"ExponentialHistogramDataPoint.Equal should handle floating point precision issues")
	})

	t.Run("backward_compatibility", func(t *testing.T) {
		// Ensure our fix doesn't break existing functionality
		dp1 := NewHistogramDataPoint()
		dp1.SetCount(100)
		dp1.SetSum(2500.75)
		dp1.SetMin(10.5)
		dp1.SetMax(150.25)

		dp2 := NewHistogramDataPoint()
		dp2.SetCount(100)
		dp2.SetSum(2500.75) // Exactly the same
		dp2.SetMin(10.5)
		dp2.SetMax(150.25)

		// These should be equal (backward compatibility)
		assert.True(t, dp1.Equal(dp2), "Identical HistogramDataPoints should be equal")

		// Different values should still be detected
		dp3 := NewHistogramDataPoint()
		dp3.SetCount(100)
		dp3.SetSum(2500.76) // Clearly different
		dp3.SetMin(10.5)
		dp3.SetMax(150.25)

		assert.False(t, dp1.Equal(dp3), "Clearly different values should not be equal")
	})

	t.Run("exact_deltatocumulative_scenario", func(t *testing.T) {
		// This reproduces the exact scenario from the deltatocumulativeprocessor test
		// that was failing in the contrib repository

		// Create the "dp" histogram (first operand)
		dp := NewHistogramDataPoint()
		dp.SetCount(42)
		dp.SetSum(777.12)
		dp.SetMin(12.3)
		dp.SetMax(66.8)

		// Create the "in" histogram (second operand)
		in := NewHistogramDataPoint()
		in.SetCount(33)
		in.SetSum(568.2)
		in.SetMin(8.21)
		in.SetMax(23.6)

		// Create the expected "want" result
		want := NewHistogramDataPoint()
		want.SetCount(42 + 33)      // 75
		want.SetSum(777.12 + 568.2) // 1345.32
		want.SetMin(8.21)           // min(12.3, 8.21)
		want.SetMax(66.8)           // max(66.8, 23.6)

		// Simulate the actual addition that happens in deltatocumulativeprocessor
		// This is where the floating point precision issue occurs
		actualSum := dp.Sum() + in.Sum() // This results in 1345.3200000000002

		result := NewHistogramDataPoint()
		result.SetCount(dp.Count() + in.Count())
		result.SetSum(actualSum) // The problematic value
		result.SetMin(8.21)
		result.SetMax(66.8)

		// Verify the precision issue exists
		assert.NotEqual(t, want.Sum(), result.Sum(),
			"Floating point addition should create precision difference")
		assert.Equal(t, 1345.3200000000002, result.Sum(),
			"Result should have the exact precision issue value")
		assert.Equal(t, 1345.32, want.Sum(),
			"Want should have the exact expected value")

		// The key test: our fix should make these equal despite the precision issue
		assert.True(t, want.Equal(result),
			"HistogramDataPoints should be equal despite floating point precision issues")

		// Verify that strict comparison would fail (proving the issue exists)
		assert.False(t, want.Equal(result, FloatTolerance(0)),
			"Strict comparison should detect the precision difference")
	})
}
