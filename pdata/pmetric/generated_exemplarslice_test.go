// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pmetric

import (
	"testing"
	"unsafe"

	"github.com/stretchr/testify/assert"

	"go.opentelemetry.io/collector/pdata/internal"
	"go.opentelemetry.io/collector/pdata/internal/data"
	otlpmetrics "go.opentelemetry.io/collector/pdata/internal/data/protogen/metrics/v1"
	"go.opentelemetry.io/collector/pdata/pcommon"
)

func TestExemplarSlice(t *testing.T) {
	es := NewExemplarSlice()
	assert.Equal(t, 0, es.Len())
	state := internal.StateMutable
	es = newExemplarSlice(&[]otlpmetrics.Exemplar{}, &state)
	assert.Equal(t, 0, es.Len())

	emptyVal := NewExemplar()
	testVal := generateTestExemplar()
	for i := 0; i < 7; i++ {
		el := es.AppendEmpty()
		assert.Equal(t, emptyVal, es.At(i))
		fillTestExemplar(el)
		assert.Equal(t, testVal, es.At(i))
	}
	assert.Equal(t, 7, es.Len())
}

func TestExemplarSliceReadOnly(t *testing.T) {
	sharedState := internal.StateReadOnly
	es := newExemplarSlice(&[]otlpmetrics.Exemplar{}, &sharedState)
	assert.Equal(t, 0, es.Len())
	assert.Panics(t, func() { es.AppendEmpty() })
	assert.Panics(t, func() { es.EnsureCapacity(2) })
	es2 := NewExemplarSlice()
	es.CopyTo(es2)
	assert.Panics(t, func() { es2.CopyTo(es) })
	assert.Panics(t, func() { es.MoveAndAppendTo(es2) })
	assert.Panics(t, func() { es2.MoveAndAppendTo(es) })
}

func TestExemplarSlice_CopyTo(t *testing.T) {
	dest := NewExemplarSlice()
	// Test CopyTo to empty
	NewExemplarSlice().CopyTo(dest)
	assert.Equal(t, NewExemplarSlice(), dest)

	// Test CopyTo larger slice
	generateTestExemplarSlice().CopyTo(dest)
	assert.Equal(t, generateTestExemplarSlice(), dest)

	// Test CopyTo same size slice
	generateTestExemplarSlice().CopyTo(dest)
	assert.Equal(t, generateTestExemplarSlice(), dest)
}

func TestExemplarSlice_EnsureCapacity(t *testing.T) {
	es := generateTestExemplarSlice()

	// Test ensure smaller capacity.
	const ensureSmallLen = 4
	es.EnsureCapacity(ensureSmallLen)
	assert.Less(t, ensureSmallLen, es.Len())
	assert.Equal(t, es.Len(), cap(*es.orig))
	assert.Equal(t, generateTestExemplarSlice(), es)

	// Test ensure larger capacity
	const ensureLargeLen = 9
	es.EnsureCapacity(ensureLargeLen)
	assert.Less(t, generateTestExemplarSlice().Len(), ensureLargeLen)
	assert.Equal(t, ensureLargeLen, cap(*es.orig))
	assert.Equal(t, generateTestExemplarSlice(), es)
}

func TestExemplarSlice_MoveAndAppendTo(t *testing.T) {
	// Test MoveAndAppendTo to empty
	expectedSlice := generateTestExemplarSlice()
	dest := NewExemplarSlice()
	src := generateTestExemplarSlice()
	src.MoveAndAppendTo(dest)
	assert.Equal(t, generateTestExemplarSlice(), dest)
	assert.Equal(t, 0, src.Len())
	assert.Equal(t, expectedSlice.Len(), dest.Len())

	// Test MoveAndAppendTo empty slice
	src.MoveAndAppendTo(dest)
	assert.Equal(t, generateTestExemplarSlice(), dest)
	assert.Equal(t, 0, src.Len())
	assert.Equal(t, expectedSlice.Len(), dest.Len())

	// Test MoveAndAppendTo not empty slice
	generateTestExemplarSlice().MoveAndAppendTo(dest)
	assert.Equal(t, 2*expectedSlice.Len(), dest.Len())
	for i := 0; i < expectedSlice.Len(); i++ {
		assert.Equal(t, expectedSlice.At(i), dest.At(i))
		assert.Equal(t, expectedSlice.At(i), dest.At(i+expectedSlice.Len()))
	}

	dest.MoveAndAppendTo(dest)
	assert.Equal(t, 2*expectedSlice.Len(), dest.Len())
	for i := 0; i < expectedSlice.Len(); i++ {
		assert.Equal(t, expectedSlice.At(i), dest.At(i))
		assert.Equal(t, expectedSlice.At(i), dest.At(i+expectedSlice.Len()))
	}
}

func TestExemplarSlice_RemoveIf(t *testing.T) {
	// Test RemoveIf on empty slice
	emptySlice := NewExemplarSlice()
	emptySlice.RemoveIf(func(el Exemplar) bool {
		t.Fail()
		return false
	})

	// Test RemoveIf
	filtered := generateTestExemplarSlice()
	pos := 0
	filtered.RemoveIf(func(el Exemplar) bool {
		pos++
		return pos%3 == 0
	})
	assert.Equal(t, 5, filtered.Len())
}

func TestExemplarSliceAll(t *testing.T) {
	ms := generateTestExemplarSlice()
	assert.NotEmpty(t, ms.Len())

	var c int
	for i, v := range ms.All() {
		assert.Equal(t, ms.At(i), v, "element should match")
		c++
	}
	assert.Equal(t, ms.Len(), c, "All elements should have been visited")
}

func TestExemplarSlice_Equal(t *testing.T) {
	es1 := NewExemplarSlice()
	es2 := NewExemplarSlice()
	assert.True(t, es1.Equal(es2))

	es1 = generateTestExemplarSlice()
	es2 = generateTestExemplarSlice()
	assert.True(t, es1.Equal(es2))

	es2 = NewExemplarSlice()
	assert.False(t, es1.Equal(es2))

	es2.AppendEmpty()
	assert.False(t, es1.Equal(es2))

	// Test element-wise inequality - create two slices with same length but different elements
	if es1.Len() > 0 {
		es1 = generateTestExemplarSlice()
		es2 = NewExemplarSlice()
		// Make es2 same length as es1 but with empty elements
		for i := 0; i < es1.Len(); i++ {
			es2.AppendEmpty()
		}
		// This should return false since elements are different
		assert.False(t, es1.Equal(es2))
	}
}

func generateTestExemplarSlice() ExemplarSlice {
	es := NewExemplarSlice()
	fillTestExemplarSlice(es)
	return es
}

func fillTestExemplarSlice(es ExemplarSlice) {
	*es.orig = make([]otlpmetrics.Exemplar, 7)
	for i := 0; i < 7; i++ {
		(*es.orig)[i] = otlpmetrics.Exemplar{}
		fillTestExemplar(newExemplar(&(*es.orig)[i], es.state))
	}
}
