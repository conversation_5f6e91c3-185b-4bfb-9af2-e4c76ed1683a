// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pmetric

import (
"iter"
"math"
"slices"
"sort"
"strings"

"go.opentelemetry.io/collector/pdata/internal"
"go.opentelemetry.io/collector/pdata/internal/data"
otlpmetrics "go.opentelemetry.io/collector/pdata/internal/data/protogen/metrics/v1"
"go.opentelemetry.io/collector/pdata/pcommon"

)

// ResourceMetrics is a collection of metrics from a Resource.
//
// This is a reference type, if passed by value and callee modifies it the
// caller will see the modification.
//
// Must use NewResourceMetrics function to create new instances.
// Important: zero-initialized instance is not valid for use.
type ResourceMetrics struct {
	orig *otlpmetrics.ResourceMetrics
	state *internal.State
}

func newResourceMetrics(orig *otlpmetrics.ResourceMetrics, state *internal.State) ResourceMetrics {
	return ResourceMetrics{orig: orig, state: state}
}

// NewResourceMetrics creates a new empty ResourceMetrics.
//
// This must be used only in testing code. Users should use "AppendEmpty" when part of a Slice,
// OR directly access the member if this is embedded in another struct.
func NewResourceMetrics() ResourceMetrics {
	state := internal.StateMutable
	return newResourceMetrics(&otlpmetrics.ResourceMetrics{}, &state)
}

// MoveTo moves all properties from the current struct overriding the destination and
// resetting the current instance to its zero value
func (ms ResourceMetrics) MoveTo(dest ResourceMetrics) {
	ms.state.AssertMutable()
	dest.state.AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if ms.orig == dest.orig {
		return
	}
	*dest.orig = *ms.orig
	*ms.orig = otlpmetrics.ResourceMetrics{}
}



// Resource returns the resource associated with this ResourceMetrics.
func (ms ResourceMetrics) Resource() pcommon.Resource {
	return pcommon.Resource(internal.NewResource(&ms.orig.Resource, ms.state))
}
// SchemaUrl returns the schemaurl associated with this ResourceMetrics.
func (ms ResourceMetrics) SchemaUrl() string {
	return ms.orig.SchemaUrl
}

// SetSchemaUrl replaces the schemaurl associated with this ResourceMetrics.
func (ms ResourceMetrics) SetSchemaUrl(v string) {
	ms.state.AssertMutable()
	ms.orig.SchemaUrl = v
}
// ScopeMetrics returns the ScopeMetrics associated with this ResourceMetrics.
func (ms ResourceMetrics) ScopeMetrics() ScopeMetricsSlice {
	return newScopeMetricsSlice(&ms.orig.ScopeMetrics, ms.state)
}


// CopyTo copies all properties from the current struct overriding the destination.
func (ms ResourceMetrics) CopyTo(dest ResourceMetrics) {
	dest.state.AssertMutable()
    copyOrigResourceMetrics(dest.orig, ms.orig)
}

func copyOrigResourceMetrics(dest, src *otlpmetrics.ResourceMetrics) {
	internal.CopyOrigResource(&dest.Resource, &src.Resource)
	dest.SchemaUrl = src.SchemaUrl
	dest.ScopeMetrics =copyOrigScopeMetricsSlice(dest.ScopeMetrics, src.ScopeMetrics)
}

// Equal checks equality with another ResourceMetrics.
// Optionally accepts CompareOption arguments to customize comparison behavior.
func (ms ResourceMetrics) Equal(val ResourceMetrics, opts ...CompareOption) bool {
	cfg := NewCompareConfig(opts)
	return ms.Resource().Equal(val.Resource()) &&
		ms.SchemaUrl() == val.SchemaUrl() &&
		ms.ScopeMetrics().Equal(val.ScopeMetrics())
}
