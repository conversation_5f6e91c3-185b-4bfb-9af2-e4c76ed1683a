// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pmetric

import (
	"testing"
	"unsafe"

	"github.com/stretchr/testify/assert"

	"go.opentelemetry.io/collector/pdata/internal"
	"go.opentelemetry.io/collector/pdata/internal/data"
	otlpmetrics "go.opentelemetry.io/collector/pdata/internal/data/protogen/metrics/v1"
	"go.opentelemetry.io/collector/pdata/pcommon"
)

func TestExemplar_MoveTo(t *testing.T) {
	ms := generateTestExemplar()
	dest := NewExemplar()
	ms.MoveTo(dest)
	assert.Equal(t, NewExemplar(), ms)
	assert.Equal(t, generateTestExemplar(), dest)
	dest.MoveTo(dest)
	assert.Equal(t, generateTestExemplar(), dest)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.MoveTo(newExemplar(&otlpmetrics.Exemplar{}, &sharedState)) })
	assert.Panics(t, func() { newExemplar(&otlpmetrics.Exemplar{}, &sharedState).MoveTo(dest) })
}

func TestExemplar_CopyTo(t *testing.T) {
	ms := NewExemplar()
	orig := NewExemplar()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	orig = generateTestExemplar()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.CopyTo(newExemplar(&otlpmetrics.Exemplar{}, &sharedState)) })
}

func TestExemplar_Equal(t *testing.T) {
	ms1 := NewExemplar()
	ms2 := NewExemplar()
	assert.True(t, ms1.Equal(ms2))

	ms1 = generateTestExemplar()
	ms2 = generateTestExemplar()
	assert.True(t, ms1.Equal(ms2))

	ms2 = NewExemplar()
	assert.False(t, ms1.Equal(ms2))
}

func TestExemplar_Timestamp(t *testing.T) {
	ms := NewExemplar()
	assert.Equal(t, pcommon.Timestamp(0), ms.Timestamp())
	testValTimestamp := pcommon.Timestamp(1234567890)
	ms.SetTimestamp(testValTimestamp)
	assert.Equal(t, testValTimestamp, ms.Timestamp())
}

func TestExemplar_ValueType(t *testing.T) {
	tv := NewExemplar()
	assert.Equal(t, ExemplarValueTypeEmpty, tv.ValueType())
}

func TestExemplar_DoubleValue(t *testing.T) {
	ms := NewExemplar()
	assert.InDelta(t, float64(0.0), ms.DoubleValue(), 0.01)
	ms.SetDoubleValue(float64(17.13))
	assert.InDelta(t, float64(17.13), ms.DoubleValue(), 0.01)
	assert.Equal(t, ExemplarValueTypeDouble, ms.ValueType())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newExemplar(&otlpmetrics.Exemplar{}, &sharedState).SetDoubleValue(float64(17.13)) })
}

func TestExemplar_IntValue(t *testing.T) {
	ms := NewExemplar()
	assert.Equal(t, int64(0), ms.IntValue())
	ms.SetIntValue(int64(17))
	assert.Equal(t, int64(17), ms.IntValue())
	assert.Equal(t, ExemplarValueTypeInt, ms.ValueType())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newExemplar(&otlpmetrics.Exemplar{}, &sharedState).SetIntValue(int64(17)) })
}

func TestExemplar_FilteredAttributes(t *testing.T) {
	ms := NewExemplar()
	assert.Equal(t, pcommon.NewMap(), ms.FilteredAttributes())
	internal.FillTestMap(internal.Map(ms.FilteredAttributes()))
	assert.Equal(t, pcommon.Map(internal.GenerateTestMap()), ms.FilteredAttributes())
}

func TestExemplar_TraceID(t *testing.T) {
	ms := NewExemplar()
	assert.Equal(t, pcommon.TraceID(data.TraceID([16]byte{})), ms.TraceID())
	testValTraceID := pcommon.TraceID(data.TraceID([16]byte{1, 2, 3, 4, 5, 6, 7, 8, 8, 7, 6, 5, 4, 3, 2, 1}))
	ms.SetTraceID(testValTraceID)
	assert.Equal(t, testValTraceID, ms.TraceID())
}

func TestExemplar_SpanID(t *testing.T) {
	ms := NewExemplar()
	assert.Equal(t, pcommon.SpanID(data.SpanID([8]byte{})), ms.SpanID())
	testValSpanID := pcommon.SpanID(data.SpanID([8]byte{8, 7, 6, 5, 4, 3, 2, 1}))
	ms.SetSpanID(testValSpanID)
	assert.Equal(t, testValSpanID, ms.SpanID())
}

func generateTestExemplar() Exemplar {
	tv := NewExemplar()
	fillTestExemplar(tv)
	return tv
}

func fillTestExemplar(tv Exemplar) {
	tv.orig.TimeUnixNano = 1234567890
	tv.orig.Value = &otlpmetrics.Exemplar_AsInt{AsInt: int64(17)}
	internal.FillTestMap(internal.NewMap(&tv.orig.FilteredAttributes, tv.state))
	tv.orig.TraceId = data.TraceID([16]byte{1, 2, 3, 4, 5, 6, 7, 8, 8, 7, 6, 5, 4, 3, 2, 1})
	tv.orig.SpanId = data.SpanID([8]byte{8, 7, 6, 5, 4, 3, 2, 1})
}
