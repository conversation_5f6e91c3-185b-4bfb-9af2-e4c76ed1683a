// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pprofile

import (
"testing"
"unsafe"

"github.com/stretchr/testify/assert"

"go.opentelemetry.io/collector/pdata/internal"
otlpprofiles "go.opentelemetry.io/collector/pdata/internal/data/protogen/profiles/v1development"
"go.opentelemetry.io/collector/pdata/pcommon"

)

func TestAttributeUnit_MoveTo(t *testing.T) {
	ms := generateTestAttributeUnit()
	dest := NewAttributeUnit()
	ms.MoveTo(dest)
	assert.Equal(t, NewAttributeUnit(), ms)
	assert.Equal(t, generateTestAttributeUnit(), dest)
	dest.MoveTo(dest)
	assert.Equal(t, generateTestAttributeUnit(), dest)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.MoveTo(newAttributeUnit(&otlpprofiles.AttributeUnit{}, &sharedState)) })
	assert.Panics(t, func() { newAttributeUnit(&otlpprofiles.AttributeUnit{}, &sharedState).MoveTo(dest) })
}

func TestAttributeUnit_CopyTo(t *testing.T) {
	ms := NewAttributeUnit()
	orig := NewAttributeUnit()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	orig = generateTestAttributeUnit()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.CopyTo(newAttributeUnit(&otlpprofiles.AttributeUnit{}, &sharedState)) })
}

func TestAttributeUnit_Equal(t *testing.T) {
	ms1 := NewAttributeUnit()
	ms2 := NewAttributeUnit()
	assert.True(t, ms1.Equal(ms2))

	ms1 = generateTestAttributeUnit()
	ms2 = generateTestAttributeUnit()
	assert.True(t, ms1.Equal(ms2))

	ms2 = NewAttributeUnit()
	assert.False(t, ms1.Equal(ms2))
}


func TestAttributeUnit_AttributeKeyStrindex(t *testing.T) {
	ms := NewAttributeUnit()
	assert.Equal(t, int32(0), ms.AttributeKeyStrindex())
	ms.SetAttributeKeyStrindex(int32(1))
	assert.Equal(t, int32(1), ms.AttributeKeyStrindex())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newAttributeUnit(&otlpprofiles.AttributeUnit{}, &sharedState).SetAttributeKeyStrindex(int32(1)) })
}

func TestAttributeUnit_UnitStrindex(t *testing.T) {
	ms := NewAttributeUnit()
	assert.Equal(t, int32(0), ms.UnitStrindex())
	ms.SetUnitStrindex(int32(1))
	assert.Equal(t, int32(1), ms.UnitStrindex())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newAttributeUnit(&otlpprofiles.AttributeUnit{}, &sharedState).SetUnitStrindex(int32(1)) })
}


func generateTestAttributeUnit() AttributeUnit {
	tv := NewAttributeUnit()
	fillTestAttributeUnit(tv)
	return tv
}

func fillTestAttributeUnit(tv AttributeUnit) {
	tv.orig.AttributeKeyStrindex = int32(1)
	tv.orig.UnitStrindex = int32(1)
}

