// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pprofile

import (
	"iter"
	"math"
	"slices"
	"strings"

	"go.opentelemetry.io/collector/pdata/internal"
	"go.opentelemetry.io/collector/pdata/internal/data"
	otlpprofiles "go.opentelemetry.io/collector/pdata/internal/data/protogen/profiles/v1development"
	"go.opentelemetry.io/collector/pdata/pcommon"
)

// ScopeProfiles is a collection of profiles from a LibraryInstrumentation.
//
// This is a reference type, if passed by value and callee modifies it the
// caller will see the modification.
//
// Must use NewScopeProfiles function to create new instances.
// Important: zero-initialized instance is not valid for use.
type ScopeProfiles struct {
	orig  *otlpprofiles.ScopeProfiles
	state *internal.State
}

func newScopeProfiles(orig *otlpprofiles.ScopeProfiles, state *internal.State) ScopeProfiles {
	return ScopeProfiles{orig: orig, state: state}
}

// NewScopeProfiles creates a new empty ScopeProfiles.
//
// This must be used only in testing code. Users should use "AppendEmpty" when part of a Slice,
// OR directly access the member if this is embedded in another struct.
func NewScopeProfiles() ScopeProfiles {
	state := internal.StateMutable
	return newScopeProfiles(&otlpprofiles.ScopeProfiles{}, &state)
}

// MoveTo moves all properties from the current struct overriding the destination and
// resetting the current instance to its zero value
func (ms ScopeProfiles) MoveTo(dest ScopeProfiles) {
	ms.state.AssertMutable()
	dest.state.AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if ms.orig == dest.orig {
		return
	}
	*dest.orig = *ms.orig
	*ms.orig = otlpprofiles.ScopeProfiles{}
}

// Scope returns the scope associated with this ScopeProfiles.
func (ms ScopeProfiles) Scope() pcommon.InstrumentationScope {
	return pcommon.InstrumentationScope(internal.NewInstrumentationScope(&ms.orig.Scope, ms.state))
}

// SchemaUrl returns the schemaurl associated with this ScopeProfiles.
func (ms ScopeProfiles) SchemaUrl() string {
	return ms.orig.SchemaUrl
}

// SetSchemaUrl replaces the schemaurl associated with this ScopeProfiles.
func (ms ScopeProfiles) SetSchemaUrl(v string) {
	ms.state.AssertMutable()
	ms.orig.SchemaUrl = v
}

// Profiles returns the Profiles associated with this ScopeProfiles.
func (ms ScopeProfiles) Profiles() ProfilesSlice {
	return newProfilesSlice(&ms.orig.Profiles, ms.state)
}

// CopyTo copies all properties from the current struct overriding the destination.
func (ms ScopeProfiles) CopyTo(dest ScopeProfiles) {
	dest.state.AssertMutable()
	copyOrigScopeProfiles(dest.orig, ms.orig)
}

func copyOrigScopeProfiles(dest, src *otlpprofiles.ScopeProfiles) {
	internal.CopyOrigInstrumentationScope(&dest.Scope, &src.Scope)
	dest.SchemaUrl = src.SchemaUrl
	dest.Profiles = copyOrigProfilesSlice(dest.Profiles, src.Profiles)
}

// Equal checks equality with another ScopeProfiles.
// Optionally accepts CompareOption arguments to customize comparison behavior.
func (ms ScopeProfiles) Equal(val ScopeProfiles, opts ...CompareOption) bool {
	cfg := NewCompareConfig(opts)
	return (cfg.ShouldIgnoreField("Scope") || ms.Scope().Equal(val.Scope())) &&
		(cfg.ShouldIgnoreField("SchemaUrl") || ms.SchemaUrl() == val.SchemaUrl()) &&
		(cfg.ShouldIgnoreField("Profiles") || ms.Profiles().Equal(val.Profiles(), opts...))
}
