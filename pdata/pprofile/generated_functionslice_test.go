// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pprofile

import (
	"testing"
	"unsafe"

	"github.com/stretchr/testify/assert"

	"go.opentelemetry.io/collector/pdata/internal"
	otlpprofiles "go.opentelemetry.io/collector/pdata/internal/data/protogen/profiles/v1development"
	"go.opentelemetry.io/collector/pdata/pcommon"
)

func TestFunctionSlice(t *testing.T) {
	es := NewFunctionSlice()
	assert.Equal(t, 0, es.Len())
	state := internal.StateMutable
	es = newFunctionSlice(&[]*otlpprofiles.Function{}, &state)
	assert.Equal(t, 0, es.Len())

	emptyVal := NewFunction()
	testVal := generateTestFunction()
	for i := 0; i < 7; i++ {
		el := es.AppendEmpty()
		assert.Equal(t, emptyVal, es.At(i))
		fillTestFunction(el)
		assert.Equal(t, testVal, es.At(i))
	}
	assert.Equal(t, 7, es.Len())
}

func TestFunctionSliceReadOnly(t *testing.T) {
	sharedState := internal.StateReadOnly
	es := newFunctionSlice(&[]*otlpprofiles.Function{}, &sharedState)
	assert.Equal(t, 0, es.Len())
	assert.Panics(t, func() { es.AppendEmpty() })
	assert.Panics(t, func() { es.EnsureCapacity(2) })
	es2 := NewFunctionSlice()
	es.CopyTo(es2)
	assert.Panics(t, func() { es2.CopyTo(es) })
	assert.Panics(t, func() { es.MoveAndAppendTo(es2) })
	assert.Panics(t, func() { es2.MoveAndAppendTo(es) })
}

func TestFunctionSlice_CopyTo(t *testing.T) {
	dest := NewFunctionSlice()
	// Test CopyTo to empty
	NewFunctionSlice().CopyTo(dest)
	assert.Equal(t, NewFunctionSlice(), dest)

	// Test CopyTo larger slice
	generateTestFunctionSlice().CopyTo(dest)
	assert.Equal(t, generateTestFunctionSlice(), dest)

	// Test CopyTo same size slice
	generateTestFunctionSlice().CopyTo(dest)
	assert.Equal(t, generateTestFunctionSlice(), dest)
}

func TestFunctionSlice_EnsureCapacity(t *testing.T) {
	es := generateTestFunctionSlice()

	// Test ensure smaller capacity.
	const ensureSmallLen = 4
	es.EnsureCapacity(ensureSmallLen)
	assert.Less(t, ensureSmallLen, es.Len())
	assert.Equal(t, es.Len(), cap(*es.orig))
	assert.Equal(t, generateTestFunctionSlice(), es)

	// Test ensure larger capacity
	const ensureLargeLen = 9
	es.EnsureCapacity(ensureLargeLen)
	assert.Less(t, generateTestFunctionSlice().Len(), ensureLargeLen)
	assert.Equal(t, ensureLargeLen, cap(*es.orig))
	assert.Equal(t, generateTestFunctionSlice(), es)
}

func TestFunctionSlice_MoveAndAppendTo(t *testing.T) {
	// Test MoveAndAppendTo to empty
	expectedSlice := generateTestFunctionSlice()
	dest := NewFunctionSlice()
	src := generateTestFunctionSlice()
	src.MoveAndAppendTo(dest)
	assert.Equal(t, generateTestFunctionSlice(), dest)
	assert.Equal(t, 0, src.Len())
	assert.Equal(t, expectedSlice.Len(), dest.Len())

	// Test MoveAndAppendTo empty slice
	src.MoveAndAppendTo(dest)
	assert.Equal(t, generateTestFunctionSlice(), dest)
	assert.Equal(t, 0, src.Len())
	assert.Equal(t, expectedSlice.Len(), dest.Len())

	// Test MoveAndAppendTo not empty slice
	generateTestFunctionSlice().MoveAndAppendTo(dest)
	assert.Equal(t, 2*expectedSlice.Len(), dest.Len())
	for i := 0; i < expectedSlice.Len(); i++ {
		assert.Equal(t, expectedSlice.At(i), dest.At(i))
		assert.Equal(t, expectedSlice.At(i), dest.At(i+expectedSlice.Len()))
	}

	dest.MoveAndAppendTo(dest)
	assert.Equal(t, 2*expectedSlice.Len(), dest.Len())
	for i := 0; i < expectedSlice.Len(); i++ {
		assert.Equal(t, expectedSlice.At(i), dest.At(i))
		assert.Equal(t, expectedSlice.At(i), dest.At(i+expectedSlice.Len()))
	}
}

func TestFunctionSlice_RemoveIf(t *testing.T) {
	// Test RemoveIf on empty slice
	emptySlice := NewFunctionSlice()
	emptySlice.RemoveIf(func(el Function) bool {
		t.Fail()
		return false
	})

	// Test RemoveIf
	filtered := generateTestFunctionSlice()
	pos := 0
	filtered.RemoveIf(func(el Function) bool {
		pos++
		return pos%3 == 0
	})
	assert.Equal(t, 5, filtered.Len())
}

func TestFunctionSliceAll(t *testing.T) {
	ms := generateTestFunctionSlice()
	assert.NotEmpty(t, ms.Len())

	var c int
	for i, v := range ms.All() {
		assert.Equal(t, ms.At(i), v, "element should match")
		c++
	}
	assert.Equal(t, ms.Len(), c, "All elements should have been visited")
}

func TestFunctionSlice_Equal(t *testing.T) {
	es1 := NewFunctionSlice()
	es2 := NewFunctionSlice()
	assert.True(t, es1.Equal(es2))

	es1 = generateTestFunctionSlice()
	es2 = generateTestFunctionSlice()
	assert.True(t, es1.Equal(es2))

	es2 = NewFunctionSlice()
	assert.False(t, es1.Equal(es2))

	es2.AppendEmpty()
	assert.False(t, es1.Equal(es2))

	// Test element-wise inequality - create two slices with same length but different elements
	if es1.Len() > 0 {
		es1 = generateTestFunctionSlice()
		es2 = NewFunctionSlice()
		// Make es2 same length as es1 but with empty elements
		for i := 0; i < es1.Len(); i++ {
			es2.AppendEmpty()
		}
		// This should return false since elements are different
		assert.False(t, es1.Equal(es2))
	}
}

func TestFunctionSlice_Sort(t *testing.T) {
	es := generateTestFunctionSlice()
	es.Sort(func(a, b Function) bool {
		return uintptr(unsafe.Pointer(a.orig)) < uintptr(unsafe.Pointer(b.orig))
	})
	for i := 1; i < es.Len(); i++ {
		assert.Less(t, uintptr(unsafe.Pointer(es.At(i-1).orig)), uintptr(unsafe.Pointer(es.At(i).orig)))
	}
	es.Sort(func(a, b Function) bool {
		return uintptr(unsafe.Pointer(a.orig)) > uintptr(unsafe.Pointer(b.orig))
	})
	for i := 1; i < es.Len(); i++ {
		assert.Greater(t, uintptr(unsafe.Pointer(es.At(i-1).orig)), uintptr(unsafe.Pointer(es.At(i).orig)))
	}
}

func generateTestFunctionSlice() FunctionSlice {
	es := NewFunctionSlice()
	fillTestFunctionSlice(es)
	return es
}

func fillTestFunctionSlice(es FunctionSlice) {
	*es.orig = make([]*otlpprofiles.Function, 7)
	for i := 0; i < 7; i++ {
		(*es.orig)[i] = &otlpprofiles.Function{}
		fillTestFunction(newFunction((*es.orig)[i], es.state))
	}
}
