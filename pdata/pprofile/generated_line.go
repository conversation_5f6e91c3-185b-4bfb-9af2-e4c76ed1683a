// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pprofile

import (
	"iter"
	"math"
	"slices"
	"strings"

	"go.opentelemetry.io/collector/pdata/internal"
	"go.opentelemetry.io/collector/pdata/internal/data"
	otlpprofiles "go.opentelemetry.io/collector/pdata/internal/data/protogen/profiles/v1development"
	"go.opentelemetry.io/collector/pdata/pcommon"
)

// Line details a specific line in a source code, linked to a function.
//
// This is a reference type, if passed by value and callee modifies it the
// caller will see the modification.
//
// Must use NewLine function to create new instances.
// Important: zero-initialized instance is not valid for use.
type Line struct {
	orig  *otlpprofiles.Line
	state *internal.State
}

func newLine(orig *otlpprofiles.Line, state *internal.State) Line {
	return Line{orig: orig, state: state}
}

// NewLine creates a new empty Line.
//
// This must be used only in testing code. Users should use "AppendEmpty" when part of a Slice,
// OR directly access the member if this is embedded in another struct.
func NewLine() Line {
	state := internal.StateMutable
	return newLine(&otlpprofiles.Line{}, &state)
}

// MoveTo moves all properties from the current struct overriding the destination and
// resetting the current instance to its zero value
func (ms Line) MoveTo(dest Line) {
	ms.state.AssertMutable()
	dest.state.AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if ms.orig == dest.orig {
		return
	}
	*dest.orig = *ms.orig
	*ms.orig = otlpprofiles.Line{}
}

// FunctionIndex returns the functionindex associated with this Line.
func (ms Line) FunctionIndex() int32 {
	return ms.orig.FunctionIndex
}

// SetFunctionIndex replaces the functionindex associated with this Line.
func (ms Line) SetFunctionIndex(v int32) {
	ms.state.AssertMutable()
	ms.orig.FunctionIndex = v
}

// Line returns the line associated with this Line.
func (ms Line) Line() int64 {
	return ms.orig.Line
}

// SetLine replaces the line associated with this Line.
func (ms Line) SetLine(v int64) {
	ms.state.AssertMutable()
	ms.orig.Line = v
}

// Column returns the column associated with this Line.
func (ms Line) Column() int64 {
	return ms.orig.Column
}

// SetColumn replaces the column associated with this Line.
func (ms Line) SetColumn(v int64) {
	ms.state.AssertMutable()
	ms.orig.Column = v
}

// CopyTo copies all properties from the current struct overriding the destination.
func (ms Line) CopyTo(dest Line) {
	dest.state.AssertMutable()
	copyOrigLine(dest.orig, ms.orig)
}

func copyOrigLine(dest, src *otlpprofiles.Line) {
	dest.FunctionIndex = src.FunctionIndex
	dest.Line = src.Line
	dest.Column = src.Column
}

// Equal checks equality with another Line.
// Optionally accepts CompareOption arguments to customize comparison behavior.
func (ms Line) Equal(val Line, opts ...CompareOption) bool {
	cfg := NewCompareConfig(opts)
	return (cfg.ShouldIgnoreField("FunctionIndex") || ms.FunctionIndex() == val.FunctionIndex()) &&
		(cfg.ShouldIgnoreField("Line") || ms.Line() == val.Line()) &&
		(cfg.ShouldIgnoreField("Column") || ms.Column() == val.Column())
}
