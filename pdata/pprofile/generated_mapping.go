// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pprofile

import (
"iter"
"math"
"slices"
"strings"

"go.opentelemetry.io/collector/pdata/internal"
"go.opentelemetry.io/collector/pdata/internal/data"
otlpprofiles "go.opentelemetry.io/collector/pdata/internal/data/protogen/profiles/v1development"
"go.opentelemetry.io/collector/pdata/pcommon"

)

// Mapping describes the mapping of a binary in memory, including its address range, file offset, and metadata like build ID
//
// This is a reference type, if passed by value and callee modifies it the
// caller will see the modification.
//
// Must use NewMapping function to create new instances.
// Important: zero-initialized instance is not valid for use.
type Mapping struct {
	orig *otlpprofiles.Mapping
	state *internal.State
}

func newMapping(orig *otlpprofiles.Mapping, state *internal.State) Mapping {
	return Mapping{orig: orig, state: state}
}

// NewMapping creates a new empty Mapping.
//
// This must be used only in testing code. Users should use "AppendEmpty" when part of a Slice,
// OR directly access the member if this is embedded in another struct.
func NewMapping() Mapping {
	state := internal.StateMutable
	return newMapping(&otlpprofiles.Mapping{}, &state)
}

// MoveTo moves all properties from the current struct overriding the destination and
// resetting the current instance to its zero value
func (ms Mapping) MoveTo(dest Mapping) {
	ms.state.AssertMutable()
	dest.state.AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if ms.orig == dest.orig {
		return
	}
	*dest.orig = *ms.orig
	*ms.orig = otlpprofiles.Mapping{}
}



// MemoryStart returns the memorystart associated with this Mapping.
func (ms Mapping) MemoryStart() uint64 {
	return ms.orig.MemoryStart
}

// SetMemoryStart replaces the memorystart associated with this Mapping.
func (ms Mapping) SetMemoryStart(v uint64) {
	ms.state.AssertMutable()
	ms.orig.MemoryStart = v
}
// MemoryLimit returns the memorylimit associated with this Mapping.
func (ms Mapping) MemoryLimit() uint64 {
	return ms.orig.MemoryLimit
}

// SetMemoryLimit replaces the memorylimit associated with this Mapping.
func (ms Mapping) SetMemoryLimit(v uint64) {
	ms.state.AssertMutable()
	ms.orig.MemoryLimit = v
}
// FileOffset returns the fileoffset associated with this Mapping.
func (ms Mapping) FileOffset() uint64 {
	return ms.orig.FileOffset
}

// SetFileOffset replaces the fileoffset associated with this Mapping.
func (ms Mapping) SetFileOffset(v uint64) {
	ms.state.AssertMutable()
	ms.orig.FileOffset = v
}
// FilenameStrindex returns the filenamestrindex associated with this Mapping.
func (ms Mapping) FilenameStrindex() int32 {
	return ms.orig.FilenameStrindex
}

// SetFilenameStrindex replaces the filenamestrindex associated with this Mapping.
func (ms Mapping) SetFilenameStrindex(v int32) {
	ms.state.AssertMutable()
	ms.orig.FilenameStrindex = v
}
// AttributeIndices returns the AttributeIndices associated with this Mapping.
func (ms Mapping) AttributeIndices() pcommon.Int32Slice {
	return pcommon.Int32Slice(internal.NewInt32Slice(&ms.orig.AttributeIndices, ms.state))
}
// HasFunctions returns the hasfunctions associated with this Mapping.
func (ms Mapping) HasFunctions() bool {
	return ms.orig.HasFunctions
}

// SetHasFunctions replaces the hasfunctions associated with this Mapping.
func (ms Mapping) SetHasFunctions(v bool) {
	ms.state.AssertMutable()
	ms.orig.HasFunctions = v
}
// HasFilenames returns the hasfilenames associated with this Mapping.
func (ms Mapping) HasFilenames() bool {
	return ms.orig.HasFilenames
}

// SetHasFilenames replaces the hasfilenames associated with this Mapping.
func (ms Mapping) SetHasFilenames(v bool) {
	ms.state.AssertMutable()
	ms.orig.HasFilenames = v
}
// HasLineNumbers returns the haslinenumbers associated with this Mapping.
func (ms Mapping) HasLineNumbers() bool {
	return ms.orig.HasLineNumbers
}

// SetHasLineNumbers replaces the haslinenumbers associated with this Mapping.
func (ms Mapping) SetHasLineNumbers(v bool) {
	ms.state.AssertMutable()
	ms.orig.HasLineNumbers = v
}
// HasInlineFrames returns the hasinlineframes associated with this Mapping.
func (ms Mapping) HasInlineFrames() bool {
	return ms.orig.HasInlineFrames
}

// SetHasInlineFrames replaces the hasinlineframes associated with this Mapping.
func (ms Mapping) SetHasInlineFrames(v bool) {
	ms.state.AssertMutable()
	ms.orig.HasInlineFrames = v
}


// CopyTo copies all properties from the current struct overriding the destination.
func (ms Mapping) CopyTo(dest Mapping) {
	dest.state.AssertMutable()
    copyOrigMapping(dest.orig, ms.orig)
}

func copyOrigMapping(dest, src *otlpprofiles.Mapping) {
	dest.MemoryStart = src.MemoryStart
	dest.MemoryLimit = src.MemoryLimit
	dest.FileOffset = src.FileOffset
	dest.FilenameStrindex = src.FilenameStrindex
	dest.AttributeIndices =internal.CopyOrigInt32Slice(dest.AttributeIndices, src.AttributeIndices)
	dest.HasFunctions = src.HasFunctions
	dest.HasFilenames = src.HasFilenames
	dest.HasLineNumbers = src.HasLineNumbers
	dest.HasInlineFrames = src.HasInlineFrames
}

// Equal checks equality with another Mapping.
// Optionally accepts CompareOption arguments to customize comparison behavior.
func (ms Mapping) Equal(val Mapping, opts ...CompareOption) bool {
	cfg := NewCompareConfig(opts)
	return (cfg.ShouldIgnoreField("MemoryStart") || ms.MemoryStart() == val.MemoryStart()) &&
		(cfg.ShouldIgnoreField("MemoryLimit") || ms.MemoryLimit() == val.MemoryLimit()) &&
		(cfg.ShouldIgnoreField("FileOffset") || ms.FileOffset() == val.FileOffset()) &&
		(cfg.ShouldIgnoreField("FilenameStrindex") || ms.FilenameStrindex() == val.FilenameStrindex()) &&
		(cfg.ShouldIgnoreField("AttributeIndices") || ms.AttributeIndices().Equal(val.AttributeIndices())) &&
		(cfg.ShouldIgnoreField("HasFunctions") || ms.HasFunctions() == val.HasFunctions()) &&
		(cfg.ShouldIgnoreField("HasFilenames") || ms.HasFilenames() == val.HasFilenames()) &&
		(cfg.ShouldIgnoreField("HasLineNumbers") || ms.HasLineNumbers() == val.HasLineNumbers()) &&
		(cfg.ShouldIgnoreField("HasInlineFrames") || ms.HasInlineFrames() == val.HasInlineFrames())
}
