// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pprofile

import (
	"testing"
	"unsafe"

	"github.com/stretchr/testify/assert"

	"go.opentelemetry.io/collector/pdata/internal"
	otlpprofiles "go.opentelemetry.io/collector/pdata/internal/data/protogen/profiles/v1development"
	"go.opentelemetry.io/collector/pdata/pcommon"
)

func TestAttributeTableSlice(t *testing.T) {
	es := NewAttributeTableSlice()
	assert.Equal(t, 0, es.Len())
	state := internal.StateMutable
	es = newAttributeTableSlice(&[]v1.KeyValue{}, &state)
	assert.Equal(t, 0, es.Len())

	emptyVal := NewAttribute()
	testVal := generateTestAttribute()
	for i := 0; i < 7; i++ {
		el := es.AppendEmpty()
		assert.Equal(t, emptyVal, es.At(i))
		fillTestAttribute(el)
		assert.Equal(t, testVal, es.At(i))
	}
	assert.Equal(t, 7, es.Len())
}

func TestAttributeTableSliceReadOnly(t *testing.T) {
	sharedState := internal.StateReadOnly
	es := newAttributeTableSlice(&[]v1.KeyValue{}, &sharedState)
	assert.Equal(t, 0, es.Len())
	assert.Panics(t, func() { es.AppendEmpty() })
	assert.Panics(t, func() { es.EnsureCapacity(2) })
	es2 := NewAttributeTableSlice()
	es.CopyTo(es2)
	assert.Panics(t, func() { es2.CopyTo(es) })
	assert.Panics(t, func() { es.MoveAndAppendTo(es2) })
	assert.Panics(t, func() { es2.MoveAndAppendTo(es) })
}

func TestAttributeTableSlice_CopyTo(t *testing.T) {
	dest := NewAttributeTableSlice()
	// Test CopyTo to empty
	NewAttributeTableSlice().CopyTo(dest)
	assert.Equal(t, NewAttributeTableSlice(), dest)

	// Test CopyTo larger slice
	generateTestAttributeTableSlice().CopyTo(dest)
	assert.Equal(t, generateTestAttributeTableSlice(), dest)

	// Test CopyTo same size slice
	generateTestAttributeTableSlice().CopyTo(dest)
	assert.Equal(t, generateTestAttributeTableSlice(), dest)
}

func TestAttributeTableSlice_EnsureCapacity(t *testing.T) {
	es := generateTestAttributeTableSlice()

	// Test ensure smaller capacity.
	const ensureSmallLen = 4
	es.EnsureCapacity(ensureSmallLen)
	assert.Less(t, ensureSmallLen, es.Len())
	assert.Equal(t, es.Len(), cap(*es.orig))
	assert.Equal(t, generateTestAttributeTableSlice(), es)

	// Test ensure larger capacity
	const ensureLargeLen = 9
	es.EnsureCapacity(ensureLargeLen)
	assert.Less(t, generateTestAttributeTableSlice().Len(), ensureLargeLen)
	assert.Equal(t, ensureLargeLen, cap(*es.orig))
	assert.Equal(t, generateTestAttributeTableSlice(), es)
}

func TestAttributeTableSlice_MoveAndAppendTo(t *testing.T) {
	// Test MoveAndAppendTo to empty
	expectedSlice := generateTestAttributeTableSlice()
	dest := NewAttributeTableSlice()
	src := generateTestAttributeTableSlice()
	src.MoveAndAppendTo(dest)
	assert.Equal(t, generateTestAttributeTableSlice(), dest)
	assert.Equal(t, 0, src.Len())
	assert.Equal(t, expectedSlice.Len(), dest.Len())

	// Test MoveAndAppendTo empty slice
	src.MoveAndAppendTo(dest)
	assert.Equal(t, generateTestAttributeTableSlice(), dest)
	assert.Equal(t, 0, src.Len())
	assert.Equal(t, expectedSlice.Len(), dest.Len())

	// Test MoveAndAppendTo not empty slice
	generateTestAttributeTableSlice().MoveAndAppendTo(dest)
	assert.Equal(t, 2*expectedSlice.Len(), dest.Len())
	for i := 0; i < expectedSlice.Len(); i++ {
		assert.Equal(t, expectedSlice.At(i), dest.At(i))
		assert.Equal(t, expectedSlice.At(i), dest.At(i+expectedSlice.Len()))
	}

	dest.MoveAndAppendTo(dest)
	assert.Equal(t, 2*expectedSlice.Len(), dest.Len())
	for i := 0; i < expectedSlice.Len(); i++ {
		assert.Equal(t, expectedSlice.At(i), dest.At(i))
		assert.Equal(t, expectedSlice.At(i), dest.At(i+expectedSlice.Len()))
	}
}

func TestAttributeTableSlice_RemoveIf(t *testing.T) {
	// Test RemoveIf on empty slice
	emptySlice := NewAttributeTableSlice()
	emptySlice.RemoveIf(func(el Attribute) bool {
		t.Fail()
		return false
	})

	// Test RemoveIf
	filtered := generateTestAttributeTableSlice()
	pos := 0
	filtered.RemoveIf(func(el Attribute) bool {
		pos++
		return pos%3 == 0
	})
	assert.Equal(t, 5, filtered.Len())
}

func TestAttributeTableSliceAll(t *testing.T) {
	ms := generateTestAttributeTableSlice()
	assert.NotEmpty(t, ms.Len())

	var c int
	for i, v := range ms.All() {
		assert.Equal(t, ms.At(i), v, "element should match")
		c++
	}
	assert.Equal(t, ms.Len(), c, "All elements should have been visited")
}

func TestAttributeTableSlice_Equal(t *testing.T) {
	es1 := NewAttributeTableSlice()
	es2 := NewAttributeTableSlice()
	assert.True(t, es1.Equal(es2))

	es1 = generateTestAttributeTableSlice()
	es2 = generateTestAttributeTableSlice()
	assert.True(t, es1.Equal(es2))

	es2 = NewAttributeTableSlice()
	assert.False(t, es1.Equal(es2))

	es2.AppendEmpty()
	assert.False(t, es1.Equal(es2))

	// Test element-wise inequality - create two slices with same length but different elements
	if es1.Len() > 0 {
		es1 = generateTestAttributeTableSlice()
		es2 = NewAttributeTableSlice()
		// Make es2 same length as es1 but with empty elements
		for i := 0; i < es1.Len(); i++ {
			es2.AppendEmpty()
		}
		// This should return false since elements are different
		assert.False(t, es1.Equal(es2))
	}
}

func generateTestAttributeTableSlice() AttributeTableSlice {
	es := NewAttributeTableSlice()
	fillTestAttributeTableSlice(es)
	return es
}

func fillTestAttributeTableSlice(es AttributeTableSlice) {
	*es.orig = make([]v1.KeyValue, 7)
	for i := 0; i < 7; i++ {
		(*es.orig)[i] = v1.KeyValue{}
		fillTestAttribute(newAttribute(&(*es.orig)[i], es.state))
	}
}
