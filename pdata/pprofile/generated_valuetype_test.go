// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pprofile

import (
	"testing"
	"unsafe"

	"github.com/stretchr/testify/assert"

	"go.opentelemetry.io/collector/pdata/internal"
	otlpprofiles "go.opentelemetry.io/collector/pdata/internal/data/protogen/profiles/v1development"
	"go.opentelemetry.io/collector/pdata/pcommon"
)

func TestValueType_MoveTo(t *testing.T) {
	ms := generateTestValueType()
	dest := NewValueType()
	ms.MoveTo(dest)
	assert.Equal(t, NewValueType(), ms)
	assert.Equal(t, generateTestValueType(), dest)
	dest.MoveTo(dest)
	assert.Equal(t, generateTestValueType(), dest)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.MoveTo(newValueType(&otlpprofiles.ValueType{}, &sharedState)) })
	assert.Panics(t, func() { newValueType(&otlpprofiles.ValueType{}, &sharedState).MoveTo(dest) })
}

func TestValueType_CopyTo(t *testing.T) {
	ms := NewValueType()
	orig := NewValueType()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	orig = generateTestValueType()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.CopyTo(newValueType(&otlpprofiles.ValueType{}, &sharedState)) })
}

func TestValueType_Equal(t *testing.T) {
	ms1 := NewValueType()
	ms2 := NewValueType()
	assert.True(t, ms1.Equal(ms2))

	ms1 = generateTestValueType()
	ms2 = generateTestValueType()
	assert.True(t, ms1.Equal(ms2))

	ms2 = NewValueType()
	assert.False(t, ms1.Equal(ms2))
}

func TestValueType_TypeStrindex(t *testing.T) {
	ms := NewValueType()
	assert.Equal(t, int32(0), ms.TypeStrindex())
	ms.SetTypeStrindex(int32(1))
	assert.Equal(t, int32(1), ms.TypeStrindex())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newValueType(&otlpprofiles.ValueType{}, &sharedState).SetTypeStrindex(int32(1)) })
}

func TestValueType_UnitStrindex(t *testing.T) {
	ms := NewValueType()
	assert.Equal(t, int32(0), ms.UnitStrindex())
	ms.SetUnitStrindex(int32(1))
	assert.Equal(t, int32(1), ms.UnitStrindex())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newValueType(&otlpprofiles.ValueType{}, &sharedState).SetUnitStrindex(int32(1)) })
}

func TestValueType_AggregationTemporality(t *testing.T) {
	ms := NewValueType()
	assert.Equal(t, AggregationTemporality(otlpprofiles.AggregationTemporality(0)), ms.AggregationTemporality())
	testValAggregationTemporality := AggregationTemporality(otlpprofiles.AggregationTemporality(1))
	ms.SetAggregationTemporality(testValAggregationTemporality)
	assert.Equal(t, testValAggregationTemporality, ms.AggregationTemporality())
}

func generateTestValueType() ValueType {
	tv := NewValueType()
	fillTestValueType(tv)
	return tv
}

func fillTestValueType(tv ValueType) {
	tv.orig.TypeStrindex = int32(1)
	tv.orig.UnitStrindex = int32(1)
	tv.orig.AggregationTemporality = otlpprofiles.AggregationTemporality(1)
}
