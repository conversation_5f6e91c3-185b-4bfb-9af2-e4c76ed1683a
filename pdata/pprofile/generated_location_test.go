// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pprofile

import (
"testing"
"unsafe"

"github.com/stretchr/testify/assert"

"go.opentelemetry.io/collector/pdata/internal"
otlpprofiles "go.opentelemetry.io/collector/pdata/internal/data/protogen/profiles/v1development"
"go.opentelemetry.io/collector/pdata/pcommon"

)

func TestLocation_MoveTo(t *testing.T) {
	ms := generateTestLocation()
	dest := NewLocation()
	ms.MoveTo(dest)
	assert.Equal(t, NewLocation(), ms)
	assert.Equal(t, generateTestLocation(), dest)
	dest.MoveTo(dest)
	assert.Equal(t, generateTestLocation(), dest)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.MoveTo(newLocation(&otlpprofiles.Location{}, &sharedState)) })
	assert.Panics(t, func() { newLocation(&otlpprofiles.Location{}, &sharedState).MoveTo(dest) })
}

func TestLocation_CopyTo(t *testing.T) {
	ms := NewLocation()
	orig := NewLocation()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	orig = generateTestLocation()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.CopyTo(newLocation(&otlpprofiles.Location{}, &sharedState)) })
}

func TestLocation_Equal(t *testing.T) {
	ms1 := NewLocation()
	ms2 := NewLocation()
	assert.True(t, ms1.Equal(ms2))

	ms1 = generateTestLocation()
	ms2 = generateTestLocation()
	assert.True(t, ms1.Equal(ms2))

	ms2 = NewLocation()
	assert.False(t, ms1.Equal(ms2))
}


func TestLocation_MappingIndex(t *testing.T) {
	ms := NewLocation()
	assert.Equal(t, int32(0), ms.MappingIndex())
	ms.SetMappingIndex(int32(1))
	assert.True(t, ms.HasMappingIndex())
	assert.Equal(t, int32(1), ms.MappingIndex())
	ms.RemoveMappingIndex()
	assert.False(t, ms.HasMappingIndex())
	dest := NewLocation()
	dest.SetMappingIndex(int32(1))
	ms.CopyTo(dest)
	assert.False(t, dest.HasMappingIndex())
}

func TestLocation_Address(t *testing.T) {
	ms := NewLocation()
	assert.Equal(t, uint64(0), ms.Address())
	ms.SetAddress(uint64(1))
	assert.Equal(t, uint64(1), ms.Address())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newLocation(&otlpprofiles.Location{}, &sharedState).SetAddress(uint64(1)) })
}

func TestLocation_Line(t *testing.T) {
	ms := NewLocation()
	assert.Equal(t, NewLineSlice(), ms.Line())
	fillTestLineSlice(ms.Line())
	assert.Equal(t, generateTestLineSlice(), ms.Line())
}

func TestLocation_IsFolded(t *testing.T) {
	ms := NewLocation()
	assert.False(t, ms.IsFolded())
	ms.SetIsFolded(true)
	assert.True(t, ms.IsFolded())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newLocation(&otlpprofiles.Location{}, &sharedState).SetIsFolded(true) })
}

func TestLocation_AttributeIndices(t *testing.T) {
	ms := NewLocation()
	assert.Equal(t, pcommon.NewInt32Slice(), ms.AttributeIndices())
	internal.FillTestInt32Slice(internal.Int32Slice(ms.AttributeIndices()))
	assert.Equal(t, pcommon.Int32Slice(internal.GenerateTestInt32Slice()), ms.AttributeIndices())
}


func generateTestLocation() Location {
	tv := NewLocation()
	fillTestLocation(tv)
	return tv
}

func fillTestLocation(tv Location) {
	tv.orig.MappingIndex_ = &otlpprofiles.Location_MappingIndex{MappingIndex: int32(1)}
	tv.orig.Address = uint64(1)
	fillTestLineSlice(newLineSlice(&tv.orig.Line, tv.state))
	tv.orig.IsFolded = true
	internal.FillTestInt32Slice(internal.NewInt32Slice(&tv.orig.AttributeIndices, tv.state))
}

