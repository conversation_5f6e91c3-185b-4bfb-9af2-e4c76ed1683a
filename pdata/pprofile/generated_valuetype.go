// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pprofile

import (
"iter"
"math"
"slices"
"strings"

"go.opentelemetry.io/collector/pdata/internal"
"go.opentelemetry.io/collector/pdata/internal/data"
otlpprofiles "go.opentelemetry.io/collector/pdata/internal/data/protogen/profiles/v1development"
"go.opentelemetry.io/collector/pdata/pcommon"

)

// ValueType describes the type and units of a value, with an optional aggregation temporality.
//
// This is a reference type, if passed by value and callee modifies it the
// caller will see the modification.
//
// Must use NewValueType function to create new instances.
// Important: zero-initialized instance is not valid for use.
type ValueType struct {
	orig *otlpprofiles.ValueType
	state *internal.State
}

func newValueType(orig *otlpprofiles.ValueType, state *internal.State) ValueType {
	return ValueType{orig: orig, state: state}
}

// NewValueType creates a new empty ValueType.
//
// This must be used only in testing code. Users should use "AppendEmpty" when part of a Slice,
// OR directly access the member if this is embedded in another struct.
func NewValueType() ValueType {
	state := internal.StateMutable
	return newValueType(&otlpprofiles.ValueType{}, &state)
}

// MoveTo moves all properties from the current struct overriding the destination and
// resetting the current instance to its zero value
func (ms ValueType) MoveTo(dest ValueType) {
	ms.state.AssertMutable()
	dest.state.AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if ms.orig == dest.orig {
		return
	}
	*dest.orig = *ms.orig
	*ms.orig = otlpprofiles.ValueType{}
}



// TypeStrindex returns the typestrindex associated with this ValueType.
func (ms ValueType) TypeStrindex() int32 {
	return ms.orig.TypeStrindex
}

// SetTypeStrindex replaces the typestrindex associated with this ValueType.
func (ms ValueType) SetTypeStrindex(v int32) {
	ms.state.AssertMutable()
	ms.orig.TypeStrindex = v
}
// UnitStrindex returns the unitstrindex associated with this ValueType.
func (ms ValueType) UnitStrindex() int32 {
	return ms.orig.UnitStrindex
}

// SetUnitStrindex replaces the unitstrindex associated with this ValueType.
func (ms ValueType) SetUnitStrindex(v int32) {
	ms.state.AssertMutable()
	ms.orig.UnitStrindex = v
}
// AggregationTemporality returns the aggregationtemporality associated with this ValueType.
func (ms ValueType) AggregationTemporality() AggregationTemporality {
	return AggregationTemporality(ms.orig.AggregationTemporality)
}

// SetAggregationTemporality replaces the aggregationtemporality associated with this ValueType.
func (ms ValueType) SetAggregationTemporality(v AggregationTemporality) {
	ms.state.AssertMutable()
	ms.orig.AggregationTemporality = otlpprofiles.AggregationTemporality(v)
}


// CopyTo copies all properties from the current struct overriding the destination.
func (ms ValueType) CopyTo(dest ValueType) {
	dest.state.AssertMutable()
    copyOrigValueType(dest.orig, ms.orig)
}

func copyOrigValueType(dest, src *otlpprofiles.ValueType) {
	dest.TypeStrindex = src.TypeStrindex
	dest.UnitStrindex = src.UnitStrindex
	dest.AggregationTemporality = src.AggregationTemporality
}

// Equal checks equality with another ValueType.
func (ms ValueType) Equal(val ValueType) bool {
	return ms.TypeStrindex() == val.TypeStrindex() &&
		ms.UnitStrindex() == val.UnitStrindex() &&
		ms.AggregationTemporality() == val.AggregationTemporality()
}
