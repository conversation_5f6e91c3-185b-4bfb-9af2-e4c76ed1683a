// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pprofile

import (
"testing"
"unsafe"

"github.com/stretchr/testify/assert"

"go.opentelemetry.io/collector/pdata/internal"
otlpprofiles "go.opentelemetry.io/collector/pdata/internal/data/protogen/profiles/v1development"
"go.opentelemetry.io/collector/pdata/pcommon"

)

func TestAttributeUnitSlice(t *testing.T) {
	es := NewAttributeUnitSlice()
	assert.Equal(t, 0, es.Len())
	state := internal.StateMutable
	es = newAttributeUnitSlice(&[]*otlpprofiles.AttributeUnit{}, &state)
	assert.Equal(t, 0, es.Len())

	emptyVal := NewAttributeUnit()
	testVal := generateTestAttributeUnit()
	for i := 0; i < 7; i++ {
		el := es.AppendEmpty()
		assert.Equal(t, emptyVal, es.At(i))
		fillTestAttributeUnit(el)
		assert.Equal(t, testVal, es.At(i))
	}
	assert.Equal(t, 7, es.Len())
}

func TestAttributeUnitSliceReadOnly(t *testing.T) {
	sharedState := internal.StateReadOnly
	es := newAttributeUnitSlice(&[]*otlpprofiles.AttributeUnit{}, &sharedState)
	assert.Equal(t, 0, es.Len())
	assert.Panics(t, func() { es.AppendEmpty() })
	assert.Panics(t, func() { es.EnsureCapacity(2) })
	es2 := NewAttributeUnitSlice()
	es.CopyTo(es2)
	assert.Panics(t, func() { es2.CopyTo(es) })
	assert.Panics(t, func() { es.MoveAndAppendTo(es2) })
	assert.Panics(t, func() { es2.MoveAndAppendTo(es) })
}

func TestAttributeUnitSlice_CopyTo(t *testing.T) {
	dest := NewAttributeUnitSlice()
	// Test CopyTo to empty
	NewAttributeUnitSlice().CopyTo(dest)
	assert.Equal(t, NewAttributeUnitSlice(), dest)

	// Test CopyTo larger slice
	generateTestAttributeUnitSlice().CopyTo(dest)
	assert.Equal(t, generateTestAttributeUnitSlice(), dest)

	// Test CopyTo same size slice
	generateTestAttributeUnitSlice().CopyTo(dest)
	assert.Equal(t, generateTestAttributeUnitSlice(), dest)
}

func TestAttributeUnitSlice_EnsureCapacity(t *testing.T) {
	es := generateTestAttributeUnitSlice()

	// Test ensure smaller capacity.
	const ensureSmallLen = 4
	es.EnsureCapacity(ensureSmallLen)
	assert.Less(t, ensureSmallLen, es.Len())
	assert.Equal(t, es.Len(), cap(*es.orig))
	assert.Equal(t, generateTestAttributeUnitSlice(), es)

	// Test ensure larger capacity
	const ensureLargeLen = 9
	es.EnsureCapacity(ensureLargeLen)
	assert.Less(t, generateTestAttributeUnitSlice().Len(), ensureLargeLen)
	assert.Equal(t, ensureLargeLen, cap(*es.orig))
	assert.Equal(t, generateTestAttributeUnitSlice(), es)
}

func TestAttributeUnitSlice_MoveAndAppendTo(t *testing.T) {
	// Test MoveAndAppendTo to empty
	expectedSlice := generateTestAttributeUnitSlice()
	dest := NewAttributeUnitSlice()
	src := generateTestAttributeUnitSlice()
	src.MoveAndAppendTo(dest)
	assert.Equal(t, generateTestAttributeUnitSlice(), dest)
	assert.Equal(t, 0, src.Len())
	assert.Equal(t, expectedSlice.Len(), dest.Len())

	// Test MoveAndAppendTo empty slice
	src.MoveAndAppendTo(dest)
	assert.Equal(t, generateTestAttributeUnitSlice(), dest)
	assert.Equal(t, 0, src.Len())
	assert.Equal(t, expectedSlice.Len(), dest.Len())

	// Test MoveAndAppendTo not empty slice
	generateTestAttributeUnitSlice().MoveAndAppendTo(dest)
	assert.Equal(t, 2*expectedSlice.Len(), dest.Len())
	for i := 0; i < expectedSlice.Len(); i++ {
		assert.Equal(t, expectedSlice.At(i), dest.At(i))
		assert.Equal(t, expectedSlice.At(i), dest.At(i+expectedSlice.Len()))
	}

	dest.MoveAndAppendTo(dest)
	assert.Equal(t, 2*expectedSlice.Len(), dest.Len())
	for i := 0; i < expectedSlice.Len(); i++ {
		assert.Equal(t, expectedSlice.At(i), dest.At(i))
		assert.Equal(t, expectedSlice.At(i), dest.At(i+expectedSlice.Len()))
	}
}

func TestAttributeUnitSlice_RemoveIf(t *testing.T) {
	// Test RemoveIf on empty slice
	emptySlice := NewAttributeUnitSlice()
	emptySlice.RemoveIf(func(el AttributeUnit) bool {
		t.Fail()
		return false
	})

	// Test RemoveIf
	filtered := generateTestAttributeUnitSlice()
	pos := 0
	filtered.RemoveIf(func(el AttributeUnit) bool {
		pos++
		return pos%3 == 0
	})
	assert.Equal(t, 5, filtered.Len())
}

func TestAttributeUnitSliceAll(t *testing.T) {
	ms := generateTestAttributeUnitSlice()
	assert.NotEmpty(t, ms.Len())

	var c int
	for i, v := range ms.All() {
		assert.Equal(t, ms.At(i), v, "element should match")
		c++
	}
	assert.Equal(t, ms.Len(), c, "All elements should have been visited")
}

func TestAttributeUnitSlice_Equal(t *testing.T) {
	es1 := NewAttributeUnitSlice()
	es2 := NewAttributeUnitSlice()
	assert.True(t, es1.Equal(es2))

	es1 = generateTestAttributeUnitSlice()
	es2 = generateTestAttributeUnitSlice()
	assert.True(t, es1.Equal(es2))

	es2 = NewAttributeUnitSlice()
	assert.False(t, es1.Equal(es2))

	es2.AppendEmpty()
	assert.False(t, es1.Equal(es2))

	// Test element-wise inequality - create two slices with same length but different elements
	if es1.Len() > 0 {
		es1 = generateTestAttributeUnitSlice()
		es2 = NewAttributeUnitSlice()
		// Make es2 same length as es1 but with empty elements
		for i := 0; i < es1.Len(); i++ {
			es2.AppendEmpty()
		}
		// This should return false since elements are different
		assert.False(t, es1.Equal(es2))
	}
}

func TestAttributeUnitSlice_Sort(t *testing.T) {
	es := generateTestAttributeUnitSlice()
	es.Sort(func(a, b AttributeUnit) bool {
		return uintptr(unsafe.Pointer(a.orig)) < uintptr(unsafe.Pointer(b.orig))
	})
	for i := 1; i < es.Len(); i++ {
		assert.Less(t, uintptr(unsafe.Pointer(es.At(i-1).orig)), uintptr(unsafe.Pointer(es.At(i).orig)))
	}
	es.Sort(func(a, b AttributeUnit) bool {
		return uintptr(unsafe.Pointer(a.orig)) > uintptr(unsafe.Pointer(b.orig))
	})
	for i := 1; i < es.Len(); i++ {
		assert.Greater(t, uintptr(unsafe.Pointer(es.At(i-1).orig)), uintptr(unsafe.Pointer(es.At(i).orig)))
	}
}

func generateTestAttributeUnitSlice() AttributeUnitSlice {
	es := NewAttributeUnitSlice()
	fillTestAttributeUnitSlice(es)
	return es
}

func fillTestAttributeUnitSlice(es AttributeUnitSlice) {
	*es.orig = make([]*otlpprofiles.AttributeUnit, 7)
	for i := 0; i < 7; i++ {
		(*es.orig)[i] = &otlpprofiles.AttributeUnit{}
		fillTestAttributeUnit(newAttributeUnit((*es.orig)[i], es.state))
	}
}
