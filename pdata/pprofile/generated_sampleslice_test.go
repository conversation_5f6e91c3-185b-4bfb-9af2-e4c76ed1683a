// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pprofile

import (
"testing"
"unsafe"

"github.com/stretchr/testify/assert"

"go.opentelemetry.io/collector/pdata/internal"
otlpprofiles "go.opentelemetry.io/collector/pdata/internal/data/protogen/profiles/v1development"
"go.opentelemetry.io/collector/pdata/pcommon"

)

func TestSampleSlice(t *testing.T) {
	es := NewSampleSlice()
	assert.Equal(t, 0, es.Len())
	state := internal.StateMutable
	es = newSampleSlice(&[]*otlpprofiles.Sample{}, &state)
	assert.Equal(t, 0, es.Len())

	emptyVal := NewSample()
	testVal := generateTestSample()
	for i := 0; i < 7; i++ {
		el := es.AppendEmpty()
		assert.Equal(t, emptyVal, es.At(i))
		fillTestSample(el)
		assert.Equal(t, testVal, es.At(i))
	}
	assert.Equal(t, 7, es.Len())
}

func TestSampleSliceReadOnly(t *testing.T) {
	sharedState := internal.StateReadOnly
	es := newSampleSlice(&[]*otlpprofiles.Sample{}, &sharedState)
	assert.Equal(t, 0, es.Len())
	assert.Panics(t, func() { es.AppendEmpty() })
	assert.Panics(t, func() { es.EnsureCapacity(2) })
	es2 := NewSampleSlice()
	es.CopyTo(es2)
	assert.Panics(t, func() { es2.CopyTo(es) })
	assert.Panics(t, func() { es.MoveAndAppendTo(es2) })
	assert.Panics(t, func() { es2.MoveAndAppendTo(es) })
}

func TestSampleSlice_CopyTo(t *testing.T) {
	dest := NewSampleSlice()
	// Test CopyTo to empty
	NewSampleSlice().CopyTo(dest)
	assert.Equal(t, NewSampleSlice(), dest)

	// Test CopyTo larger slice
	generateTestSampleSlice().CopyTo(dest)
	assert.Equal(t, generateTestSampleSlice(), dest)

	// Test CopyTo same size slice
	generateTestSampleSlice().CopyTo(dest)
	assert.Equal(t, generateTestSampleSlice(), dest)
}

func TestSampleSlice_EnsureCapacity(t *testing.T) {
	es := generateTestSampleSlice()

	// Test ensure smaller capacity.
	const ensureSmallLen = 4
	es.EnsureCapacity(ensureSmallLen)
	assert.Less(t, ensureSmallLen, es.Len())
	assert.Equal(t, es.Len(), cap(*es.orig))
	assert.Equal(t, generateTestSampleSlice(), es)

	// Test ensure larger capacity
	const ensureLargeLen = 9
	es.EnsureCapacity(ensureLargeLen)
	assert.Less(t, generateTestSampleSlice().Len(), ensureLargeLen)
	assert.Equal(t, ensureLargeLen, cap(*es.orig))
	assert.Equal(t, generateTestSampleSlice(), es)
}

func TestSampleSlice_MoveAndAppendTo(t *testing.T) {
	// Test MoveAndAppendTo to empty
	expectedSlice := generateTestSampleSlice()
	dest := NewSampleSlice()
	src := generateTestSampleSlice()
	src.MoveAndAppendTo(dest)
	assert.Equal(t, generateTestSampleSlice(), dest)
	assert.Equal(t, 0, src.Len())
	assert.Equal(t, expectedSlice.Len(), dest.Len())

	// Test MoveAndAppendTo empty slice
	src.MoveAndAppendTo(dest)
	assert.Equal(t, generateTestSampleSlice(), dest)
	assert.Equal(t, 0, src.Len())
	assert.Equal(t, expectedSlice.Len(), dest.Len())

	// Test MoveAndAppendTo not empty slice
	generateTestSampleSlice().MoveAndAppendTo(dest)
	assert.Equal(t, 2*expectedSlice.Len(), dest.Len())
	for i := 0; i < expectedSlice.Len(); i++ {
		assert.Equal(t, expectedSlice.At(i), dest.At(i))
		assert.Equal(t, expectedSlice.At(i), dest.At(i+expectedSlice.Len()))
	}

	dest.MoveAndAppendTo(dest)
	assert.Equal(t, 2*expectedSlice.Len(), dest.Len())
	for i := 0; i < expectedSlice.Len(); i++ {
		assert.Equal(t, expectedSlice.At(i), dest.At(i))
		assert.Equal(t, expectedSlice.At(i), dest.At(i+expectedSlice.Len()))
	}
}

func TestSampleSlice_RemoveIf(t *testing.T) {
	// Test RemoveIf on empty slice
	emptySlice := NewSampleSlice()
	emptySlice.RemoveIf(func(el Sample) bool {
		t.Fail()
		return false
	})

	// Test RemoveIf
	filtered := generateTestSampleSlice()
	pos := 0
	filtered.RemoveIf(func(el Sample) bool {
		pos++
		return pos%3 == 0
	})
	assert.Equal(t, 5, filtered.Len())
}

func TestSampleSliceAll(t *testing.T) {
	ms := generateTestSampleSlice()
	assert.NotEmpty(t, ms.Len())

	var c int
	for i, v := range ms.All() {
		assert.Equal(t, ms.At(i), v, "element should match")
		c++
	}
	assert.Equal(t, ms.Len(), c, "All elements should have been visited")
}

func TestSampleSlice_Equal(t *testing.T) {
	es1 := NewSampleSlice()
	es2 := NewSampleSlice()
	assert.True(t, es1.Equal(es2))

	es1 = generateTestSampleSlice()
	es2 = generateTestSampleSlice()
	assert.True(t, es1.Equal(es2))

	es2 = NewSampleSlice()
	assert.False(t, es1.Equal(es2))

	es2.AppendEmpty()
	assert.False(t, es1.Equal(es2))

	// Test element-wise inequality - create two slices with same length but different elements
	if es1.Len() > 0 {
		es1 = generateTestSampleSlice()
		es2 = NewSampleSlice()
		// Make es2 same length as es1 but with empty elements
		for i := 0; i < es1.Len(); i++ {
			es2.AppendEmpty()
		}
		// This should return false since elements are different
		assert.False(t, es1.Equal(es2))
	}
}

func TestSampleSlice_Sort(t *testing.T) {
	es := generateTestSampleSlice()
	es.Sort(func(a, b Sample) bool {
		return uintptr(unsafe.Pointer(a.orig)) < uintptr(unsafe.Pointer(b.orig))
	})
	for i := 1; i < es.Len(); i++ {
		assert.Less(t, uintptr(unsafe.Pointer(es.At(i-1).orig)), uintptr(unsafe.Pointer(es.At(i).orig)))
	}
	es.Sort(func(a, b Sample) bool {
		return uintptr(unsafe.Pointer(a.orig)) > uintptr(unsafe.Pointer(b.orig))
	})
	for i := 1; i < es.Len(); i++ {
		assert.Greater(t, uintptr(unsafe.Pointer(es.At(i-1).orig)), uintptr(unsafe.Pointer(es.At(i).orig)))
	}
}

func generateTestSampleSlice() SampleSlice {
	es := NewSampleSlice()
	fillTestSampleSlice(es)
	return es
}

func fillTestSampleSlice(es SampleSlice) {
	*es.orig = make([]*otlpprofiles.Sample, 7)
	for i := 0; i < 7; i++ {
		(*es.orig)[i] = &otlpprofiles.Sample{}
		fillTestSample(newSample((*es.orig)[i], es.state))
	}
}
