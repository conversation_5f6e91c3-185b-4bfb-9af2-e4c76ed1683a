// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pprofile

import (
	"go.opentelemetry.io/collector/pdata/internal"
	otlpprofiles "go.opentelemetry.io/collector/pdata/internal/data/protogen/profiles/v1development"
)

// Function describes a function, including its human-readable name, system name, source file, and starting line number in the source.
//
// This is a reference type, if passed by value and callee modifies it the
// caller will see the modification.
//
// Must use NewFunction function to create new instances.
// Important: zero-initialized instance is not valid for use.
type Function struct {
	orig  *otlpprofiles.Function
	state *internal.State
}

func newFunction(orig *otlpprofiles.Function, state *internal.State) Function {
	return Function{orig: orig, state: state}
}

// NewFunction creates a new empty Function.
//
// This must be used only in testing code. Users should use "AppendEmpty" when part of a Slice,
// OR directly access the member if this is embedded in another struct.
func NewFunction() Function {
	state := internal.StateMutable
	return newFunction(&otlpprofiles.Function{}, &state)
}

// MoveTo moves all properties from the current struct overriding the destination and
// resetting the current instance to its zero value
func (ms Function) MoveTo(dest Function) {
	ms.state.AssertMutable()
	dest.state.AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if ms.orig == dest.orig {
		return
	}
	*dest.orig = *ms.orig
	*ms.orig = otlpprofiles.Function{}
}

// NameStrindex returns the namestrindex associated with this Function.
func (ms Function) NameStrindex() int32 {
	return ms.orig.NameStrindex
}

// SetNameStrindex replaces the namestrindex associated with this Function.
func (ms Function) SetNameStrindex(v int32) {
	ms.state.AssertMutable()
	ms.orig.NameStrindex = v
}

// SystemNameStrindex returns the systemnamestrindex associated with this Function.
func (ms Function) SystemNameStrindex() int32 {
	return ms.orig.SystemNameStrindex
}

// SetSystemNameStrindex replaces the systemnamestrindex associated with this Function.
func (ms Function) SetSystemNameStrindex(v int32) {
	ms.state.AssertMutable()
	ms.orig.SystemNameStrindex = v
}

// FilenameStrindex returns the filenamestrindex associated with this Function.
func (ms Function) FilenameStrindex() int32 {
	return ms.orig.FilenameStrindex
}

// SetFilenameStrindex replaces the filenamestrindex associated with this Function.
func (ms Function) SetFilenameStrindex(v int32) {
	ms.state.AssertMutable()
	ms.orig.FilenameStrindex = v
}

// StartLine returns the startline associated with this Function.
func (ms Function) StartLine() int64 {
	return ms.orig.StartLine
}

// SetStartLine replaces the startline associated with this Function.
func (ms Function) SetStartLine(v int64) {
	ms.state.AssertMutable()
	ms.orig.StartLine = v
}

// CopyTo copies all properties from the current struct overriding the destination.
func (ms Function) CopyTo(dest Function) {
	dest.state.AssertMutable()
	copyOrigFunction(dest.orig, ms.orig)
}

func copyOrigFunction(dest, src *otlpprofiles.Function) {
	dest.NameStrindex = src.NameStrindex
	dest.SystemNameStrindex = src.SystemNameStrindex
	dest.FilenameStrindex = src.FilenameStrindex
	dest.StartLine = src.StartLine
}

// Equal checks equality with another Function.
// Optionally accepts CompareOption arguments to customize comparison behavior.
func (ms Function) Equal(val Function, opts ...CompareOption) bool {
	cfg := NewCompareConfig(opts)
	return ms.NameStrindex() == val.NameStrindex() &&
		ms.SystemNameStrindex() == val.SystemNameStrindex() &&
		ms.FilenameStrindex() == val.FilenameStrindex() &&
		ms.StartLine() == val.StartLine()
}
