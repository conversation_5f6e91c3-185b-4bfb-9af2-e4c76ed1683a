// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pprofile

import (
"testing"
"unsafe"

"github.com/stretchr/testify/assert"

"go.opentelemetry.io/collector/pdata/internal"
otlpprofiles "go.opentelemetry.io/collector/pdata/internal/data/protogen/profiles/v1development"
"go.opentelemetry.io/collector/pdata/pcommon"

)

func TestResourceProfilesSlice(t *testing.T) {
	es := NewResourceProfilesSlice()
	assert.Equal(t, 0, es.Len())
	state := internal.StateMutable
	es = newResourceProfilesSlice(&[]*otlpprofiles.ResourceProfiles{}, &state)
	assert.Equal(t, 0, es.Len())

	emptyVal := NewResourceProfiles()
	testVal := generateTestResourceProfiles()
	for i := 0; i < 7; i++ {
		el := es.AppendEmpty()
		assert.Equal(t, emptyVal, es.At(i))
		fillTestResourceProfiles(el)
		assert.Equal(t, testVal, es.At(i))
	}
	assert.Equal(t, 7, es.Len())
}

func TestResourceProfilesSliceReadOnly(t *testing.T) {
	sharedState := internal.StateReadOnly
	es := newResourceProfilesSlice(&[]*otlpprofiles.ResourceProfiles{}, &sharedState)
	assert.Equal(t, 0, es.Len())
	assert.Panics(t, func() { es.AppendEmpty() })
	assert.Panics(t, func() { es.EnsureCapacity(2) })
	es2 := NewResourceProfilesSlice()
	es.CopyTo(es2)
	assert.Panics(t, func() { es2.CopyTo(es) })
	assert.Panics(t, func() { es.MoveAndAppendTo(es2) })
	assert.Panics(t, func() { es2.MoveAndAppendTo(es) })
}

func TestResourceProfilesSlice_CopyTo(t *testing.T) {
	dest := NewResourceProfilesSlice()
	// Test CopyTo to empty
	NewResourceProfilesSlice().CopyTo(dest)
	assert.Equal(t, NewResourceProfilesSlice(), dest)

	// Test CopyTo larger slice
	generateTestResourceProfilesSlice().CopyTo(dest)
	assert.Equal(t, generateTestResourceProfilesSlice(), dest)

	// Test CopyTo same size slice
	generateTestResourceProfilesSlice().CopyTo(dest)
	assert.Equal(t, generateTestResourceProfilesSlice(), dest)
}

func TestResourceProfilesSlice_EnsureCapacity(t *testing.T) {
	es := generateTestResourceProfilesSlice()

	// Test ensure smaller capacity.
	const ensureSmallLen = 4
	es.EnsureCapacity(ensureSmallLen)
	assert.Less(t, ensureSmallLen, es.Len())
	assert.Equal(t, es.Len(), cap(*es.orig))
	assert.Equal(t, generateTestResourceProfilesSlice(), es)

	// Test ensure larger capacity
	const ensureLargeLen = 9
	es.EnsureCapacity(ensureLargeLen)
	assert.Less(t, generateTestResourceProfilesSlice().Len(), ensureLargeLen)
	assert.Equal(t, ensureLargeLen, cap(*es.orig))
	assert.Equal(t, generateTestResourceProfilesSlice(), es)
}

func TestResourceProfilesSlice_MoveAndAppendTo(t *testing.T) {
	// Test MoveAndAppendTo to empty
	expectedSlice := generateTestResourceProfilesSlice()
	dest := NewResourceProfilesSlice()
	src := generateTestResourceProfilesSlice()
	src.MoveAndAppendTo(dest)
	assert.Equal(t, generateTestResourceProfilesSlice(), dest)
	assert.Equal(t, 0, src.Len())
	assert.Equal(t, expectedSlice.Len(), dest.Len())

	// Test MoveAndAppendTo empty slice
	src.MoveAndAppendTo(dest)
	assert.Equal(t, generateTestResourceProfilesSlice(), dest)
	assert.Equal(t, 0, src.Len())
	assert.Equal(t, expectedSlice.Len(), dest.Len())

	// Test MoveAndAppendTo not empty slice
	generateTestResourceProfilesSlice().MoveAndAppendTo(dest)
	assert.Equal(t, 2*expectedSlice.Len(), dest.Len())
	for i := 0; i < expectedSlice.Len(); i++ {
		assert.Equal(t, expectedSlice.At(i), dest.At(i))
		assert.Equal(t, expectedSlice.At(i), dest.At(i+expectedSlice.Len()))
	}

	dest.MoveAndAppendTo(dest)
	assert.Equal(t, 2*expectedSlice.Len(), dest.Len())
	for i := 0; i < expectedSlice.Len(); i++ {
		assert.Equal(t, expectedSlice.At(i), dest.At(i))
		assert.Equal(t, expectedSlice.At(i), dest.At(i+expectedSlice.Len()))
	}
}

func TestResourceProfilesSlice_RemoveIf(t *testing.T) {
	// Test RemoveIf on empty slice
	emptySlice := NewResourceProfilesSlice()
	emptySlice.RemoveIf(func(el ResourceProfiles) bool {
		t.Fail()
		return false
	})

	// Test RemoveIf
	filtered := generateTestResourceProfilesSlice()
	pos := 0
	filtered.RemoveIf(func(el ResourceProfiles) bool {
		pos++
		return pos%3 == 0
	})
	assert.Equal(t, 5, filtered.Len())
}

func TestResourceProfilesSliceAll(t *testing.T) {
	ms := generateTestResourceProfilesSlice()
	assert.NotEmpty(t, ms.Len())

	var c int
	for i, v := range ms.All() {
		assert.Equal(t, ms.At(i), v, "element should match")
		c++
	}
	assert.Equal(t, ms.Len(), c, "All elements should have been visited")
}

func TestResourceProfilesSlice_Equal(t *testing.T) {
	es1 := NewResourceProfilesSlice()
	es2 := NewResourceProfilesSlice()
	assert.True(t, es1.Equal(es2))

	es1 = generateTestResourceProfilesSlice()
	es2 = generateTestResourceProfilesSlice()
	assert.True(t, es1.Equal(es2))

	es2 = NewResourceProfilesSlice()
	assert.False(t, es1.Equal(es2))

	es2.AppendEmpty()
	assert.False(t, es1.Equal(es2))

	// Test element-wise inequality - create two slices with same length but different elements
	if es1.Len() > 0 {
		es1 = generateTestResourceProfilesSlice()
		es2 = NewResourceProfilesSlice()
		// Make es2 same length as es1 but with empty elements
		for i := 0; i < es1.Len(); i++ {
			es2.AppendEmpty()
		}
		// This should return false since elements are different
		assert.False(t, es1.Equal(es2))
	}
}

func TestResourceProfilesSlice_Sort(t *testing.T) {
	es := generateTestResourceProfilesSlice()
	es.Sort(func(a, b ResourceProfiles) bool {
		return uintptr(unsafe.Pointer(a.orig)) < uintptr(unsafe.Pointer(b.orig))
	})
	for i := 1; i < es.Len(); i++ {
		assert.Less(t, uintptr(unsafe.Pointer(es.At(i-1).orig)), uintptr(unsafe.Pointer(es.At(i).orig)))
	}
	es.Sort(func(a, b ResourceProfiles) bool {
		return uintptr(unsafe.Pointer(a.orig)) > uintptr(unsafe.Pointer(b.orig))
	})
	for i := 1; i < es.Len(); i++ {
		assert.Greater(t, uintptr(unsafe.Pointer(es.At(i-1).orig)), uintptr(unsafe.Pointer(es.At(i).orig)))
	}
}

func generateTestResourceProfilesSlice() ResourceProfilesSlice {
	es := NewResourceProfilesSlice()
	fillTestResourceProfilesSlice(es)
	return es
}

func fillTestResourceProfilesSlice(es ResourceProfilesSlice) {
	*es.orig = make([]*otlpprofiles.ResourceProfiles, 7)
	for i := 0; i < 7; i++ {
		(*es.orig)[i] = &otlpprofiles.ResourceProfiles{}
		fillTestResourceProfiles(newResourceProfiles((*es.orig)[i], es.state))
	}
}
