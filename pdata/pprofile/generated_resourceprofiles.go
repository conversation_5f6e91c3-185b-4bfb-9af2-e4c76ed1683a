// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pprofile

import (
"iter"
"math"
"slices"
"strings"

"go.opentelemetry.io/collector/pdata/internal"
"go.opentelemetry.io/collector/pdata/internal/data"
otlpprofiles "go.opentelemetry.io/collector/pdata/internal/data/protogen/profiles/v1development"
"go.opentelemetry.io/collector/pdata/pcommon"

)

// ResourceProfiles is a collection of profiles from a Resource.
//
// This is a reference type, if passed by value and callee modifies it the
// caller will see the modification.
//
// Must use NewResourceProfiles function to create new instances.
// Important: zero-initialized instance is not valid for use.
type ResourceProfiles struct {
	orig *otlpprofiles.ResourceProfiles
	state *internal.State
}

func newResourceProfiles(orig *otlpprofiles.ResourceProfiles, state *internal.State) ResourceProfiles {
	return ResourceProfiles{orig: orig, state: state}
}

// NewResourceProfiles creates a new empty ResourceProfiles.
//
// This must be used only in testing code. Users should use "AppendEmpty" when part of a Slice,
// OR directly access the member if this is embedded in another struct.
func NewResourceProfiles() ResourceProfiles {
	state := internal.StateMutable
	return newResourceProfiles(&otlpprofiles.ResourceProfiles{}, &state)
}

// MoveTo moves all properties from the current struct overriding the destination and
// resetting the current instance to its zero value
func (ms ResourceProfiles) MoveTo(dest ResourceProfiles) {
	ms.state.AssertMutable()
	dest.state.AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if ms.orig == dest.orig {
		return
	}
	*dest.orig = *ms.orig
	*ms.orig = otlpprofiles.ResourceProfiles{}
}



// Resource returns the resource associated with this ResourceProfiles.
func (ms ResourceProfiles) Resource() pcommon.Resource {
	return pcommon.Resource(internal.NewResource(&ms.orig.Resource, ms.state))
}
// SchemaUrl returns the schemaurl associated with this ResourceProfiles.
func (ms ResourceProfiles) SchemaUrl() string {
	return ms.orig.SchemaUrl
}

// SetSchemaUrl replaces the schemaurl associated with this ResourceProfiles.
func (ms ResourceProfiles) SetSchemaUrl(v string) {
	ms.state.AssertMutable()
	ms.orig.SchemaUrl = v
}
// ScopeProfiles returns the ScopeProfiles associated with this ResourceProfiles.
func (ms ResourceProfiles) ScopeProfiles() ScopeProfilesSlice {
	return newScopeProfilesSlice(&ms.orig.ScopeProfiles, ms.state)
}


// CopyTo copies all properties from the current struct overriding the destination.
func (ms ResourceProfiles) CopyTo(dest ResourceProfiles) {
	dest.state.AssertMutable()
    copyOrigResourceProfiles(dest.orig, ms.orig)
}

func copyOrigResourceProfiles(dest, src *otlpprofiles.ResourceProfiles) {
	internal.CopyOrigResource(&dest.Resource, &src.Resource)
	dest.SchemaUrl = src.SchemaUrl
	dest.ScopeProfiles =copyOrigScopeProfilesSlice(dest.ScopeProfiles, src.ScopeProfiles)
}

// Equal checks equality with another ResourceProfiles.
func (ms ResourceProfiles) Equal(val ResourceProfiles) bool {
	return ms.Resource().Equal(val.Resource()) &&
		ms.SchemaUrl() == val.SchemaUrl() &&
		ms.ScopeProfiles().Equal(val.ScopeProfiles())
}
