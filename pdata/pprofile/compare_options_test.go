// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

package pprofile

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"go.opentelemetry.io/collector/pdata/pcommon"
)

func TestProfileCompareOptions(t *testing.T) {
	t.Run("ignore_fields", func(t *testing.T) {
		profile1 := NewProfile()
		profile1.SetPeriod(10)
		profile1.SetDefaultSampleTypeIndex(1)
		profile1.SetTime(pcommon.Timestamp(**********))

		profile2 := NewProfile()
		profile2.SetPeriod(15) // Different
		profile2.SetDefaultSampleTypeIndex(1)
		profile2.SetTime(pcommon.Timestamp(**********))

		// Should be different with default comparison
		assert.False(t, profile1.Equal(profile2), "Should be different with default comparison")

		// Should be equal when ignoring Period field
		assert.True(t, profile1.Equal(profile2, IgnoreFields("Period")),
			"Should be equal when ignoring Period field")
	})

	t.Run("ignore_original_payload", func(t *testing.T) {
		profile1 := NewProfile()
		profile1.SetTime(pcommon.Timestamp(**********))
		profile1.SetOriginalPayloadFormat("json")
		profile1.OriginalPayload().FromRaw([]byte("payload1"))

		profile2 := NewProfile()
		profile2.SetTime(pcommon.Timestamp(**********))
		profile2.SetOriginalPayloadFormat("json")
		profile2.OriginalPayload().FromRaw([]byte("payload2")) // Different payload

		// Should be different with default comparison
		assert.False(t, profile1.Equal(profile2), "Should be different with default comparison")

		// Should be equal when ignoring the OriginalPayload field
		assert.True(t, profile1.Equal(profile2, IgnoreFields("OriginalPayload")),
			"Should be equal when ignoring OriginalPayload field")
	})
}

func TestSampleCompareOptions(t *testing.T) {
	t.Run("ignore_locations_start_index", func(t *testing.T) {
		sample1 := NewSample()
		sample1.SetLocationsStartIndex(10)
		sample1.SetLocationsLength(5)

		sample2 := NewSample()
		sample2.SetLocationsStartIndex(20) // Different start index
		sample2.SetLocationsLength(5)

		// Should be different with default comparison
		assert.False(t, sample1.Equal(sample2), "Should be different with default comparison")

		// Should be equal when ignoring LocationsStartIndex
		assert.True(t, sample1.Equal(sample2, IgnoreFields("LocationsStartIndex")),
			"Should be equal when ignoring LocationsStartIndex")
	})
}

func TestCompareConfigBasicFunctionality(t *testing.T) {
	t.Run("ignore_fields_config", func(t *testing.T) {
		cfg := NewCompareConfig([]CompareOption{
			IgnoreFields("Period", "DefaultSampleTypeIndex"),
		})

		assert.True(t, cfg.ShouldIgnoreField("Period"))
		assert.True(t, cfg.ShouldIgnoreField("DefaultSampleTypeIndex"))
		assert.False(t, cfg.ShouldIgnoreField("Time"))
	})

	t.Run("ignore_paths_config", func(t *testing.T) {
		cfg := NewCompareConfig([]CompareOption{
			IgnorePaths("Sample.*", "SampleType.Unit"),
		})

		assert.True(t, cfg.ShouldIgnorePath("Sample.LocationsStartIndex"))
		assert.True(t, cfg.ShouldIgnorePath("SampleType.Unit"))
		assert.False(t, cfg.ShouldIgnorePath("Time"))
	})
}
