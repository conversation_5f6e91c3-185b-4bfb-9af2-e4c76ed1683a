// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pprofile

import (
	"testing"
	"unsafe"

	"github.com/stretchr/testify/assert"

	"go.opentelemetry.io/collector/pdata/internal"
	otlpprofiles "go.opentelemetry.io/collector/pdata/internal/data/protogen/profiles/v1development"
	"go.opentelemetry.io/collector/pdata/pcommon"
)

func TestFunction_MoveTo(t *testing.T) {
	ms := generateTestFunction()
	dest := NewFunction()
	ms.MoveTo(dest)
	assert.Equal(t, NewFunction(), ms)
	assert.Equal(t, generateTestFunction(), dest)
	dest.MoveTo(dest)
	assert.Equal(t, generateTestFunction(), dest)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.MoveTo(newFunction(&otlpprofiles.Function{}, &sharedState)) })
	assert.Panics(t, func() { newFunction(&otlpprofiles.Function{}, &sharedState).MoveTo(dest) })
}

func TestFunction_CopyTo(t *testing.T) {
	ms := NewFunction()
	orig := NewFunction()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	orig = generateTestFunction()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.CopyTo(newFunction(&otlpprofiles.Function{}, &sharedState)) })
}

func TestFunction_Equal(t *testing.T) {
	ms1 := NewFunction()
	ms2 := NewFunction()
	assert.True(t, ms1.Equal(ms2))

	ms1 = generateTestFunction()
	ms2 = generateTestFunction()
	assert.True(t, ms1.Equal(ms2))

	ms2 = NewFunction()
	assert.False(t, ms1.Equal(ms2))
}

func TestFunction_NameStrindex(t *testing.T) {
	ms := NewFunction()
	assert.Equal(t, int32(0), ms.NameStrindex())
	ms.SetNameStrindex(int32(1))
	assert.Equal(t, int32(1), ms.NameStrindex())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newFunction(&otlpprofiles.Function{}, &sharedState).SetNameStrindex(int32(1)) })
}

func TestFunction_SystemNameStrindex(t *testing.T) {
	ms := NewFunction()
	assert.Equal(t, int32(0), ms.SystemNameStrindex())
	ms.SetSystemNameStrindex(int32(1))
	assert.Equal(t, int32(1), ms.SystemNameStrindex())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newFunction(&otlpprofiles.Function{}, &sharedState).SetSystemNameStrindex(int32(1)) })
}

func TestFunction_FilenameStrindex(t *testing.T) {
	ms := NewFunction()
	assert.Equal(t, int32(0), ms.FilenameStrindex())
	ms.SetFilenameStrindex(int32(1))
	assert.Equal(t, int32(1), ms.FilenameStrindex())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newFunction(&otlpprofiles.Function{}, &sharedState).SetFilenameStrindex(int32(1)) })
}

func TestFunction_StartLine(t *testing.T) {
	ms := NewFunction()
	assert.Equal(t, int64(0), ms.StartLine())
	ms.SetStartLine(int64(1))
	assert.Equal(t, int64(1), ms.StartLine())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newFunction(&otlpprofiles.Function{}, &sharedState).SetStartLine(int64(1)) })
}

func generateTestFunction() Function {
	tv := NewFunction()
	fillTestFunction(tv)
	return tv
}

func fillTestFunction(tv Function) {
	tv.orig.NameStrindex = int32(1)
	tv.orig.SystemNameStrindex = int32(1)
	tv.orig.FilenameStrindex = int32(1)
	tv.orig.StartLine = int64(1)
}
