// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pprofile

import (
	"iter"
	"math"
	"slices"
	"strings"

	"go.opentelemetry.io/collector/pdata/internal"
	"go.opentelemetry.io/collector/pdata/internal/data"
	otlpprofiles "go.opentelemetry.io/collector/pdata/internal/data/protogen/profiles/v1development"
	"go.opentelemetry.io/collector/pdata/pcommon"
)

// ProfilesDictionary is the reference table containing all data shared by profiles across the message being sent.
//
// This is a reference type, if passed by value and callee modifies it the
// caller will see the modification.
//
// Must use NewProfilesDictionary function to create new instances.
// Important: zero-initialized instance is not valid for use.
type ProfilesDictionary struct {
	orig  *otlpprofiles.ProfilesDictionary
	state *internal.State
}

func newProfilesDictionary(orig *otlpprofiles.ProfilesDictionary, state *internal.State) ProfilesDictionary {
	return ProfilesDictionary{orig: orig, state: state}
}

// NewProfilesDictionary creates a new empty ProfilesDictionary.
//
// This must be used only in testing code. Users should use "AppendEmpty" when part of a Slice,
// OR directly access the member if this is embedded in another struct.
func NewProfilesDictionary() ProfilesDictionary {
	state := internal.StateMutable
	return newProfilesDictionary(&otlpprofiles.ProfilesDictionary{}, &state)
}

// MoveTo moves all properties from the current struct overriding the destination and
// resetting the current instance to its zero value
func (ms ProfilesDictionary) MoveTo(dest ProfilesDictionary) {
	ms.state.AssertMutable()
	dest.state.AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if ms.orig == dest.orig {
		return
	}
	*dest.orig = *ms.orig
	*ms.orig = otlpprofiles.ProfilesDictionary{}
}

// MappingTable returns the MappingTable associated with this ProfilesDictionary.
func (ms ProfilesDictionary) MappingTable() MappingSlice {
	return newMappingSlice(&ms.orig.MappingTable, ms.state)
}

// LocationTable returns the LocationTable associated with this ProfilesDictionary.
func (ms ProfilesDictionary) LocationTable() LocationSlice {
	return newLocationSlice(&ms.orig.LocationTable, ms.state)
}

// FunctionTable returns the FunctionTable associated with this ProfilesDictionary.
func (ms ProfilesDictionary) FunctionTable() FunctionSlice {
	return newFunctionSlice(&ms.orig.FunctionTable, ms.state)
}

// LinkTable returns the LinkTable associated with this ProfilesDictionary.
func (ms ProfilesDictionary) LinkTable() LinkSlice {
	return newLinkSlice(&ms.orig.LinkTable, ms.state)
}

// StringTable returns the StringTable associated with this ProfilesDictionary.
func (ms ProfilesDictionary) StringTable() pcommon.StringSlice {
	return pcommon.StringSlice(internal.NewStringSlice(&ms.orig.StringTable, ms.state))
}

// AttributeTable returns the AttributeTable associated with this ProfilesDictionary.
func (ms ProfilesDictionary) AttributeTable() AttributeTableSlice {
	return newAttributeTableSlice(&ms.orig.AttributeTable, ms.state)
}

// AttributeUnits returns the AttributeUnits associated with this ProfilesDictionary.
func (ms ProfilesDictionary) AttributeUnits() AttributeUnitSlice {
	return newAttributeUnitSlice(&ms.orig.AttributeUnits, ms.state)
}

// CopyTo copies all properties from the current struct overriding the destination.
func (ms ProfilesDictionary) CopyTo(dest ProfilesDictionary) {
	dest.state.AssertMutable()
	copyOrigProfilesDictionary(dest.orig, ms.orig)
}

func copyOrigProfilesDictionary(dest, src *otlpprofiles.ProfilesDictionary) {
	dest.MappingTable = copyOrigMappingSlice(dest.MappingTable, src.MappingTable)
	dest.LocationTable = copyOrigLocationSlice(dest.LocationTable, src.LocationTable)
	dest.FunctionTable = copyOrigFunctionSlice(dest.FunctionTable, src.FunctionTable)
	dest.LinkTable = copyOrigLinkSlice(dest.LinkTable, src.LinkTable)
	dest.StringTable = internal.CopyOrigStringSlice(dest.StringTable, src.StringTable)
	dest.AttributeTable = copyOrigAttributeTableSlice(dest.AttributeTable, src.AttributeTable)
	dest.AttributeUnits = copyOrigAttributeUnitSlice(dest.AttributeUnits, src.AttributeUnits)
}

// Equal checks equality with another ProfilesDictionary.
// Optionally accepts CompareOption arguments to customize comparison behavior.
func (ms ProfilesDictionary) Equal(val ProfilesDictionary, opts ...CompareOption) bool {
	cfg := NewCompareConfig(opts)
	return (cfg.ShouldIgnoreField("MappingTable") || ms.MappingTable().Equal(val.MappingTable(), opts...)) &&
		(cfg.ShouldIgnoreField("LocationTable") || ms.LocationTable().Equal(val.LocationTable(), opts...)) &&
		(cfg.ShouldIgnoreField("FunctionTable") || ms.FunctionTable().Equal(val.FunctionTable(), opts...)) &&
		(cfg.ShouldIgnoreField("LinkTable") || ms.LinkTable().Equal(val.LinkTable(), opts...)) &&
		(cfg.ShouldIgnoreField("StringTable") || ms.StringTable().Equal(val.StringTable())) &&
		(cfg.ShouldIgnoreField("AttributeTable") || ms.AttributeTable().Equal(val.AttributeTable(), opts...)) &&
		(cfg.ShouldIgnoreField("AttributeUnits") || ms.AttributeUnits().Equal(val.AttributeUnits(), opts...))
}
