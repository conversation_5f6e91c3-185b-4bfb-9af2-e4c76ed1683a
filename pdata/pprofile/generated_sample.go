// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pprofile

import (
	"go.opentelemetry.io/collector/pdata/internal"
	otlpprofiles "go.opentelemetry.io/collector/pdata/internal/data/protogen/profiles/v1development"
	"go.opentelemetry.io/collector/pdata/pcommon"
)

// Sample represents each record value encountered within a profiled program.
//
// This is a reference type, if passed by value and callee modifies it the
// caller will see the modification.
//
// Must use NewSample function to create new instances.
// Important: zero-initialized instance is not valid for use.
type Sample struct {
	orig  *otlpprofiles.Sample
	state *internal.State
}

func newSample(orig *otlpprofiles.Sample, state *internal.State) Sample {
	return Sample{orig: orig, state: state}
}

// NewSample creates a new empty Sample.
//
// This must be used only in testing code. Users should use "AppendEmpty" when part of a Slice,
// OR directly access the member if this is embedded in another struct.
func NewSample() Sample {
	state := internal.StateMutable
	return newSample(&otlpprofiles.Sample{}, &state)
}

// MoveTo moves all properties from the current struct overriding the destination and
// resetting the current instance to its zero value
func (ms Sample) MoveTo(dest Sample) {
	ms.state.AssertMutable()
	dest.state.AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if ms.orig == dest.orig {
		return
	}
	*dest.orig = *ms.orig
	*ms.orig = otlpprofiles.Sample{}
}

// LocationsStartIndex returns the locationsstartindex associated with this Sample.
func (ms Sample) LocationsStartIndex() int32 {
	return ms.orig.LocationsStartIndex
}

// SetLocationsStartIndex replaces the locationsstartindex associated with this Sample.
func (ms Sample) SetLocationsStartIndex(v int32) {
	ms.state.AssertMutable()
	ms.orig.LocationsStartIndex = v
}

// LocationsLength returns the locationslength associated with this Sample.
func (ms Sample) LocationsLength() int32 {
	return ms.orig.LocationsLength
}

// SetLocationsLength replaces the locationslength associated with this Sample.
func (ms Sample) SetLocationsLength(v int32) {
	ms.state.AssertMutable()
	ms.orig.LocationsLength = v
}

// Value returns the Value associated with this Sample.
func (ms Sample) Value() pcommon.Int64Slice {
	return pcommon.Int64Slice(internal.NewInt64Slice(&ms.orig.Value, ms.state))
}

// AttributeIndices returns the AttributeIndices associated with this Sample.
func (ms Sample) AttributeIndices() pcommon.Int32Slice {
	return pcommon.Int32Slice(internal.NewInt32Slice(&ms.orig.AttributeIndices, ms.state))
}

// LinkIndex returns the linkindex associated with this Sample.
func (ms Sample) LinkIndex() int32 {
	return ms.orig.GetLinkIndex()
}

// HasLinkIndex returns true if the Sample contains a
// LinkIndex value, false otherwise.
func (ms Sample) HasLinkIndex() bool {
	return ms.orig.LinkIndex_ != nil
}

// SetLinkIndex replaces the linkindex associated with this Sample.
func (ms Sample) SetLinkIndex(v int32) {
	ms.state.AssertMutable()
	ms.orig.LinkIndex_ = &otlpprofiles.Sample_LinkIndex{LinkIndex: v}
}

// RemoveLinkIndex removes the linkindex associated with this Sample.
func (ms Sample) RemoveLinkIndex() {
	ms.state.AssertMutable()
	ms.orig.LinkIndex_ = nil
}

// TimestampsUnixNano returns the TimestampsUnixNano associated with this Sample.
func (ms Sample) TimestampsUnixNano() pcommon.UInt64Slice {
	return pcommon.UInt64Slice(internal.NewUInt64Slice(&ms.orig.TimestampsUnixNano, ms.state))
}

// CopyTo copies all properties from the current struct overriding the destination.
func (ms Sample) CopyTo(dest Sample) {
	dest.state.AssertMutable()
	copyOrigSample(dest.orig, ms.orig)
}

func copyOrigSample(dest, src *otlpprofiles.Sample) {
	dest.LocationsStartIndex = src.LocationsStartIndex
	dest.LocationsLength = src.LocationsLength
	dest.Value = internal.CopyOrigInt64Slice(dest.Value, src.Value)
	dest.AttributeIndices = internal.CopyOrigInt32Slice(dest.AttributeIndices, src.AttributeIndices)
	if src.LinkIndex_ == nil {
		dest.LinkIndex_ = nil
	} else {
		dest.LinkIndex_ = &otlpprofiles.Sample_LinkIndex{LinkIndex: src.GetLinkIndex()}
	}
	dest.TimestampsUnixNano = internal.CopyOrigUInt64Slice(dest.TimestampsUnixNano, src.TimestampsUnixNano)
}

// Equal checks equality with another Sample.
// Optionally accepts CompareOption arguments to customize comparison behavior.
func (ms Sample) Equal(val Sample, opts ...CompareOption) bool {
	cfg := NewCompareConfig(opts)
	return (cfg.ShouldIgnoreField("LocationsStartIndex") || ms.LocationsStartIndex() == val.LocationsStartIndex()) &&
		(cfg.ShouldIgnoreField("LocationsLength") || ms.LocationsLength() == val.LocationsLength()) &&
		(cfg.ShouldIgnoreField("Value") || ms.Value().Equal(val.Value())) &&
		(cfg.ShouldIgnoreField("AttributeIndices") || ms.AttributeIndices().Equal(val.AttributeIndices())) &&
		(cfg.ShouldIgnoreField("LinkIndex") || (ms.HasLinkIndex() == val.HasLinkIndex() && (!ms.HasLinkIndex() || ms.LinkIndex() == val.LinkIndex()))) &&
		(cfg.ShouldIgnoreField("TimestampsUnixNano") || ms.TimestampsUnixNano().Equal(val.TimestampsUnixNano()))
}
