// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pprofile

import (
	"testing"
	"unsafe"

	"github.com/stretchr/testify/assert"

	"go.opentelemetry.io/collector/pdata/internal"
	otlpprofiles "go.opentelemetry.io/collector/pdata/internal/data/protogen/profiles/v1development"
	"go.opentelemetry.io/collector/pdata/pcommon"
)

func TestLinkSlice(t *testing.T) {
	es := NewLinkSlice()
	assert.Equal(t, 0, es.Len())
	state := internal.StateMutable
	es = newLinkSlice(&[]*otlpprofiles.Link{}, &state)
	assert.Equal(t, 0, es.Len())

	emptyVal := NewLink()
	testVal := generateTestLink()
	for i := 0; i < 7; i++ {
		el := es.AppendEmpty()
		assert.Equal(t, emptyVal, es.At(i))
		fillTestLink(el)
		assert.Equal(t, testVal, es.At(i))
	}
	assert.Equal(t, 7, es.Len())
}

func TestLinkSliceReadOnly(t *testing.T) {
	sharedState := internal.StateReadOnly
	es := newLinkSlice(&[]*otlpprofiles.Link{}, &sharedState)
	assert.Equal(t, 0, es.Len())
	assert.Panics(t, func() { es.AppendEmpty() })
	assert.Panics(t, func() { es.EnsureCapacity(2) })
	es2 := NewLinkSlice()
	es.CopyTo(es2)
	assert.Panics(t, func() { es2.CopyTo(es) })
	assert.Panics(t, func() { es.MoveAndAppendTo(es2) })
	assert.Panics(t, func() { es2.MoveAndAppendTo(es) })
}

func TestLinkSlice_CopyTo(t *testing.T) {
	dest := NewLinkSlice()
	// Test CopyTo to empty
	NewLinkSlice().CopyTo(dest)
	assert.Equal(t, NewLinkSlice(), dest)

	// Test CopyTo larger slice
	generateTestLinkSlice().CopyTo(dest)
	assert.Equal(t, generateTestLinkSlice(), dest)

	// Test CopyTo same size slice
	generateTestLinkSlice().CopyTo(dest)
	assert.Equal(t, generateTestLinkSlice(), dest)
}

func TestLinkSlice_EnsureCapacity(t *testing.T) {
	es := generateTestLinkSlice()

	// Test ensure smaller capacity.
	const ensureSmallLen = 4
	es.EnsureCapacity(ensureSmallLen)
	assert.Less(t, ensureSmallLen, es.Len())
	assert.Equal(t, es.Len(), cap(*es.orig))
	assert.Equal(t, generateTestLinkSlice(), es)

	// Test ensure larger capacity
	const ensureLargeLen = 9
	es.EnsureCapacity(ensureLargeLen)
	assert.Less(t, generateTestLinkSlice().Len(), ensureLargeLen)
	assert.Equal(t, ensureLargeLen, cap(*es.orig))
	assert.Equal(t, generateTestLinkSlice(), es)
}

func TestLinkSlice_MoveAndAppendTo(t *testing.T) {
	// Test MoveAndAppendTo to empty
	expectedSlice := generateTestLinkSlice()
	dest := NewLinkSlice()
	src := generateTestLinkSlice()
	src.MoveAndAppendTo(dest)
	assert.Equal(t, generateTestLinkSlice(), dest)
	assert.Equal(t, 0, src.Len())
	assert.Equal(t, expectedSlice.Len(), dest.Len())

	// Test MoveAndAppendTo empty slice
	src.MoveAndAppendTo(dest)
	assert.Equal(t, generateTestLinkSlice(), dest)
	assert.Equal(t, 0, src.Len())
	assert.Equal(t, expectedSlice.Len(), dest.Len())

	// Test MoveAndAppendTo not empty slice
	generateTestLinkSlice().MoveAndAppendTo(dest)
	assert.Equal(t, 2*expectedSlice.Len(), dest.Len())
	for i := 0; i < expectedSlice.Len(); i++ {
		assert.Equal(t, expectedSlice.At(i), dest.At(i))
		assert.Equal(t, expectedSlice.At(i), dest.At(i+expectedSlice.Len()))
	}

	dest.MoveAndAppendTo(dest)
	assert.Equal(t, 2*expectedSlice.Len(), dest.Len())
	for i := 0; i < expectedSlice.Len(); i++ {
		assert.Equal(t, expectedSlice.At(i), dest.At(i))
		assert.Equal(t, expectedSlice.At(i), dest.At(i+expectedSlice.Len()))
	}
}

func TestLinkSlice_RemoveIf(t *testing.T) {
	// Test RemoveIf on empty slice
	emptySlice := NewLinkSlice()
	emptySlice.RemoveIf(func(el Link) bool {
		t.Fail()
		return false
	})

	// Test RemoveIf
	filtered := generateTestLinkSlice()
	pos := 0
	filtered.RemoveIf(func(el Link) bool {
		pos++
		return pos%3 == 0
	})
	assert.Equal(t, 5, filtered.Len())
}

func TestLinkSliceAll(t *testing.T) {
	ms := generateTestLinkSlice()
	assert.NotEmpty(t, ms.Len())

	var c int
	for i, v := range ms.All() {
		assert.Equal(t, ms.At(i), v, "element should match")
		c++
	}
	assert.Equal(t, ms.Len(), c, "All elements should have been visited")
}

func TestLinkSlice_Equal(t *testing.T) {
	es1 := NewLinkSlice()
	es2 := NewLinkSlice()
	assert.True(t, es1.Equal(es2))

	es1 = generateTestLinkSlice()
	es2 = generateTestLinkSlice()
	assert.True(t, es1.Equal(es2))

	es2 = NewLinkSlice()
	assert.False(t, es1.Equal(es2))

	es2.AppendEmpty()
	assert.False(t, es1.Equal(es2))

	// Test element-wise inequality - create two slices with same length but different elements
	if es1.Len() > 0 {
		es1 = generateTestLinkSlice()
		es2 = NewLinkSlice()
		// Make es2 same length as es1 but with empty elements
		for i := 0; i < es1.Len(); i++ {
			es2.AppendEmpty()
		}
		// This should return false since elements are different
		assert.False(t, es1.Equal(es2))
	}
}

func TestLinkSlice_Sort(t *testing.T) {
	es := generateTestLinkSlice()
	es.Sort(func(a, b Link) bool {
		return uintptr(unsafe.Pointer(a.orig)) < uintptr(unsafe.Pointer(b.orig))
	})
	for i := 1; i < es.Len(); i++ {
		assert.Less(t, uintptr(unsafe.Pointer(es.At(i-1).orig)), uintptr(unsafe.Pointer(es.At(i).orig)))
	}
	es.Sort(func(a, b Link) bool {
		return uintptr(unsafe.Pointer(a.orig)) > uintptr(unsafe.Pointer(b.orig))
	})
	for i := 1; i < es.Len(); i++ {
		assert.Greater(t, uintptr(unsafe.Pointer(es.At(i-1).orig)), uintptr(unsafe.Pointer(es.At(i).orig)))
	}
}

func generateTestLinkSlice() LinkSlice {
	es := NewLinkSlice()
	fillTestLinkSlice(es)
	return es
}

func fillTestLinkSlice(es LinkSlice) {
	*es.orig = make([]*otlpprofiles.Link, 7)
	for i := 0; i < 7; i++ {
		(*es.orig)[i] = &otlpprofiles.Link{}
		fillTestLink(newLink((*es.orig)[i], es.state))
	}
}
