// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pprofile

import (
"testing"
"unsafe"

"github.com/stretchr/testify/assert"

"go.opentelemetry.io/collector/pdata/internal"
otlpprofiles "go.opentelemetry.io/collector/pdata/internal/data/protogen/profiles/v1development"
"go.opentelemetry.io/collector/pdata/pcommon"

)

func TestAttribute_MoveTo(t *testing.T) {
	ms := generateTestAttribute()
	dest := NewAttribute()
	ms.MoveTo(dest)
	assert.Equal(t, NewAttribute(), ms)
	assert.Equal(t, generateTestAttribute(), dest)
	dest.MoveTo(dest)
	assert.Equal(t, generateTestAttribute(), dest)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.MoveTo(newAttribute(&v1.KeyValue{}, &sharedState)) })
	assert.Panics(t, func() { newAttribute(&v1.KeyValue{}, &sharedState).MoveTo(dest) })
}

func TestAttribute_CopyTo(t *testing.T) {
	ms := NewAttribute()
	orig := NewAttribute()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	orig = generateTestAttribute()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.CopyTo(newAttribute(&v1.KeyValue{}, &sharedState)) })
}

func TestAttribute_Equal(t *testing.T) {
	ms1 := NewAttribute()
	ms2 := NewAttribute()
	assert.True(t, ms1.Equal(ms2))

	ms1 = generateTestAttribute()
	ms2 = generateTestAttribute()
	assert.True(t, ms1.Equal(ms2))

	ms2 = NewAttribute()
	assert.False(t, ms1.Equal(ms2))
}


func TestAttribute_Key(t *testing.T) {
	ms := NewAttribute()
	assert.Empty(t, ms.Key())
	ms.SetKey("key")
	assert.Equal(t, "key", ms.Key())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newAttribute(&v1.KeyValue{}, &sharedState).SetKey("key") })
}

func TestAttribute_Value(t *testing.T) {
	ms := NewAttribute()
	internal.FillTestValue(internal.Value(ms.Value()))
	assert.Equal(t, pcommon.Value(internal.GenerateTestValue()), ms.Value())
}


func generateTestAttribute() Attribute {
	tv := NewAttribute()
	fillTestAttribute(tv)
	return tv
}

func fillTestAttribute(tv Attribute) {
	tv.orig.Key = "key"
	internal.FillTestValue(internal.NewValue(&tv.orig.Value, tv.state))
}

