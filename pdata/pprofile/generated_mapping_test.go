// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pprofile

import (
"testing"
"unsafe"

"github.com/stretchr/testify/assert"

"go.opentelemetry.io/collector/pdata/internal"
otlpprofiles "go.opentelemetry.io/collector/pdata/internal/data/protogen/profiles/v1development"
"go.opentelemetry.io/collector/pdata/pcommon"

)

func TestMapping_MoveTo(t *testing.T) {
	ms := generateTestMapping()
	dest := NewMapping()
	ms.MoveTo(dest)
	assert.Equal(t, NewMapping(), ms)
	assert.Equal(t, generateTestMapping(), dest)
	dest.MoveTo(dest)
	assert.Equal(t, generateTestMapping(), dest)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.MoveTo(newMapping(&otlpprofiles.Mapping{}, &sharedState)) })
	assert.Panics(t, func() { newMapping(&otlpprofiles.Mapping{}, &sharedState).MoveTo(dest) })
}

func TestMapping_CopyTo(t *testing.T) {
	ms := NewMapping()
	orig := NewMapping()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	orig = generateTestMapping()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.CopyTo(newMapping(&otlpprofiles.Mapping{}, &sharedState)) })
}

func TestMapping_Equal(t *testing.T) {
	ms1 := NewMapping()
	ms2 := NewMapping()
	assert.True(t, ms1.Equal(ms2))

	ms1 = generateTestMapping()
	ms2 = generateTestMapping()
	assert.True(t, ms1.Equal(ms2))

	ms2 = NewMapping()
	assert.False(t, ms1.Equal(ms2))
}


func TestMapping_MemoryStart(t *testing.T) {
	ms := NewMapping()
	assert.Equal(t, uint64(0), ms.MemoryStart())
	ms.SetMemoryStart(uint64(1))
	assert.Equal(t, uint64(1), ms.MemoryStart())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newMapping(&otlpprofiles.Mapping{}, &sharedState).SetMemoryStart(uint64(1)) })
}

func TestMapping_MemoryLimit(t *testing.T) {
	ms := NewMapping()
	assert.Equal(t, uint64(0), ms.MemoryLimit())
	ms.SetMemoryLimit(uint64(1))
	assert.Equal(t, uint64(1), ms.MemoryLimit())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newMapping(&otlpprofiles.Mapping{}, &sharedState).SetMemoryLimit(uint64(1)) })
}

func TestMapping_FileOffset(t *testing.T) {
	ms := NewMapping()
	assert.Equal(t, uint64(0), ms.FileOffset())
	ms.SetFileOffset(uint64(1))
	assert.Equal(t, uint64(1), ms.FileOffset())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newMapping(&otlpprofiles.Mapping{}, &sharedState).SetFileOffset(uint64(1)) })
}

func TestMapping_FilenameStrindex(t *testing.T) {
	ms := NewMapping()
	assert.Equal(t, int32(0), ms.FilenameStrindex())
	ms.SetFilenameStrindex(int32(1))
	assert.Equal(t, int32(1), ms.FilenameStrindex())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newMapping(&otlpprofiles.Mapping{}, &sharedState).SetFilenameStrindex(int32(1)) })
}

func TestMapping_AttributeIndices(t *testing.T) {
	ms := NewMapping()
	assert.Equal(t, pcommon.NewInt32Slice(), ms.AttributeIndices())
	internal.FillTestInt32Slice(internal.Int32Slice(ms.AttributeIndices()))
	assert.Equal(t, pcommon.Int32Slice(internal.GenerateTestInt32Slice()), ms.AttributeIndices())
}

func TestMapping_HasFunctions(t *testing.T) {
	ms := NewMapping()
	assert.False(t, ms.HasFunctions())
	ms.SetHasFunctions(true)
	assert.True(t, ms.HasFunctions())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newMapping(&otlpprofiles.Mapping{}, &sharedState).SetHasFunctions(true) })
}

func TestMapping_HasFilenames(t *testing.T) {
	ms := NewMapping()
	assert.False(t, ms.HasFilenames())
	ms.SetHasFilenames(true)
	assert.True(t, ms.HasFilenames())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newMapping(&otlpprofiles.Mapping{}, &sharedState).SetHasFilenames(true) })
}

func TestMapping_HasLineNumbers(t *testing.T) {
	ms := NewMapping()
	assert.False(t, ms.HasLineNumbers())
	ms.SetHasLineNumbers(true)
	assert.True(t, ms.HasLineNumbers())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newMapping(&otlpprofiles.Mapping{}, &sharedState).SetHasLineNumbers(true) })
}

func TestMapping_HasInlineFrames(t *testing.T) {
	ms := NewMapping()
	assert.False(t, ms.HasInlineFrames())
	ms.SetHasInlineFrames(true)
	assert.True(t, ms.HasInlineFrames())
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { newMapping(&otlpprofiles.Mapping{}, &sharedState).SetHasInlineFrames(true) })
}


func generateTestMapping() Mapping {
	tv := NewMapping()
	fillTestMapping(tv)
	return tv
}

func fillTestMapping(tv Mapping) {
	tv.orig.MemoryStart = uint64(1)
	tv.orig.MemoryLimit = uint64(1)
	tv.orig.FileOffset = uint64(1)
	tv.orig.FilenameStrindex = int32(1)
	internal.FillTestInt32Slice(internal.NewInt32Slice(&tv.orig.AttributeIndices, tv.state))
	tv.orig.HasFunctions = true
	tv.orig.HasFilenames = true
	tv.orig.HasLineNumbers = true
	tv.orig.HasInlineFrames = true
}

