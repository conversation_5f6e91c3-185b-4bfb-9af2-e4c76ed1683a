// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pprofile

import (
	"go.opentelemetry.io/collector/pdata/internal"
	"go.opentelemetry.io/collector/pdata/internal/data"
	otlpprofiles "go.opentelemetry.io/collector/pdata/internal/data/protogen/profiles/v1development"
	"go.opentelemetry.io/collector/pdata/pcommon"
)

// Profile are an implementation of the pprofextended data model.

// This is a reference type, if passed by value and callee modifies it the
// caller will see the modification.
//
// Must use NewProfile function to create new instances.
// Important: zero-initialized instance is not valid for use.
type Profile struct {
	orig  *otlpprofiles.Profile
	state *internal.State
}

func newProfile(orig *otlpprofiles.Profile, state *internal.State) Profile {
	return Profile{orig: orig, state: state}
}

// NewProfile creates a new empty Profile.
//
// This must be used only in testing code. Users should use "AppendEmpty" when part of a Slice,
// OR directly access the member if this is embedded in another struct.
func NewProfile() Profile {
	state := internal.StateMutable
	return newProfile(&otlpprofiles.Profile{}, &state)
}

// MoveTo moves all properties from the current struct overriding the destination and
// resetting the current instance to its zero value
func (ms Profile) MoveTo(dest Profile) {
	ms.state.AssertMutable()
	dest.state.AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if ms.orig == dest.orig {
		return
	}
	*dest.orig = *ms.orig
	*ms.orig = otlpprofiles.Profile{}
}

// SampleType returns the SampleType associated with this Profile.
func (ms Profile) SampleType() ValueTypeSlice {
	return newValueTypeSlice(&ms.orig.SampleType, ms.state)
}

// Sample returns the Sample associated with this Profile.
func (ms Profile) Sample() SampleSlice {
	return newSampleSlice(&ms.orig.Sample, ms.state)
}

// LocationIndices returns the LocationIndices associated with this Profile.
func (ms Profile) LocationIndices() pcommon.Int32Slice {
	return pcommon.Int32Slice(internal.NewInt32Slice(&ms.orig.LocationIndices, ms.state))
}

// Time returns the time associated with this Profile.
func (ms Profile) Time() pcommon.Timestamp {
	return pcommon.Timestamp(ms.orig.TimeNanos)
}

// SetTime replaces the time associated with this Profile.
func (ms Profile) SetTime(v pcommon.Timestamp) {
	ms.state.AssertMutable()
	ms.orig.TimeNanos = int64(v)
}

// Duration returns the duration associated with this Profile.
func (ms Profile) Duration() pcommon.Timestamp {
	return pcommon.Timestamp(ms.orig.DurationNanos)
}

// SetDuration replaces the duration associated with this Profile.
func (ms Profile) SetDuration(v pcommon.Timestamp) {
	ms.state.AssertMutable()
	ms.orig.DurationNanos = int64(v)
}

// StartTime returns the starttime associated with this Profile.
func (ms Profile) StartTime() pcommon.Timestamp {
	return pcommon.Timestamp(ms.orig.TimeNanos)
}

// SetStartTime replaces the starttime associated with this Profile.
func (ms Profile) SetStartTime(v pcommon.Timestamp) {
	ms.state.AssertMutable()
	ms.orig.TimeNanos = int64(v)
}

// PeriodType returns the periodtype associated with this Profile.
func (ms Profile) PeriodType() ValueType {
	return newValueType(&ms.orig.PeriodType, ms.state)
}

// Period returns the period associated with this Profile.
func (ms Profile) Period() int64 {
	return ms.orig.Period
}

// SetPeriod replaces the period associated with this Profile.
func (ms Profile) SetPeriod(v int64) {
	ms.state.AssertMutable()
	ms.orig.Period = v
}

// CommentStrindices returns the CommentStrindices associated with this Profile.
func (ms Profile) CommentStrindices() pcommon.Int32Slice {
	return pcommon.Int32Slice(internal.NewInt32Slice(&ms.orig.CommentStrindices, ms.state))
}

// DefaultSampleTypeIndex returns the defaultsampletypeindex associated with this Profile.
func (ms Profile) DefaultSampleTypeIndex() int32 {
	return ms.orig.DefaultSampleTypeIndex
}

// SetDefaultSampleTypeIndex replaces the defaultsampletypeindex associated with this Profile.
func (ms Profile) SetDefaultSampleTypeIndex(v int32) {
	ms.state.AssertMutable()
	ms.orig.DefaultSampleTypeIndex = v
}

// ProfileID returns the profileid associated with this Profile.
func (ms Profile) ProfileID() ProfileID {
	return ProfileID(ms.orig.ProfileId)
}

// SetProfileID replaces the profileid associated with this Profile.
func (ms Profile) SetProfileID(v ProfileID) {
	ms.state.AssertMutable()
	ms.orig.ProfileId = data.ProfileID(v)
}

// AttributeIndices returns the AttributeIndices associated with this Profile.
func (ms Profile) AttributeIndices() pcommon.Int32Slice {
	return pcommon.Int32Slice(internal.NewInt32Slice(&ms.orig.AttributeIndices, ms.state))
}

// DroppedAttributesCount returns the droppedattributescount associated with this Profile.
func (ms Profile) DroppedAttributesCount() uint32 {
	return ms.orig.DroppedAttributesCount
}

// SetDroppedAttributesCount replaces the droppedattributescount associated with this Profile.
func (ms Profile) SetDroppedAttributesCount(v uint32) {
	ms.state.AssertMutable()
	ms.orig.DroppedAttributesCount = v
}

// OriginalPayloadFormat returns the originalpayloadformat associated with this Profile.
func (ms Profile) OriginalPayloadFormat() string {
	return ms.orig.OriginalPayloadFormat
}

// SetOriginalPayloadFormat replaces the originalpayloadformat associated with this Profile.
func (ms Profile) SetOriginalPayloadFormat(v string) {
	ms.state.AssertMutable()
	ms.orig.OriginalPayloadFormat = v
}

// OriginalPayload returns the OriginalPayload associated with this Profile.
func (ms Profile) OriginalPayload() pcommon.ByteSlice {
	return pcommon.ByteSlice(internal.NewByteSlice(&ms.orig.OriginalPayload, ms.state))
}

// CopyTo copies all properties from the current struct overriding the destination.
func (ms Profile) CopyTo(dest Profile) {
	dest.state.AssertMutable()
	copyOrigProfile(dest.orig, ms.orig)
}

func copyOrigProfile(dest, src *otlpprofiles.Profile) {
	dest.SampleType = copyOrigValueTypeSlice(dest.SampleType, src.SampleType)
	dest.Sample = copyOrigSampleSlice(dest.Sample, src.Sample)
	dest.LocationIndices = internal.CopyOrigInt32Slice(dest.LocationIndices, src.LocationIndices)
	dest.TimeNanos = src.TimeNanos
	dest.DurationNanos = src.DurationNanos
	dest.TimeNanos = src.TimeNanos
	copyOrigValueType(&dest.PeriodType, &src.PeriodType)
	dest.Period = src.Period
	dest.CommentStrindices = internal.CopyOrigInt32Slice(dest.CommentStrindices, src.CommentStrindices)
	dest.DefaultSampleTypeIndex = src.DefaultSampleTypeIndex
	dest.ProfileId = src.ProfileId
	dest.AttributeIndices = internal.CopyOrigInt32Slice(dest.AttributeIndices, src.AttributeIndices)
	dest.DroppedAttributesCount = src.DroppedAttributesCount
	dest.OriginalPayloadFormat = src.OriginalPayloadFormat
	dest.OriginalPayload = internal.CopyOrigByteSlice(dest.OriginalPayload, src.OriginalPayload)
}

// Equal checks equality with another Profile.
// Optionally accepts CompareOption arguments to customize comparison behavior.
func (ms Profile) Equal(val Profile, opts ...CompareOption) bool {
	cfg := NewCompareConfig(opts)
	return ms.SampleType().Equal(val.SampleType()) &&
		ms.Sample().Equal(val.Sample()) &&
		ms.LocationIndices().Equal(val.LocationIndices()) &&
		ms.Time() == val.Time() &&
		ms.Duration() == val.Duration() &&
		ms.StartTime() == val.StartTime() &&
		ms.PeriodType().Equal(val.PeriodType()) &&
		ms.Period() == val.Period() &&
		ms.CommentStrindices().Equal(val.CommentStrindices()) &&
		ms.DefaultSampleTypeIndex() == val.DefaultSampleTypeIndex() &&
		ms.ProfileID() == val.ProfileID() &&
		ms.AttributeIndices().Equal(val.AttributeIndices()) &&
		ms.DroppedAttributesCount() == val.DroppedAttributesCount() &&
		ms.OriginalPayloadFormat() == val.OriginalPayloadFormat() &&
		ms.OriginalPayload().Equal(val.OriginalPayload())
}
