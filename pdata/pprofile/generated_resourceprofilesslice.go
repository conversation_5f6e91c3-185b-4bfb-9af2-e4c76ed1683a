// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pprofile

import (
	"iter"
	"sort"

	"go.opentelemetry.io/collector/pdata/internal"
	otlpprofiles "go.opentelemetry.io/collector/pdata/internal/data/protogen/profiles/v1development"
)

// ResourceProfilesSlice logically represents a slice of ResourceProfiles.
//
// This is a reference type. If passed by value and callee modifies it, the
// caller will see the modification.
//
// Must use NewResourceProfilesSlice function to create new instances.
// Important: zero-initialized instance is not valid for use.
type ResourceProfilesSlice struct {
	orig  *[]*otlpprofiles.ResourceProfiles
	state *internal.State
}

func newResourceProfilesSlice(orig *[]*otlpprofiles.ResourceProfiles, state *internal.State) ResourceProfilesSlice {
	return ResourceProfilesSlice{orig: orig, state: state}
}

// NewResourceProfilesSlice creates a ResourceProfilesSlice with 0 elements.
// Can use "EnsureCapacity" to initialize with a given capacity.
func NewResourceProfilesSlice() ResourceProfilesSlice {
	orig := []*otlpprofiles.ResourceProfiles(nil)
	state := internal.StateMutable
	return newResourceProfilesSlice(&orig, &state)
}

// Len returns the number of elements in the slice.
//
// Returns "0" for a newly instance created with "NewResourceProfilesSlice()".
func (es ResourceProfilesSlice) Len() int {
	return len(*es.orig)
}

// At returns the element at the given index.
//
// This function is used mostly for iterating over all the values in the slice:
//
//	for i := 0; i < es.Len(); i++ {
//	    e := es.At(i)
//	    ... // Do something with the element
//	}
func (es ResourceProfilesSlice) At(i int) ResourceProfiles {
	return newResourceProfiles((*es.orig)[i], es.state)
}

// All returns an iterator over index-value pairs in the slice.
//
//	for i, v := range es.All() {
//	    ... // Do something with index-value pair
//	}
func (es ResourceProfilesSlice) All() iter.Seq2[int, ResourceProfiles] {
	return func(yield func(int, ResourceProfiles) bool) {
		for i := 0; i < es.Len(); i++ {
			if !yield(i, es.At(i)) {
				return
			}
		}
	}
}

// EnsureCapacity is an operation that ensures the slice has at least the specified capacity.
// 1. If the newCap <= cap then no change in capacity.
// 2. If the newCap > cap then the slice capacity will be expanded to equal newCap.
//
// Here is how a new ResourceProfilesSlice can be initialized:
//
//	es := NewResourceProfilesSlice()
//	es.EnsureCapacity(4)
//	for i := 0; i < 4; i++ {
//	    e := es.AppendEmpty()
//	    // Here should set all the values for e.
//	}
func (es ResourceProfilesSlice) EnsureCapacity(newCap int) {
	es.state.AssertMutable()
	oldCap := cap(*es.orig)
	if newCap <= oldCap {
		return
	}

	newOrig := make([]*otlpprofiles.ResourceProfiles, len(*es.orig), newCap)
	copy(newOrig, *es.orig)
	*es.orig = newOrig
}

// AppendEmpty will append to the end of the slice an empty ResourceProfiles.
// It returns the newly added ResourceProfiles.
func (es ResourceProfilesSlice) AppendEmpty() ResourceProfiles {
	es.state.AssertMutable()
	*es.orig = append(*es.orig, &otlpprofiles.ResourceProfiles{})
	return es.At(es.Len() - 1)
}

// MoveAndAppendTo moves all elements from the current slice and appends them to the dest.
// The current slice will be cleared.
func (es ResourceProfilesSlice) MoveAndAppendTo(dest ResourceProfilesSlice) {
	es.state.AssertMutable()
	dest.state.AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if es.orig == dest.orig {
		return
	}
	if *dest.orig == nil {
		// We can simply move the entire vector and avoid any allocations.
		*dest.orig = *es.orig
	} else {
		*dest.orig = append(*dest.orig, *es.orig...)
	}
	*es.orig = nil
}

// RemoveIf calls f sequentially for each element present in the slice.
// If f returns true, the element is removed from the slice.
func (es ResourceProfilesSlice) RemoveIf(f func(ResourceProfiles) bool) {
	es.state.AssertMutable()
	newLen := 0
	for i := 0; i < len(*es.orig); i++ {
		if f(es.At(i)) {
			continue
		}
		if newLen == i {
			// Nothing to move, element is at the right place.
			newLen++
			continue
		}
		(*es.orig)[newLen] = (*es.orig)[i]
		newLen++
	}
	*es.orig = (*es.orig)[:newLen]
}

// CopyTo copies all elements from the current slice overriding the destination.
func (es ResourceProfilesSlice) CopyTo(dest ResourceProfilesSlice) {
	dest.state.AssertMutable()
	*dest.orig = copyOrigResourceProfilesSlice(*dest.orig, *es.orig)
}

// Equal checks equality with another ResourceProfilesSlice.
// In order to match equality, the order of elements must be the same.
// Optionally accepts CompareOption arguments to customize comparison behavior.
func (es ResourceProfilesSlice) Equal(val ResourceProfilesSlice, opts ...CompareOption) bool {
	if es.Len() != val.Len() {
		return false
	}
	for i := 0; i < es.Len(); i++ {
		if !es.At(i).Equal(val.At(i), opts...) {
			return false
		}
	}
	return true
}

// Sort sorts the ResourceProfiles elements within ResourceProfilesSlice given the
// provided less function so that two instances of ResourceProfilesSlice
// can be compared.
func (es ResourceProfilesSlice) Sort(less func(a, b ResourceProfiles) bool) {
	es.state.AssertMutable()
	sort.SliceStable(*es.orig, func(i, j int) bool { return less(es.At(i), es.At(j)) })
}

func copyOrigResourceProfilesSlice(dest, src []*otlpprofiles.ResourceProfiles) []*otlpprofiles.ResourceProfiles {
	if cap(dest) < len(src) {
		dest = make([]*otlpprofiles.ResourceProfiles, len(src))
		data := make([]otlpprofiles.ResourceProfiles, len(src))
		for i := range src {
			dest[i] = &data[i]
		}
	}
	dest = dest[:len(src)]
	for i := range src {
		copyOrigResourceProfiles(dest[i], src[i])
	}
	return dest
}
