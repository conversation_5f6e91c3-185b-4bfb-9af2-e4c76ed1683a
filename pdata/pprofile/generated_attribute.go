// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pprofile

import (
	"go.opentelemetry.io/collector/pdata/internal"
	v1 "go.opentelemetry.io/collector/pdata/internal/data/protogen/common/v1"
	"go.opentelemetry.io/collector/pdata/pcommon"
)

// Attribute describes an attribute stored in a profile's attribute table.
//
// This is a reference type, if passed by value and callee modifies it the
// caller will see the modification.
//
// Must use NewAttribute function to create new instances.
// Important: zero-initialized instance is not valid for use.
type Attribute struct {
	orig  *v1.KeyValue
	state *internal.State
}

func newAttribute(orig *v1.KeyValue, state *internal.State) Attribute {
	return Attribute{orig: orig, state: state}
}

// NewAttribute creates a new empty Attribute.
//
// This must be used only in testing code. Users should use "AppendEmpty" when part of a Slice,
// OR directly access the member if this is embedded in another struct.
func NewAttribute() Attribute {
	state := internal.StateMutable
	return newAttribute(&v1.KeyValue{}, &state)
}

// MoveTo moves all properties from the current struct overriding the destination and
// resetting the current instance to its zero value
func (ms Attribute) MoveTo(dest Attribute) {
	ms.state.AssertMutable()
	dest.state.AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if ms.orig == dest.orig {
		return
	}
	*dest.orig = *ms.orig
	*ms.orig = v1.KeyValue{}
}

// Key returns the key associated with this Attribute.
func (ms Attribute) Key() string {
	return ms.orig.Key
}

// SetKey replaces the key associated with this Attribute.
func (ms Attribute) SetKey(v string) {
	ms.state.AssertMutable()
	ms.orig.Key = v
}

// Value returns the value associated with this Attribute.
func (ms Attribute) Value() pcommon.Value {
	return pcommon.Value(internal.NewValue(&ms.orig.Value, ms.state))
}

// CopyTo copies all properties from the current struct overriding the destination.
func (ms Attribute) CopyTo(dest Attribute) {
	dest.state.AssertMutable()
	copyOrigAttribute(dest.orig, ms.orig)
}

func copyOrigAttribute(dest, src *v1.KeyValue) {
	dest.Key = src.Key
	internal.CopyOrigValue(&dest.Value, &src.Value)
}

// Equal checks equality with another Attribute.
// Optionally accepts CompareOption arguments to customize comparison behavior.
func (ms Attribute) Equal(val Attribute, opts ...CompareOption) bool {
	cfg := NewCompareConfig(opts)
	return (cfg.ShouldIgnoreField("Key") || ms.Key() == val.Key()) &&
		(cfg.ShouldIgnoreField("Value") || ms.Value().Equal(val.Value()))
}
