// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pprofile

import (
"iter"
"math"
"slices"
"strings"

"go.opentelemetry.io/collector/pdata/internal"
"go.opentelemetry.io/collector/pdata/internal/data"
otlpprofiles "go.opentelemetry.io/collector/pdata/internal/data/protogen/profiles/v1development"
"go.opentelemetry.io/collector/pdata/pcommon"

)

// LineSlice logically represents a slice of Line.
//
// This is a reference type. If passed by value and callee modifies it, the
// caller will see the modification.
//
// Must use NewLineSlice function to create new instances.
// Important: zero-initialized instance is not valid for use.
type LineSlice struct {
	orig *[]*otlpprofiles.Line
	state *internal.State
}

func newLineSlice(orig *[]*otlpprofiles.Line, state *internal.State) LineSlice {
	return LineSlice{orig: orig, state: state}
}

// NewLineSlice creates a LineSlice with 0 elements.
// Can use "EnsureCapacity" to initialize with a given capacity.
func NewLineSlice() LineSlice {
	orig := []*otlpprofiles.Line(nil)
	state := internal.StateMutable
	return newLineSlice(&orig, &state)
}

// Len returns the number of elements in the slice.
//
// Returns "0" for a newly instance created with "NewLineSlice()".
func (es LineSlice) Len() int {
	return len(*es.orig)
}

// At returns the element at the given index.
//
// This function is used mostly for iterating over all the values in the slice:
//   for i := 0; i < es.Len(); i++ {
//       e := es.At(i)
//       ... // Do something with the element
//   }
func (es LineSlice) At(i int) Line {
	return newLine((*es.orig)[i], es.state)
}

// All returns an iterator over index-value pairs in the slice.
//
//	for i, v := range es.All() {
//	    ... // Do something with index-value pair
//	}
func (es LineSlice) All() iter.Seq2[int, Line] {
	return func(yield func(int, Line) bool) {
		for i := 0; i < es.Len(); i++ {
			if !yield(i, es.At(i)) {
				return
			}
		}
	}
}

// EnsureCapacity is an operation that ensures the slice has at least the specified capacity.
// 1. If the newCap <= cap then no change in capacity.
// 2. If the newCap > cap then the slice capacity will be expanded to equal newCap.
//
// Here is how a new LineSlice can be initialized:
//   es := NewLineSlice()
//   es.EnsureCapacity(4)
//   for i := 0; i < 4; i++ {
//       e := es.AppendEmpty()
//       // Here should set all the values for e.
//   }
func (es LineSlice) EnsureCapacity(newCap int) {
	es.state.AssertMutable()
	oldCap := cap(*es.orig)
	if newCap <= oldCap {
		return
	}

	newOrig := make([]*otlpprofiles.Line, len(*es.orig), newCap)
	copy(newOrig, *es.orig)
	*es.orig = newOrig
}

// AppendEmpty will append to the end of the slice an empty Line.
// It returns the newly added Line.
func (es LineSlice) AppendEmpty() Line {
	es.state.AssertMutable()
	*es.orig = append(*es.orig, &otlpprofiles.Line{})
	return es.At(es.Len() - 1)
}

// MoveAndAppendTo moves all elements from the current slice and appends them to the dest.
// The current slice will be cleared.
func (es LineSlice) MoveAndAppendTo(dest LineSlice) {
	es.state.AssertMutable()
	dest.state.AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if es.orig == dest.orig {
		return
	}
	if *dest.orig == nil {
		// We can simply move the entire vector and avoid any allocations.
		*dest.orig = *es.orig
	} else {
		*dest.orig = append(*dest.orig, *es.orig...)
	}
	*es.orig = nil
}

// RemoveIf calls f sequentially for each element present in the slice.
// If f returns true, the element is removed from the slice.
func (es LineSlice) RemoveIf(f func(Line) bool) {
	es.state.AssertMutable()
	newLen := 0
	for i := 0; i < len(*es.orig); i++ {
		if f(es.At(i)) {
			continue
		}
		if newLen == i {
			// Nothing to move, element is at the right place.
			newLen++
			continue
		}
		(*es.orig)[newLen] = (*es.orig)[i]
		newLen++
	}
	*es.orig = (*es.orig)[:newLen]
}


// CopyTo copies all elements from the current slice overriding the destination.
func (es LineSlice) CopyTo(dest LineSlice) {
	dest.state.AssertMutable()
	*dest.orig = copyOrigLineSlice(*dest.orig, *es.orig)
}

// Equal checks equality with another LineSlice.
// In order to match equality, the order of elements must be the same.
func (es LineSlice) Equal(val LineSlice) bool {
	if es.Len() != val.Len() {
		return false
	}
	for i := 0; i < es.Len(); i++ {
		if !es.At(i).Equal(val.At(i)) {
			return false
		}
	}
	return true
	}

// Sort sorts the Line elements within LineSlice given the
// provided less function so that two instances of LineSlice
// can be compared.
func (es LineSlice) Sort(less func(a, b Line) bool) {
	es.state.AssertMutable()
	sort.SliceStable(*es.orig, func(i, j int) bool { return less(es.At(i), es.At(j)) })
}



func copyOrigLineSlice(dest, src []*otlpprofiles.Line) []*otlpprofiles.Line {
	if cap(dest) < len(src) {
		dest = make([]*otlpprofiles.Line, len(src))
		data := make([]otlpprofiles.Line, len(src))
		for i := range src {
			dest[i] = &data[i]
		}
	}
	dest = dest[:len(src)]
	for i := range src {
		copyOrigLine(dest[i], src[i])
	}
	return dest
}
