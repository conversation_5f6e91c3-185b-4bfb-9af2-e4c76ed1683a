// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pprofile

import (
	"testing"
	"unsafe"

	"github.com/stretchr/testify/assert"

	"go.opentelemetry.io/collector/pdata/internal"
	otlpprofiles "go.opentelemetry.io/collector/pdata/internal/data/protogen/profiles/v1development"
	"go.opentelemetry.io/collector/pdata/pcommon"
)

func TestLineSlice(t *testing.T) {
	es := NewLineSlice()
	assert.Equal(t, 0, es.Len())
	state := internal.StateMutable
	es = newLineSlice(&[]*otlpprofiles.Line{}, &state)
	assert.Equal(t, 0, es.Len())

	emptyVal := NewLine()
	testVal := generateTestLine()
	for i := 0; i < 7; i++ {
		el := es.AppendEmpty()
		assert.Equal(t, emptyVal, es.At(i))
		fillTestLine(el)
		assert.Equal(t, testVal, es.At(i))
	}
	assert.Equal(t, 7, es.Len())
}

func TestLineSliceReadOnly(t *testing.T) {
	sharedState := internal.StateReadOnly
	es := newLineSlice(&[]*otlpprofiles.Line{}, &sharedState)
	assert.Equal(t, 0, es.Len())
	assert.Panics(t, func() { es.AppendEmpty() })
	assert.Panics(t, func() { es.EnsureCapacity(2) })
	es2 := NewLineSlice()
	es.CopyTo(es2)
	assert.Panics(t, func() { es2.CopyTo(es) })
	assert.Panics(t, func() { es.MoveAndAppendTo(es2) })
	assert.Panics(t, func() { es2.MoveAndAppendTo(es) })
}

func TestLineSlice_CopyTo(t *testing.T) {
	dest := NewLineSlice()
	// Test CopyTo to empty
	NewLineSlice().CopyTo(dest)
	assert.Equal(t, NewLineSlice(), dest)

	// Test CopyTo larger slice
	generateTestLineSlice().CopyTo(dest)
	assert.Equal(t, generateTestLineSlice(), dest)

	// Test CopyTo same size slice
	generateTestLineSlice().CopyTo(dest)
	assert.Equal(t, generateTestLineSlice(), dest)
}

func TestLineSlice_EnsureCapacity(t *testing.T) {
	es := generateTestLineSlice()

	// Test ensure smaller capacity.
	const ensureSmallLen = 4
	es.EnsureCapacity(ensureSmallLen)
	assert.Less(t, ensureSmallLen, es.Len())
	assert.Equal(t, es.Len(), cap(*es.orig))
	assert.Equal(t, generateTestLineSlice(), es)

	// Test ensure larger capacity
	const ensureLargeLen = 9
	es.EnsureCapacity(ensureLargeLen)
	assert.Less(t, generateTestLineSlice().Len(), ensureLargeLen)
	assert.Equal(t, ensureLargeLen, cap(*es.orig))
	assert.Equal(t, generateTestLineSlice(), es)
}

func TestLineSlice_MoveAndAppendTo(t *testing.T) {
	// Test MoveAndAppendTo to empty
	expectedSlice := generateTestLineSlice()
	dest := NewLineSlice()
	src := generateTestLineSlice()
	src.MoveAndAppendTo(dest)
	assert.Equal(t, generateTestLineSlice(), dest)
	assert.Equal(t, 0, src.Len())
	assert.Equal(t, expectedSlice.Len(), dest.Len())

	// Test MoveAndAppendTo empty slice
	src.MoveAndAppendTo(dest)
	assert.Equal(t, generateTestLineSlice(), dest)
	assert.Equal(t, 0, src.Len())
	assert.Equal(t, expectedSlice.Len(), dest.Len())

	// Test MoveAndAppendTo not empty slice
	generateTestLineSlice().MoveAndAppendTo(dest)
	assert.Equal(t, 2*expectedSlice.Len(), dest.Len())
	for i := 0; i < expectedSlice.Len(); i++ {
		assert.Equal(t, expectedSlice.At(i), dest.At(i))
		assert.Equal(t, expectedSlice.At(i), dest.At(i+expectedSlice.Len()))
	}

	dest.MoveAndAppendTo(dest)
	assert.Equal(t, 2*expectedSlice.Len(), dest.Len())
	for i := 0; i < expectedSlice.Len(); i++ {
		assert.Equal(t, expectedSlice.At(i), dest.At(i))
		assert.Equal(t, expectedSlice.At(i), dest.At(i+expectedSlice.Len()))
	}
}

func TestLineSlice_RemoveIf(t *testing.T) {
	// Test RemoveIf on empty slice
	emptySlice := NewLineSlice()
	emptySlice.RemoveIf(func(el Line) bool {
		t.Fail()
		return false
	})

	// Test RemoveIf
	filtered := generateTestLineSlice()
	pos := 0
	filtered.RemoveIf(func(el Line) bool {
		pos++
		return pos%3 == 0
	})
	assert.Equal(t, 5, filtered.Len())
}

func TestLineSliceAll(t *testing.T) {
	ms := generateTestLineSlice()
	assert.NotEmpty(t, ms.Len())

	var c int
	for i, v := range ms.All() {
		assert.Equal(t, ms.At(i), v, "element should match")
		c++
	}
	assert.Equal(t, ms.Len(), c, "All elements should have been visited")
}

func TestLineSlice_Equal(t *testing.T) {
	es1 := NewLineSlice()
	es2 := NewLineSlice()
	assert.True(t, es1.Equal(es2))

	es1 = generateTestLineSlice()
	es2 = generateTestLineSlice()
	assert.True(t, es1.Equal(es2))

	es2 = NewLineSlice()
	assert.False(t, es1.Equal(es2))

	es2.AppendEmpty()
	assert.False(t, es1.Equal(es2))

	// Test element-wise inequality - create two slices with same length but different elements
	if es1.Len() > 0 {
		es1 = generateTestLineSlice()
		es2 = NewLineSlice()
		// Make es2 same length as es1 but with empty elements
		for i := 0; i < es1.Len(); i++ {
			es2.AppendEmpty()
		}
		// This should return false since elements are different
		assert.False(t, es1.Equal(es2))
	}
}

func TestLineSlice_Sort(t *testing.T) {
	es := generateTestLineSlice()
	es.Sort(func(a, b Line) bool {
		return uintptr(unsafe.Pointer(a.orig)) < uintptr(unsafe.Pointer(b.orig))
	})
	for i := 1; i < es.Len(); i++ {
		assert.Less(t, uintptr(unsafe.Pointer(es.At(i-1).orig)), uintptr(unsafe.Pointer(es.At(i).orig)))
	}
	es.Sort(func(a, b Line) bool {
		return uintptr(unsafe.Pointer(a.orig)) > uintptr(unsafe.Pointer(b.orig))
	})
	for i := 1; i < es.Len(); i++ {
		assert.Greater(t, uintptr(unsafe.Pointer(es.At(i-1).orig)), uintptr(unsafe.Pointer(es.At(i).orig)))
	}
}

func generateTestLineSlice() LineSlice {
	es := NewLineSlice()
	fillTestLineSlice(es)
	return es
}

func fillTestLineSlice(es LineSlice) {
	*es.orig = make([]*otlpprofiles.Line, 7)
	for i := 0; i < 7; i++ {
		(*es.orig)[i] = &otlpprofiles.Line{}
		fillTestLine(newLine((*es.orig)[i], es.state))
	}
}
