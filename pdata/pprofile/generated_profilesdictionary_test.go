// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pprofile

import (
	"testing"
	"unsafe"

	"github.com/stretchr/testify/assert"

	"go.opentelemetry.io/collector/pdata/internal"
	otlpprofiles "go.opentelemetry.io/collector/pdata/internal/data/protogen/profiles/v1development"
	"go.opentelemetry.io/collector/pdata/pcommon"
)

func TestProfilesDictionary_MoveTo(t *testing.T) {
	ms := generateTestProfilesDictionary()
	dest := NewProfilesDictionary()
	ms.MoveTo(dest)
	assert.Equal(t, NewProfilesDictionary(), ms)
	assert.Equal(t, generateTestProfilesDictionary(), dest)
	dest.MoveTo(dest)
	assert.Equal(t, generateTestProfilesDictionary(), dest)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.MoveTo(newProfilesDictionary(&otlpprofiles.ProfilesDictionary{}, &sharedState)) })
	assert.Panics(t, func() { newProfilesDictionary(&otlpprofiles.ProfilesDictionary{}, &sharedState).MoveTo(dest) })
}

func TestProfilesDictionary_CopyTo(t *testing.T) {
	ms := NewProfilesDictionary()
	orig := NewProfilesDictionary()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	orig = generateTestProfilesDictionary()
	orig.CopyTo(ms)
	assert.Equal(t, orig, ms)
	sharedState := internal.StateReadOnly
	assert.Panics(t, func() { ms.CopyTo(newProfilesDictionary(&otlpprofiles.ProfilesDictionary{}, &sharedState)) })
}

func TestProfilesDictionary_Equal(t *testing.T) {
	ms1 := NewProfilesDictionary()
	ms2 := NewProfilesDictionary()
	assert.True(t, ms1.Equal(ms2))

	ms1 = generateTestProfilesDictionary()
	ms2 = generateTestProfilesDictionary()
	assert.True(t, ms1.Equal(ms2))

	ms2 = NewProfilesDictionary()
	assert.False(t, ms1.Equal(ms2))
}

func TestProfilesDictionary_MappingTable(t *testing.T) {
	ms := NewProfilesDictionary()
	assert.Equal(t, NewMappingSlice(), ms.MappingTable())
	fillTestMappingSlice(ms.MappingTable())
	assert.Equal(t, generateTestMappingSlice(), ms.MappingTable())
}

func TestProfilesDictionary_LocationTable(t *testing.T) {
	ms := NewProfilesDictionary()
	assert.Equal(t, NewLocationSlice(), ms.LocationTable())
	fillTestLocationSlice(ms.LocationTable())
	assert.Equal(t, generateTestLocationSlice(), ms.LocationTable())
}

func TestProfilesDictionary_FunctionTable(t *testing.T) {
	ms := NewProfilesDictionary()
	assert.Equal(t, NewFunctionSlice(), ms.FunctionTable())
	fillTestFunctionSlice(ms.FunctionTable())
	assert.Equal(t, generateTestFunctionSlice(), ms.FunctionTable())
}

func TestProfilesDictionary_LinkTable(t *testing.T) {
	ms := NewProfilesDictionary()
	assert.Equal(t, NewLinkSlice(), ms.LinkTable())
	fillTestLinkSlice(ms.LinkTable())
	assert.Equal(t, generateTestLinkSlice(), ms.LinkTable())
}

func TestProfilesDictionary_StringTable(t *testing.T) {
	ms := NewProfilesDictionary()
	assert.Equal(t, pcommon.NewStringSlice(), ms.StringTable())
	internal.FillTestStringSlice(internal.StringSlice(ms.StringTable()))
	assert.Equal(t, pcommon.StringSlice(internal.GenerateTestStringSlice()), ms.StringTable())
}

func TestProfilesDictionary_AttributeTable(t *testing.T) {
	ms := NewProfilesDictionary()
	assert.Equal(t, NewAttributeTableSlice(), ms.AttributeTable())
	fillTestAttributeTableSlice(ms.AttributeTable())
	assert.Equal(t, generateTestAttributeTableSlice(), ms.AttributeTable())
}

func TestProfilesDictionary_AttributeUnits(t *testing.T) {
	ms := NewProfilesDictionary()
	assert.Equal(t, NewAttributeUnitSlice(), ms.AttributeUnits())
	fillTestAttributeUnitSlice(ms.AttributeUnits())
	assert.Equal(t, generateTestAttributeUnitSlice(), ms.AttributeUnits())
}

func generateTestProfilesDictionary() ProfilesDictionary {
	tv := NewProfilesDictionary()
	fillTestProfilesDictionary(tv)
	return tv
}

func fillTestProfilesDictionary(tv ProfilesDictionary) {
	fillTestMappingSlice(newMappingSlice(&tv.orig.MappingTable, tv.state))
	fillTestLocationSlice(newLocationSlice(&tv.orig.LocationTable, tv.state))
	fillTestFunctionSlice(newFunctionSlice(&tv.orig.FunctionTable, tv.state))
	fillTestLinkSlice(newLinkSlice(&tv.orig.LinkTable, tv.state))
	internal.FillTestStringSlice(internal.NewStringSlice(&tv.orig.StringTable, tv.state))
	fillTestAttributeTableSlice(newAttributeTableSlice(&tv.orig.AttributeTable, tv.state))
	fillTestAttributeUnitSlice(newAttributeUnitSlice(&tv.orig.AttributeUnits, tv.state))
}
