// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by "pdata/internal/cmd/pdatagen/main.go". DO NOT EDIT.
// To regenerate this file run "make genpdata".

package pprofile

import (
	"go.opentelemetry.io/collector/pdata/internal"
	otlpprofiles "go.opentelemetry.io/collector/pdata/internal/data/protogen/profiles/v1development"
	"go.opentelemetry.io/collector/pdata/pcommon"
)

// Location describes function and line table debug information.
//
// This is a reference type, if passed by value and callee modifies it the
// caller will see the modification.
//
// Must use NewLocation function to create new instances.
// Important: zero-initialized instance is not valid for use.
type Location struct {
	orig  *otlpprofiles.Location
	state *internal.State
}

func newLocation(orig *otlpprofiles.Location, state *internal.State) Location {
	return Location{orig: orig, state: state}
}

// NewLocation creates a new empty Location.
//
// This must be used only in testing code. Users should use "AppendEmpty" when part of a Slice,
// OR directly access the member if this is embedded in another struct.
func NewLocation() Location {
	state := internal.StateMutable
	return newLocation(&otlpprofiles.Location{}, &state)
}

// MoveTo moves all properties from the current struct overriding the destination and
// resetting the current instance to its zero value
func (ms Location) MoveTo(dest Location) {
	ms.state.AssertMutable()
	dest.state.AssertMutable()
	// If they point to the same data, they are the same, nothing to do.
	if ms.orig == dest.orig {
		return
	}
	*dest.orig = *ms.orig
	*ms.orig = otlpprofiles.Location{}
}

// MappingIndex returns the mappingindex associated with this Location.
func (ms Location) MappingIndex() int32 {
	return ms.orig.GetMappingIndex()
}

// HasMappingIndex returns true if the Location contains a
// MappingIndex value, false otherwise.
func (ms Location) HasMappingIndex() bool {
	return ms.orig.MappingIndex_ != nil
}

// SetMappingIndex replaces the mappingindex associated with this Location.
func (ms Location) SetMappingIndex(v int32) {
	ms.state.AssertMutable()
	ms.orig.MappingIndex_ = &otlpprofiles.Location_MappingIndex{MappingIndex: v}
}

// RemoveMappingIndex removes the mappingindex associated with this Location.
func (ms Location) RemoveMappingIndex() {
	ms.state.AssertMutable()
	ms.orig.MappingIndex_ = nil
}

// Address returns the address associated with this Location.
func (ms Location) Address() uint64 {
	return ms.orig.Address
}

// SetAddress replaces the address associated with this Location.
func (ms Location) SetAddress(v uint64) {
	ms.state.AssertMutable()
	ms.orig.Address = v
}

// Line returns the Line associated with this Location.
func (ms Location) Line() LineSlice {
	return newLineSlice(&ms.orig.Line, ms.state)
}

// IsFolded returns the isfolded associated with this Location.
func (ms Location) IsFolded() bool {
	return ms.orig.IsFolded
}

// SetIsFolded replaces the isfolded associated with this Location.
func (ms Location) SetIsFolded(v bool) {
	ms.state.AssertMutable()
	ms.orig.IsFolded = v
}

// AttributeIndices returns the AttributeIndices associated with this Location.
func (ms Location) AttributeIndices() pcommon.Int32Slice {
	return pcommon.Int32Slice(internal.NewInt32Slice(&ms.orig.AttributeIndices, ms.state))
}

// CopyTo copies all properties from the current struct overriding the destination.
func (ms Location) CopyTo(dest Location) {
	dest.state.AssertMutable()
	copyOrigLocation(dest.orig, ms.orig)
}

func copyOrigLocation(dest, src *otlpprofiles.Location) {
	if src.MappingIndex_ == nil {
		dest.MappingIndex_ = nil
	} else {
		dest.MappingIndex_ = &otlpprofiles.Location_MappingIndex{MappingIndex: src.GetMappingIndex()}
	}
	dest.Address = src.Address
	dest.Line = copyOrigLineSlice(dest.Line, src.Line)
	dest.IsFolded = src.IsFolded
	dest.AttributeIndices = internal.CopyOrigInt32Slice(dest.AttributeIndices, src.AttributeIndices)
}

// Equal checks equality with another Location.
// Optionally accepts CompareOption arguments to customize comparison behavior.
func (ms Location) Equal(val Location, opts ...CompareOption) bool {
	cfg := NewCompareConfig(opts)
	return (ms.HasMappingIndex() == val.HasMappingIndex() && (!ms.HasMappingIndex() || ms.MappingIndex() == val.MappingIndex())) &&
		ms.Address() == val.Address() &&
		ms.Line().Equal(val.Line()) &&
		ms.IsFolded() == val.IsFolded() &&
		ms.AttributeIndices().Equal(val.AttributeIndices())
}
