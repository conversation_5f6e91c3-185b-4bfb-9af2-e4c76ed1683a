// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Package configgrpc defines the  configuration settings to create
// a gRPC client and server.
//
// The configuration structs in this package may be shared across signals, but
// assume each struct is used for a single protocol and component.
package configgrpc // import "go.opentelemetry.io/collector/config/configgrpc"
