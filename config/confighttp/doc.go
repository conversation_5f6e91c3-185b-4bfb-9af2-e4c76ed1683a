// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Package confighttp defines the configuration settings
// for creating an HTTP client and server.
//
// The configuration structs in this package may be shared across signals, but
// assume each struct is used for a single protocol and component.
package confighttp // import "go.opentelemetry.io/collector/config/confighttp"
