// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Package configtelemetry defines various telemetry level for configuration.
// It enables every component to have access to telemetry level
// to enable metrics only when necessary.
//
// This document provides guidance on which telemetry level to adopt for Collector metrics.
// When adopting a telemetry level, component authors are expected to rely on this guidance to
// justify their choice of telemetry level.
//
// 1. configtelemetry.None
//
// No telemetry data is recorded.
//
// 2. configtelemetry.Basic
//
// Telemetry associated with this level provides essential coverage of the Collector telemetry.
// It should only be used for telemetry generated by the core Collector API.
// Components outside of the core API MUST NOT record telemetry at this level.
//
// 3. configtelemetry.Normal
//
// Telemetry associated with this level provides intermediate coverage of the Collector telemetry.
// It should be the default for component authors.
//
// Normal-level telemetry should have limited cardinality and data volume, though it is acceptable
// for them to scale linearly with the monitored resources.
// For example, there may be a limit of 5 attribute sets or 5 spans generated per request.
//
// Normal-level telemetry should also have a low computational cost: it should not contain values
// requiring significant additional computation compared to the normal flow of processing.
//
// This is the default level recommended when running the Collector.
//
// 4. configtelemetry.Detailed
//
// Telemetry associated with this level provides complete coverage of the collector telemetry.
//
// The signals associated with this level may exhibit high cardinality, high data volume, or high
// computational cost.
package configtelemetry // import "go.opentelemetry.io/collector/config/configtelemetry"
