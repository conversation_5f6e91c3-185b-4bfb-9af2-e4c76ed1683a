# Environment Variable Provider

<!-- status autogenerated section -->
| Status        |           |
| ------------- |-----------|
| Stability     | [stable]  |
| Distributions | [core], [contrib], [k8s], [otlp] |
| Issues        | [![Open issues](https://img.shields.io/github/issues-search/open-telemetry/opentelemetry-collector?query=is%3Aissue%20is%3Aopen%20label%3Aprovider%2Fenvprovider%20&label=open&color=orange&logo=opentelemetry)](https://github.com/open-telemetry/opentelemetry-collector/issues?q=is%3Aopen+is%3Aissue+label%3Aprovider%2Fenvprovider) [![Closed issues](https://img.shields.io/github/issues-search/open-telemetry/opentelemetry-collector?query=is%3Aissue%20is%3Aclosed%20label%3Aprovider%2Fenvprovider%20&label=closed&color=blue&logo=opentelemetry)](https://github.com/open-telemetry/opentelemetry-collector/issues?q=is%3Aclosed+is%3Aissue+label%3Aprovider%2Fenvprovider) |

[stable]: https://github.com/open-telemetry/opentelemetry-collector/blob/main/docs/component-stability.md#stable
[core]: https://github.com/open-telemetry/opentelemetry-collector-releases/tree/main/distributions/otelcol
[contrib]: https://github.com/open-telemetry/opentelemetry-collector-releases/tree/main/distributions/otelcol-contrib
[k8s]: https://github.com/open-telemetry/opentelemetry-collector-releases/tree/main/distributions/otelcol-k8s
[otlp]: https://github.com/open-telemetry/opentelemetry-collector-releases/tree/main/distributions/otelcol-otlp
<!-- end autogenerated section -->

## Usage

The scheme for this provider is `env`. Usage looks like the following:

```text
env:NAME_OF_ENVIRONMENT_VARIABLE
```

To use default values when the environment variable has not been set, you can
include a suffix to specify it:

```text
env:NAME_OF_ENVIRONMENT_VARIABLE:-default_value
```

Environment variables must match the following regular expression. That is, they
must be at least one character, start with a letter or underscore, and can only
include letters, numbers, and underscores.

```text
^[a-zA-Z_][a-zA-Z0-9_]*$
```
