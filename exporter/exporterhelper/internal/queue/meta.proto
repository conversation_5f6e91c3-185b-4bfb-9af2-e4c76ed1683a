syntax = "proto3";

package opentelemetry.collector.exporter.exporterhelper.internal.queue;

option go_package = "go.opentelemetry.io/collector/exporter/exporterhelper/internal/queue";

// Sizer type configuration
enum SizerType {
  REQUESTS = 0;
  ITEMS = 1;
  BYTES = 2;
}

// QueueMetadata holds all persistent metadata for the queue.
message QueueMetadata{
  // Sizer type configuration.
  SizerType sizer_type = 1;

  // Current total size of the queue (in bytes, items, or requests).
  sfixed64 queue_size = 2;

  // Index of the next item to be read from the queue.
  fixed64 read_index = 3;

  // Index where the next item will be written to the queue.
  fixed64 write_index = 4;

  // List of item indices currently being processed by consumers.
  repeated fixed64 currently_dispatched_items = 5;
}
