// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: exporter/exporterhelper/internal/queue/meta.proto

package queue

import (
	encoding_binary "encoding/binary"
	fmt "fmt"
	io "io"
	math "math"
	math_bits "math/bits"

	proto "github.com/gogo/protobuf/proto"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

// Sizer type configuration
type SizerType int32

const (
	SizerType_REQUESTS SizerType = 0
	SizerType_ITEMS    SizerType = 1
	SizerType_BYTES    SizerType = 2
)

var SizerType_name = map[int32]string{
	0: "REQUESTS",
	1: "ITEMS",
	2: "BYTES",
}

var SizerType_value = map[string]int32{
	"REQUESTS": 0,
	"ITEMS":    1,
	"BYTES":    2,
}

func (x SizerType) String() string {
	return proto.EnumName(SizerType_name, int32(x))
}

func (SizerType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_8ad1b29fca6d4f37, []int{0}
}

// QueueMetadata holds all persistent metadata for the queue.
type QueueMetadata struct {
	// Sizer type configuration.
	SizerType SizerType `protobuf:"varint,1,opt,name=sizer_type,json=sizerType,proto3,enum=opentelemetry.collector.exporter.exporterhelper.internal.queue.SizerType" json:"sizer_type,omitempty"`
	// Current total size of the queue (in bytes, items, or requests).
	QueueSize int64 `protobuf:"fixed64,2,opt,name=queue_size,json=queueSize,proto3" json:"queue_size,omitempty"`
	// Index of the next item to be read from the queue.
	ReadIndex uint64 `protobuf:"fixed64,3,opt,name=read_index,json=readIndex,proto3" json:"read_index,omitempty"`
	// Index where the next item will be written to the queue.
	WriteIndex uint64 `protobuf:"fixed64,4,opt,name=write_index,json=writeIndex,proto3" json:"write_index,omitempty"`
	// List of item indices currently being processed by consumers.
	CurrentlyDispatchedItems []uint64 `protobuf:"fixed64,5,rep,packed,name=currently_dispatched_items,json=currentlyDispatchedItems,proto3" json:"currently_dispatched_items,omitempty"`
}

func (m *QueueMetadata) Reset()         { *m = QueueMetadata{} }
func (m *QueueMetadata) String() string { return proto.CompactTextString(m) }
func (*QueueMetadata) ProtoMessage()    {}
func (*QueueMetadata) Descriptor() ([]byte, []int) {
	return fileDescriptor_8ad1b29fca6d4f37, []int{0}
}
func (m *QueueMetadata) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *QueueMetadata) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_QueueMetadata.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *QueueMetadata) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueueMetadata.Merge(m, src)
}
func (m *QueueMetadata) XXX_Size() int {
	return m.Size()
}
func (m *QueueMetadata) XXX_DiscardUnknown() {
	xxx_messageInfo_QueueMetadata.DiscardUnknown(m)
}

var xxx_messageInfo_QueueMetadata proto.InternalMessageInfo

func (m *QueueMetadata) GetSizerType() SizerType {
	if m != nil {
		return m.SizerType
	}
	return SizerType_REQUESTS
}

func (m *QueueMetadata) GetQueueSize() int64 {
	if m != nil {
		return m.QueueSize
	}
	return 0
}

func (m *QueueMetadata) GetReadIndex() uint64 {
	if m != nil {
		return m.ReadIndex
	}
	return 0
}

func (m *QueueMetadata) GetWriteIndex() uint64 {
	if m != nil {
		return m.WriteIndex
	}
	return 0
}

func (m *QueueMetadata) GetCurrentlyDispatchedItems() []uint64 {
	if m != nil {
		return m.CurrentlyDispatchedItems
	}
	return nil
}

func init() {
	proto.RegisterEnum("opentelemetry.collector.exporter.exporterhelper.internal.queue.SizerType", SizerType_name, SizerType_value)
	proto.RegisterType((*QueueMetadata)(nil), "opentelemetry.collector.exporter.exporterhelper.internal.queue.QueueMetadata")
}

func init() {
	proto.RegisterFile("exporter/exporterhelper/internal/queue/meta.proto", fileDescriptor_8ad1b29fca6d4f37)
}

var fileDescriptor_8ad1b29fca6d4f37 = []byte{
	// 344 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x91, 0x3f, 0x4f, 0xb3, 0x50,
	0x18, 0xc5, 0xb9, 0xed, 0xdb, 0xe6, 0xe5, 0xbe, 0x7f, 0x42, 0x98, 0x88, 0x89, 0x48, 0x9c, 0x88,
	0xc3, 0x25, 0xea, 0x6a, 0x1c, 0x9a, 0x32, 0x30, 0x74, 0x28, 0xe0, 0xa0, 0x83, 0x04, 0xe1, 0x89,
	0x25, 0xa1, 0xdc, 0xeb, 0xe5, 0x69, 0x2c, 0x7e, 0x0a, 0x27, 0x3f, 0x93, 0x63, 0x47, 0x47, 0xd3,
	0x7e, 0x11, 0x73, 0x51, 0x48, 0x74, 0x32, 0x71, 0x3b, 0x39, 0xe7, 0xe4, 0x77, 0x6f, 0x9e, 0x43,
	0x8f, 0x61, 0x2d, 0xb8, 0x44, 0x90, 0x5e, 0x27, 0x16, 0x50, 0x0a, 0x90, 0x5e, 0x51, 0x21, 0xc8,
	0x2a, 0x2d, 0xbd, 0xbb, 0x15, 0xac, 0xc0, 0x5b, 0x02, 0xa6, 0x4c, 0x48, 0x8e, 0xdc, 0x3c, 0xe7,
	0x02, 0x2a, 0x84, 0x12, 0x96, 0x80, 0xb2, 0x61, 0x19, 0x2f, 0x4b, 0xc8, 0x90, 0x4b, 0xd6, 0x11,
	0xd8, 0x67, 0x14, 0xeb, 0x50, 0xac, 0x45, 0x1d, 0x3e, 0x0d, 0xe8, 0xbf, 0xb9, 0x52, 0x33, 0xc0,
	0x34, 0x4f, 0x31, 0x35, 0x17, 0x94, 0xd6, 0xc5, 0x03, 0xc8, 0x04, 0x1b, 0x01, 0x16, 0x71, 0x88,
	0xfb, 0xff, 0x24, 0x60, 0x3f, 0x7b, 0x86, 0x45, 0x8a, 0x18, 0x37, 0x02, 0x42, 0xbd, 0xee, 0xa4,
	0xb9, 0x4f, 0x69, 0x9b, 0x26, 0xca, 0xb2, 0x06, 0x0e, 0x71, 0x8d, 0x50, 0x6f, 0x1d, 0x55, 0x57,
	0xb1, 0x84, 0x34, 0x4f, 0x8a, 0x2a, 0x87, 0xb5, 0x35, 0x74, 0x88, 0x3b, 0x0e, 0x75, 0xe5, 0x04,
	0xca, 0x30, 0x0f, 0xe8, 0x9f, 0x7b, 0x59, 0x20, 0x7c, 0xe4, 0xbf, 0xda, 0x9c, 0xb6, 0xd6, 0x7b,
	0xe1, 0x8c, 0xee, 0x65, 0x2b, 0x29, 0xa1, 0xc2, 0xb2, 0x49, 0xf2, 0xa2, 0x16, 0x29, 0x66, 0x0b,
	0xc8, 0x93, 0x02, 0x61, 0x59, 0x5b, 0x23, 0x67, 0xe8, 0x8e, 0x43, 0xab, 0x6f, 0x4c, 0xfb, 0x42,
	0xa0, 0xf2, 0x23, 0x8f, 0xea, 0xfd, 0xa7, 0xcd, 0xbf, 0xf4, 0x77, 0xe8, 0xcf, 0x2f, 0xfc, 0x28,
	0x8e, 0x0c, 0xcd, 0xd4, 0xe9, 0x28, 0x88, 0xfd, 0x59, 0x64, 0x10, 0x25, 0x27, 0x97, 0xb1, 0x1f,
	0x19, 0x83, 0xc9, 0xf5, 0xf3, 0xd6, 0x26, 0x9b, 0xad, 0x4d, 0x5e, 0xb7, 0x36, 0x79, 0xdc, 0xd9,
	0xda, 0x66, 0x67, 0x6b, 0x2f, 0x3b, 0x5b, 0xbb, 0x9a, 0xde, 0xf2, 0x2f, 0xf7, 0x2b, 0xb8, 0xd7,
	0x9f, 0xd0, 0xfb, 0xde, 0xe8, 0x37, 0xe3, 0x76, 0xf0, 0xd3, 0xb7, 0x00, 0x00, 0x00, 0xff, 0xff,
	0x50, 0x2d, 0x5e, 0xba, 0x25, 0x02, 0x00, 0x00,
}

func (m *QueueMetadata) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QueueMetadata) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *QueueMetadata) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.CurrentlyDispatchedItems) > 0 {
		for iNdEx := len(m.CurrentlyDispatchedItems) - 1; iNdEx >= 0; iNdEx-- {
			i -= 8
			encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(m.CurrentlyDispatchedItems[iNdEx]))
		}
		i = encodeVarintMeta(dAtA, i, uint64(len(m.CurrentlyDispatchedItems)*8))
		i--
		dAtA[i] = 0x2a
	}
	if m.WriteIndex != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(m.WriteIndex))
		i--
		dAtA[i] = 0x21
	}
	if m.ReadIndex != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(m.ReadIndex))
		i--
		dAtA[i] = 0x19
	}
	if m.QueueSize != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(m.QueueSize))
		i--
		dAtA[i] = 0x11
	}
	if m.SizerType != 0 {
		i = encodeVarintMeta(dAtA, i, uint64(m.SizerType))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func encodeVarintMeta(dAtA []byte, offset int, v uint64) int {
	offset -= sovMeta(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *QueueMetadata) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.SizerType != 0 {
		n += 1 + sovMeta(uint64(m.SizerType))
	}
	if m.QueueSize != 0 {
		n += 9
	}
	if m.ReadIndex != 0 {
		n += 9
	}
	if m.WriteIndex != 0 {
		n += 9
	}
	if len(m.CurrentlyDispatchedItems) > 0 {
		n += 1 + sovMeta(uint64(len(m.CurrentlyDispatchedItems)*8)) + len(m.CurrentlyDispatchedItems)*8
	}
	return n
}

func sovMeta(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozMeta(x uint64) (n int) {
	return sovMeta(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *QueueMetadata) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMeta
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: QueueMetadata: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: QueueMetadata: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SizerType", wireType)
			}
			m.SizerType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMeta
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SizerType |= SizerType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field QueueSize", wireType)
			}
			m.QueueSize = 0
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			m.QueueSize = int64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
		case 3:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReadIndex", wireType)
			}
			m.ReadIndex = 0
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			m.ReadIndex = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
		case 4:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field WriteIndex", wireType)
			}
			m.WriteIndex = 0
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			m.WriteIndex = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
		case 5:
			if wireType == 1 {
				var v uint64
				if (iNdEx + 8) > l {
					return io.ErrUnexpectedEOF
				}
				v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
				iNdEx += 8
				m.CurrentlyDispatchedItems = append(m.CurrentlyDispatchedItems, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMeta
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthMeta
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthMeta
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				elementCount = packedLen / 8
				if elementCount != 0 && len(m.CurrentlyDispatchedItems) == 0 {
					m.CurrentlyDispatchedItems = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					if (iNdEx + 8) > l {
						return io.ErrUnexpectedEOF
					}
					v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
					iNdEx += 8
					m.CurrentlyDispatchedItems = append(m.CurrentlyDispatchedItems, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field CurrentlyDispatchedItems", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMeta(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthMeta
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipMeta(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowMeta
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowMeta
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowMeta
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthMeta
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupMeta
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthMeta
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthMeta        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowMeta          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupMeta = fmt.Errorf("proto: unexpected end of group")
)
