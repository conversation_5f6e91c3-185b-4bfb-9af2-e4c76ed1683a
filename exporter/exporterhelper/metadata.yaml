type: exporterhelper
github_project: open-telemetry/opentelemetry-collector

status:
  disable_codecov_badge: true
  codeowners:
    active:
      - BogdanDrutu
      - dmitryax
  class: pkg
  stability:
    beta: [traces, metrics, logs]

telemetry:
  metrics:
    exporter_sent_spans:
      enabled: true
      stability:
        level: alpha
      description: Number of spans successfully sent to destination.
      unit: "{spans}"
      sum:
        value_type: int
        monotonic: true

    exporter_send_failed_spans:
      enabled: true
      stability:
        level: alpha
      description: Number of spans in failed attempts to send to destination.
      unit: "{spans}"
      sum:
        value_type: int
        monotonic: true

    exporter_enqueue_failed_spans:
      enabled: true
      stability:
        level: alpha
      description: Number of spans failed to be added to the sending queue.
      unit: "{spans}"
      sum:
        value_type: int
        monotonic: true

    exporter_sent_metric_points:
      enabled: true
      stability:
        level: alpha
      description: Number of metric points successfully sent to destination.
      unit: "{datapoints}"
      sum:
        value_type: int
        monotonic: true

    exporter_send_failed_metric_points:
      enabled: true
      stability:
        level: alpha
      description: Number of metric points in failed attempts to send to destination.
      unit: "{datapoints}"
      sum:
        value_type: int
        monotonic: true

    exporter_enqueue_failed_metric_points:
      enabled: true
      stability:
        level: alpha
      description: Number of metric points failed to be added to the sending queue.
      unit: "{datapoints}"
      sum:
        value_type: int
        monotonic: true

    exporter_sent_log_records:
      enabled: true
      stability:
        level: alpha
      description: Number of log record successfully sent to destination.
      unit: "{records}"
      sum:
        value_type: int
        monotonic: true

    exporter_send_failed_log_records:
      enabled: true
      stability:
        level: alpha
      description: Number of log records in failed attempts to send to destination.
      unit: "{records}"
      sum:
        value_type: int
        monotonic: true

    exporter_enqueue_failed_log_records:
      enabled: true
      stability:
        level: alpha
      description: Number of log records failed to be added to the sending queue.
      unit: "{records}"
      sum:
        value_type: int
        monotonic: true

    exporter_queue_size:
      enabled: true
      stability:
        level: alpha
      description: Current size of the retry queue (in batches)
      unit: "{batches}"
      gauge:
        value_type: int
        async: true

    exporter_queue_capacity:
      enabled: true
      stability:
        level: alpha
      description: Fixed capacity of the retry queue (in batches)
      unit: "{batches}"
      gauge:
        value_type: int
        async: true
