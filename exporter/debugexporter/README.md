# Debug Exporter

<!-- status autogenerated section -->
| Status        |           |
| ------------- |-----------|
| Stability     | [development]: traces, metrics, logs, profiles   |
| Distributions | [core], [contrib], [k8s] |
| Warnings      | [Unstable Output Format](#warnings) |
| Issues        | [![Open issues](https://img.shields.io/github/issues-search/open-telemetry/opentelemetry-collector?query=is%3Aissue%20is%3Aopen%20label%3Aexporter%2Fdebug%20&label=open&color=orange&logo=opentelemetry)](https://github.com/open-telemetry/opentelemetry-collector/issues?q=is%3Aopen+is%3Aissue+label%3Aexporter%2Fdebug) [![Closed issues](https://img.shields.io/github/issues-search/open-telemetry/opentelemetry-collector?query=is%3Aissue%20is%3Aclosed%20label%3Aexporter%2Fdebug%20&label=closed&color=blue&logo=opentelemetry)](https://github.com/open-telemetry/opentelemetry-collector/issues?q=is%3Aclosed+is%3Aissue+label%3Aexporter%2Fdebug) |
| [Code Owners](https://github.com/open-telemetry/opentelemetry-collector-contrib/blob/main/CONTRIBUTING.md#becoming-a-code-owner)    | [@andrzej-stencel](https://www.github.com/andrzej-stencel) |

[development]: https://github.com/open-telemetry/opentelemetry-collector/blob/main/docs/component-stability.md#development
[core]: https://github.com/open-telemetry/opentelemetry-collector-releases/tree/main/distributions/otelcol
[contrib]: https://github.com/open-telemetry/opentelemetry-collector-releases/tree/main/distributions/otelcol-contrib
[k8s]: https://github.com/open-telemetry/opentelemetry-collector-releases/tree/main/distributions/otelcol-k8s
<!-- end autogenerated section -->

Outputs telemetry data to the console for debugging purposes.

See also the [Troubleshooting][troubleshooting_docs] document for examples on using this exporter.

[troubleshooting_docs]: https://opentelemetry.io/docs/collector/troubleshooting/#local-exporters

## Getting Started

The following settings are optional:

- `verbosity` (default = `basic`): the verbosity of the debug exporter: `basic`, `normal` or `detailed`.
  See [Verbosity levels](#verbosity-levels) below for more information.
- `sampling_initial` (default = `2`): number of messages initially logged each
  second.
- `sampling_thereafter` (default = `1`): sampling rate after the initial
  messages are logged (every Mth message is logged).
  The default value of `1` means that sampling is disabled.
  To enable sampling, change `sampling_thereafter` to a value higher than `1`.
  Refer to [Zap docs](https://godoc.org/go.uber.org/zap/zapcore#NewSampler) for more details
  on how sampling parameters impact number of messages.
- `use_internal_logger` (default = `true`): uses the collector's internal logger for output. See [below](#using-the-collectors-internal-logger) for description.

Example configuration:

```yaml
exporters:
  debug:
    verbosity: detailed
    sampling_initial: 5
    sampling_thereafter: 200
```

## Verbosity levels

The following subsections describe the output from the exporter depending on the configured verbosity level - `basic`, `normal` and `detailed`.
The default verbosity level is `basic`.

To understand how the below example output was generated, see [Generating example output](./generating-example-output.md).

### Basic verbosity

With `verbosity: basic`, the exporter outputs a single-line summary of received data with a total count of telemetry records for every batch of received logs, metrics or traces.

Here's an example output:

```console
2025-04-17T10:40:44.559+0200    info    Traces  {"otelcol.component.id": "debug/basic", "otelcol.component.kind": "Exporter", "otelcol.signal": "traces", "resource spans": 1, "spans": 2}
```

### Normal verbosity

With `verbosity: normal`, the exporter outputs about one line for each telemetry record.
The "one line per telemetry record" is not a strict rule.
For example, logs with multiline body will be output as multiple lines.

Here's an example output:

```console
2025-05-09T19:57:16.332+0200    info    Traces  {"resource": {}, "otelcol.component.id": "debug/normal", "otelcol.component.kind": "exporter", "otelcol.signal": "traces", "resource spans": 1, "spans": 2}
2025-05-09T19:57:16.332+0200    info    ResourceTraces #0 [https://opentelemetry.io/schemas/1.25.0] service.name=telemetrygen
ScopeTraces #0 telemetrygen
okey-dokey-0 ab1030bd4ee554af936542b01d7b4807 1d8c93663d043aa8 net.sock.peer.addr=******* peer.service=telemetrygen-client
lets-go ab1030bd4ee554af936542b01d7b4807 0d238e8a2f97733f net.sock.peer.addr=******* peer.service=telemetrygen-server
        {"resource": {}, "otelcol.component.id": "debug/normal", "otelcol.component.kind": "exporter", "otelcol.signal": "traces"}
```

### Detailed verbosity

With `verbosity: detailed`, the exporter outputs all details of every telemetry record, typically writing multiple lines for every telemetry record.

Here's an example output:

```console
2025-04-17T10:40:44.560+0200    info    Traces  {"otelcol.component.id": "debug/detailed", "otelcol.component.kind": "Exporter", "otelcol.signal": "traces", "resource spans": 1, "spans": 2}
2025-04-17T10:40:44.560+0200    info    ResourceSpans #0
Resource SchemaURL: https://opentelemetry.io/schemas/1.25.0
Resource attributes:
     -> service.name: Str(telemetrygen)
ScopeSpans #0
ScopeSpans SchemaURL: 
InstrumentationScope telemetrygen 
Span #0
    Trace ID       : fafdac970271dd2ce89de2442c0518c7
    Parent ID      : d98de4cb8e2a0ad6
    ID             : 3875f436d989d0e5
    Name           : okey-dokey-0
    Kind           : Server
    Start time     : 2025-04-17 08:40:44.555461596 +0000 UTC
    End time       : 2025-04-17 08:40:44.555584596 +0000 UTC
    Status code    : Unset
    Status message : 
Attributes:
     -> net.sock.peer.addr: Str(*******)
     -> peer.service: Str(telemetrygen-client)
Span #1
    Trace ID       : fafdac970271dd2ce89de2442c0518c7
    Parent ID      : 
    ID             : d98de4cb8e2a0ad6
    Name           : lets-go
    Kind           : Client
    Start time     : 2025-04-17 08:40:44.555461596 +0000 UTC
    End time       : 2025-04-17 08:40:44.555584596 +0000 UTC
    Status code    : Unset
    Status message : 
Attributes:
     -> net.sock.peer.addr: Str(*******)
     -> peer.service: Str(telemetrygen-server)
        {"otelcol.component.id": "debug/detailed", "otelcol.component.kind": "Exporter", "otelcol.signal": "traces"}
```

## Using the collector's internal logger

When `use_internal_logger` is set to `true` (the default), the exporter uses the collector's [internal logger][internal_telemetry] for output.
This comes with the following consequences:

- The output from the exporter may be annotated by additional output from the collector's logger.
- The output from the exporter is affected by the collector's [logging configuration][internal_logs_config] specified in `service::telemetry::logs`.

When `use_internal_logger` is set to `false`, the exporter does not use the collector's internal logger.
Changing the values in `service::telemetry::logs` has no effect on the exporter's output.
The exporter's output is sent to `stdout`.

[internal_telemetry]: https://opentelemetry.io/docs/collector/internal-telemetry/
[internal_logs_config]: https://opentelemetry.io/docs/collector/internal-telemetry/#configure-internal-logs

## Warnings

- Unstable Output Format: The output formats for all verbosity levels is not guaranteed and may be changed at any time without a breaking change.
