Mapping #0
    Memory start: 2
    Memory limit: 3
    File offset: 4
    File name: 5
    Attributes: [7 8]
    Has functions: true
    Has filenames: true
    Has line numbers: true
    Has inline frames: true
Location #0
    Mapping index: 3
    Address: 4
    Line #0
        Function index: 1
        Line: 2
        Column: 3
    Is folded: true
    Attributes: [6 7]
Function #0
    Name: 2
    System name: 3
    Filename: 4
    Start line: 5
Attribute units:
     -> attributeKey: Int(1)
     -> unit: Int(5)
Link table:
     -> Trace ID: Str(0302030405060708090a0b0c0d0e0f10)
     -> Span ID: Str(1112131415161718)
String table:
    foobar
ResourceProfiles #0
Resource SchemaURL: 
Resource attributes:
     -> resource-attr: Str(resource-attr-val-1)
ScopeProfiles #0
ScopeProfiles SchemaURL: 
InstrumentationScope  
Profile #0
    Profile ID     : 0102030405060708090a0b0c0d0e0f10
    Start time     : 2020-02-11 20:26:12.000000321 +0000 UTC
    Duration       : 2020-02-11 20:26:13.000000789 +0000 UTC
    Dropped attributes count: 1
    Location indices: [1]
    Sample #0
        Location length: 10
        Value: [4]
        Attributes:
             -> key: value
Profile #1
    Profile ID     : 0202030405060708090a0b0c0d0e0f10
    Start time     : 2020-02-11 20:26:12.000000321 +0000 UTC
    Duration       : 2020-02-11 20:26:13.000000789 +0000 UTC
    Dropped attributes count: 0
    Location indices: []
    Sample #0
        Location length: 20
        Value: [9]
        Attributes:
             -> key: value
    Comment:
        1
        2
