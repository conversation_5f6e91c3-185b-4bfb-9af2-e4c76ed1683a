// Code generated by mdatagen. DO NOT EDIT.

package metadata

import (
	"go.opentelemetry.io/collector/component"
)

var (
	Type      = component.MustNewType("otlphttp")
	ScopeName = "go.opentelemetry.io/collector/exporter/otlphttpexporter"
)

const (
	ProfilesStability = component.StabilityLevelDevelopment
	TracesStability   = component.StabilityLevelStable
	MetricsStability  = component.StabilityLevelStable
	LogsStability     = component.StabilityLevelStable
)
