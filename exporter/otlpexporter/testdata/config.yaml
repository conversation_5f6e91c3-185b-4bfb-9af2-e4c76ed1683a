endpoint: "*******:1234"
compression: "gzip"
tls:
  ca_file: /var/lib/mycert.pem
timeout: 10s
sending_queue:
  enabled: true
  sizer: "items"
  num_consumers: 2
  queue_size: 100000
  batch:
    flush_timeout: 200ms
    min_size: 1000
    max_size: 10000
retry_on_failure:
  enabled: true
  initial_interval: 10s
  randomization_factor: 0.7
  multiplier: 1.3
  max_interval: 60s
  max_elapsed_time: 10m
auth:
  authenticator: nop
headers:
  "can you have a . here?": "F0000000-0000-0000-0000-000000000000"
  header1: "234"
  another: "somevalue"
keepalive:
  time: 20s
  timeout: 30s
  permit_without_stream: true
balancer_name: "round_robin"
