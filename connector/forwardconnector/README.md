# Forward Connector

<!-- status autogenerated section -->
| Status        |           |
| ------------- |-----------|
| Distributions | [core], [contrib], [k8s] |
| Issues        | [![Open issues](https://img.shields.io/github/issues-search/open-telemetry/opentelemetry-collector?query=is%3Aissue%20is%3Aopen%20label%3Aconnector%2Fforward%20&label=open&color=orange&logo=opentelemetry)](https://github.com/open-telemetry/opentelemetry-collector/issues?q=is%3Aopen+is%3Aissue+label%3Aconnector%2Fforward) [![Closed issues](https://img.shields.io/github/issues-search/open-telemetry/opentelemetry-collector?query=is%3Aissue%20is%3Aclosed%20label%3Aconnector%2Fforward%20&label=closed&color=blue&logo=opentelemetry)](https://github.com/open-telemetry/opentelemetry-collector/issues?q=is%3Aclosed+is%3Aissue+label%3Aconnector%2Fforward) |

[beta]: https://github.com/open-telemetry/opentelemetry-collector/blob/main/docs/component-stability.md#beta
[core]: https://github.com/open-telemetry/opentelemetry-collector-releases/tree/main/distributions/otelcol
[contrib]: https://github.com/open-telemetry/opentelemetry-collector-releases/tree/main/distributions/otelcol-contrib
[k8s]: https://github.com/open-telemetry/opentelemetry-collector-releases/tree/main/distributions/otelcol-k8s

## Supported Pipeline Types

| [Exporter Pipeline Type] | [Receiver Pipeline Type] | [Stability Level] |
| ------------------------ | ------------------------ | ----------------- |
| traces | traces | [beta] |
| metrics | metrics | [beta] |
| logs | logs | [beta] |

[Exporter Pipeline Type]: https://github.com/open-telemetry/opentelemetry-collector/blob/main/connector/README.md#exporter-pipeline-type
[Receiver Pipeline Type]: https://github.com/open-telemetry/opentelemetry-collector/blob/main/connector/README.md#receiver-pipeline-type
[Stability Level]: https://github.com/open-telemetry/opentelemetry-collector/blob/main/docs/component-stability.md#stability-levels
<!-- end autogenerated section -->

The `forward` connector can merge or fork pipelines of the same type.

## Configuration

If you are not already familiar with connectors, you may find it helpful to first visit the [Connectors README].

The `forward` connector does not have any configuration settings.

```yaml
receivers:
  foo:
exporters:
  bar:
connectors:
  forward:
```

### Example Usage

Annotate distinct log streams, then merge them together, batch, and export.

```yaml
receivers:
  foo/blue:
  foo/green:
processors:
  attributes/blue:
  attributes/green:
  batch:
exporters:
  bar:
connectors:
  forward:
service:
  pipelines:
    logs/blue:
      receivers: [foo/blue]
      processors: [attributes/blue]
      exporters: [forward]
    logs/green:
      receivers: [foo/green]
      processors: [attributes/green]
      exporters: [forward]
    logs:
      receivers: [forward]
      processors: [batch]
      exporters: [bar]
```

Preprocess data, then replicate and handle in distinct ways.

```yaml
receivers:
  foo:
processors:
  resourcedetection:
  sample:
  attributes:
  batch:
exporters:
  bar/hot:
  bar/cold:
connectors:
  forward:
service:
  pipelines:
    traces:
      receivers: [foo]
      processors: [resourcedetection]
      exporters: [forward]
    traces/hot:
      receivers: [forward]
      processors: [sample, batch]
      exporters: [bar/hot]
    traces/cold:
      receivers: [forward]
      processors: [attributes]
      exporters: [bar/cold]
```

Add a temporary debugging exporter. (Uncomment to enable.)

```yaml
receivers:
  foo:
processors:
  filter:
  batch:
exporters:
  bar:
# connectors:
#   forward:
service:
  pipelines:
    traces:
      receivers:
        - foo
      processors:
        - filter
        - batch
      exporters:
        - bar
      # - forward
  # traces/log:
  #   receivers: [forward]
  #   exporters: [debug]
```

[Connectors README]:../README.md
